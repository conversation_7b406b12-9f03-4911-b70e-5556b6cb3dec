[2025-07-18T10:41:19.856+0000] {processor.py:186} INFO - Started process (PID=4112) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:41:19.857+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:41:19.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:19.859+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:41:20.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:20.072+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:20.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:20.074+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:20.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:20.074+0000] {cost_tracking.py:123} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:41:20.075+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:41:20.090+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.240 seconds
[2025-07-18T10:41:50.345+0000] {processor.py:186} INFO - Started process (PID=4248) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:41:50.346+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:41:50.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:50.348+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:41:50.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:50.529+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:50.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:50.530+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:50.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:50.531+0000] {cost_tracking.py:123} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:41:50.531+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:41:50.532+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:41:50.546+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.206 seconds
[2025-07-18T10:42:20.633+0000] {processor.py:186} INFO - Started process (PID=4384) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:42:20.634+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:42:20.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:20.636+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:42:20.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:20.845+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:20.846+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:20.846+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:20.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:20.846+0000] {cost_tracking.py:123} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:42:20.847+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:42:20.848+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:42:20.863+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.236 seconds
[2025-07-18T10:42:56.071+0000] {processor.py:186} INFO - Started process (PID=182) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:42:56.072+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:42:56.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:56.074+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:42:56.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:56.570+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:56.571+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:56.571+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:56.572+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:56.571+0000] {cost_tracking.py:123} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:42:56.572+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:42:56.573+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:42:56.584+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.519 seconds
[2025-07-18T10:43:26.682+0000] {processor.py:186} INFO - Started process (PID=318) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:43:26.682+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:43:26.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:26.684+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:43:27.023+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:27.023+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:27.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:27.024+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:27.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:27.024+0000] {cost_tracking.py:123} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:43:27.025+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:43:27.025+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:43:27.036+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.359 seconds
[2025-07-18T10:43:57.455+0000] {processor.py:186} INFO - Started process (PID=454) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:43:57.456+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:43:57.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:57.459+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:43:57.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:57.811+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:57.812+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:57.812+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:57.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:57.813+0000] {cost_tracking.py:123} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:43:57.813+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:43:57.814+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:43:57.823+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.374 seconds
[2025-07-18T10:44:28.481+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:44:28.482+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:44:28.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:28.485+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:44:28.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:28.693+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:28.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:28.694+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:28.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:28.695+0000] {cost_tracking.py:123} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:44:28.695+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:44:28.696+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:44:28.715+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.240 seconds
[2025-07-18T10:44:59.476+0000] {processor.py:186} INFO - Started process (PID=732) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:44:59.477+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:44:59.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:59.479+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:44:59.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:59.684+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:59.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:59.685+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:59.686+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:59.686+0000] {cost_tracking.py:123} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:44:59.686+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:44:59.687+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:44:59.704+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.234 seconds
[2025-07-18T10:45:29.775+0000] {processor.py:186} INFO - Started process (PID=868) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:45:29.776+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:45:29.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:29.778+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:45:29.953+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:29.953+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:29.954+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:29.954+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:29.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:29.955+0000] {cost_tracking.py:123} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:45:29.955+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:45:29.956+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:45:29.969+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.200 seconds
[2025-07-18T10:46:00.058+0000] {processor.py:186} INFO - Started process (PID=1004) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:46:00.059+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:46:00.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:00.061+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:46:00.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:00.263+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:00.264+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:00.264+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:00.265+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:00.264+0000] {cost_tracking.py:123} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:46:00.265+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:46:00.266+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:46:00.279+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.227 seconds
[2025-07-18T10:46:30.764+0000] {processor.py:186} INFO - Started process (PID=1140) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:46:30.765+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:46:30.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:30.769+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:46:30.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:30.983+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:30.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:30.985+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:30.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:30.985+0000] {cost_tracking.py:123} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:46:30.986+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:46:30.986+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:46:31.002+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.244 seconds
[2025-07-18T10:47:01.180+0000] {processor.py:186} INFO - Started process (PID=1276) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:47:01.181+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:47:01.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:01.184+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:47:01.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:01.386+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:01.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:01.387+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:01.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:01.387+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:47:01.388+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:47:01.388+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:47:01.400+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.226 seconds
[2025-07-18T10:48:01.387+0000] {processor.py:186} INFO - Started process (PID=182) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:48:01.388+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:48:01.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:01.390+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:48:01.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:01.956+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:01.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:01.957+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:01.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:01.958+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:48:01.959+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:48:01.960+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:48:01.972+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.590 seconds
[2025-07-18T10:48:32.267+0000] {processor.py:186} INFO - Started process (PID=324) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:48:32.268+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:48:32.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:32.270+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:48:32.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:32.612+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:32.614+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:32.613+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:32.614+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:32.614+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:48:32.615+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:48:32.615+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:48:32.627+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.366 seconds
[2025-07-18T10:49:03.491+0000] {processor.py:186} INFO - Started process (PID=460) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:49:03.492+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:49:03.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:03.496+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:49:03.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:03.875+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:03.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:03.875+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:03.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:03.876+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:49:03.876+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:49:03.877+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:49:03.887+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.404 seconds
[2025-07-18T10:49:33.973+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:49:33.974+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:49:33.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:33.976+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:49:34.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:34.189+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:34.190+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:34.190+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:34.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:34.191+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:49:34.192+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:49:34.192+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:49:34.206+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.238 seconds
[2025-07-18T10:50:04.403+0000] {processor.py:186} INFO - Started process (PID=732) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:50:04.404+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:50:04.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:04.406+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:50:04.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:04.611+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:04.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:04.612+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:04.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:04.612+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:50:04.613+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:50:04.614+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:50:04.628+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.231 seconds
[2025-07-18T10:50:35.276+0000] {processor.py:186} INFO - Started process (PID=868) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:50:35.277+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:50:35.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:35.279+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:50:35.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:35.485+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:35.486+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:35.486+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:35.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:35.486+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:50:35.487+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:50:35.488+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:50:35.505+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.235 seconds
[2025-07-18T10:51:06.053+0000] {processor.py:186} INFO - Started process (PID=1004) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:51:06.054+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:51:06.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:06.056+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:51:06.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:06.249+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:06.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:06.250+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:06.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:06.251+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:51:06.252+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:51:06.253+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:51:06.271+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.224 seconds
[2025-07-18T10:51:36.533+0000] {processor.py:186} INFO - Started process (PID=1141) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:51:36.534+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:51:36.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:36.536+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:51:36.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:36.736+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:36.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:36.737+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:36.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:36.737+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:51:36.738+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:51:36.738+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:51:36.755+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.228 seconds
[2025-07-18T10:52:06.943+0000] {processor.py:186} INFO - Started process (PID=1277) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:52:06.944+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:52:06.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:06.946+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:52:07.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:07.163+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:07.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:07.164+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:07.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:07.165+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:52:07.166+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:52:07.166+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:52:07.187+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.250 seconds
[2025-07-18T10:52:37.425+0000] {processor.py:186} INFO - Started process (PID=1413) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:52:37.426+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:52:37.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:37.428+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:52:37.630+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:37.630+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:37.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:37.631+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:37.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:37.631+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:52:37.631+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:52:37.632+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:52:37.647+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.228 seconds
[2025-07-18T10:53:08.152+0000] {processor.py:186} INFO - Started process (PID=1549) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:53:08.153+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:53:08.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:08.155+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:53:08.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:08.357+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:08.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:08.359+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:08.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:08.359+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:53:08.360+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:53:08.361+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:53:08.376+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.230 seconds
[2025-07-18T10:53:38.722+0000] {processor.py:186} INFO - Started process (PID=1685) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:53:38.723+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:53:38.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:38.725+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:53:38.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:38.904+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:38.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:38.906+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:38.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:38.906+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:53:38.907+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:53:38.907+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:53:38.922+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.205 seconds
[2025-07-18T10:54:09.375+0000] {processor.py:186} INFO - Started process (PID=1821) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:54:09.376+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:54:09.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:09.379+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:54:09.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:09.564+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:09.565+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:09.565+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:09.566+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:09.566+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:54:09.567+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:54:09.567+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:54:09.581+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.211 seconds
[2025-07-18T10:54:39.756+0000] {processor.py:186} INFO - Started process (PID=1957) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:54:39.757+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:54:39.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:39.759+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:54:39.954+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:39.953+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:39.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:39.955+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:39.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:39.955+0000] {cost_tracking.py:124} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:54:39.956+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:54:39.957+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:54:39.971+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.221 seconds
[2025-07-18T10:55:10.779+0000] {processor.py:186} INFO - Started process (PID=2093) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:55:10.780+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:55:10.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:10.783+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:55:10.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:10.979+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:10.981+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:10.980+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:10.981+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:10.981+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:55:10.982+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:55:10.982+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:55:10.994+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.219 seconds
[2025-07-18T10:55:41.631+0000] {processor.py:186} INFO - Started process (PID=2229) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:55:41.631+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:55:41.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:41.634+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:55:41.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:41.858+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:41.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:41.859+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:41.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:41.860+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:55:41.860+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:55:41.861+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:55:41.875+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.250 seconds
[2025-07-18T10:57:26.187+0000] {processor.py:186} INFO - Started process (PID=182) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:57:26.189+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:57:26.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:26.191+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:57:26.723+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:26.723+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:26.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:26.724+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:26.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:26.724+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:57:26.725+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:57:26.725+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:57:26.737+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.556 seconds
[2025-07-18T10:57:56.813+0000] {processor.py:186} INFO - Started process (PID=318) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:57:56.814+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:57:56.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:56.815+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:57:57.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:57.163+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:57.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:57.164+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:57.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:57.165+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:57:57.165+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:57:57.166+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:57:57.176+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.368 seconds
[2025-07-18T10:58:27.378+0000] {processor.py:186} INFO - Started process (PID=454) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:58:27.379+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:58:27.382+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:27.381+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:58:27.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:27.700+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:27.701+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:27.701+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:27.701+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:27.701+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:58:27.702+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:58:27.702+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:58:27.712+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.339 seconds
[2025-07-18T10:58:58.104+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:58:58.105+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:58:58.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:58.107+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:58:58.317+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:58.317+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:58.318+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:58.318+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:58.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:58.319+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:58:58.319+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:58:58.320+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:58:58.333+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.236 seconds
[2025-07-18T10:59:28.384+0000] {processor.py:186} INFO - Started process (PID=732) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:59:28.385+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:59:28.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:28.387+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:59:28.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:28.579+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:28.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:28.580+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:28.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:28.580+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:59:28.581+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:59:28.582+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:59:28.596+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.217 seconds
[2025-07-18T10:59:58.909+0000] {processor.py:186} INFO - Started process (PID=868) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:59:58.909+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T10:59:58.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:58.912+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:59:59.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:59.108+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:59.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:59.109+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:59.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:59.110+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:59:59.110+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T10:59:59.111+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T10:59:59.124+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.221 seconds
[2025-07-18T11:00:29.256+0000] {processor.py:186} INFO - Started process (PID=1004) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:00:29.257+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:00:29.260+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:29.259+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:00:29.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:29.441+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:29.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:29.442+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:29.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:29.443+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:00:29.444+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:00:29.444+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:00:29.457+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.207 seconds
[2025-07-18T11:00:59.941+0000] {processor.py:186} INFO - Started process (PID=1140) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:00:59.943+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:00:59.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:59.946+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:01:00.160+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:00.159+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:00.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:00.161+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:00.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:00.161+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:01:00.162+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:01:00.162+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:01:00.178+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.242 seconds
[2025-07-18T11:01:30.608+0000] {processor.py:186} INFO - Started process (PID=1276) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:01:30.609+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:01:30.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:30.612+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:01:30.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:30.800+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:30.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:30.802+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:30.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:30.802+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:01:30.803+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:01:30.804+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:01:30.818+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.216 seconds
[2025-07-18T11:02:01.729+0000] {processor.py:186} INFO - Started process (PID=1412) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:02:01.730+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:02:01.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:01.733+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:02:01.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:01.935+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:01.936+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:01.936+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:01.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:01.937+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:02:01.937+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:02:01.938+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:02:01.953+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.229 seconds
[2025-07-18T11:02:32.709+0000] {processor.py:186} INFO - Started process (PID=1548) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:02:32.710+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:02:32.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:32.711+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:02:32.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:32.921+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:32.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:32.923+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:32.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:32.924+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:02:32.924+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:02:32.925+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:02:32.940+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.239 seconds
[2025-07-18T11:03:03.197+0000] {processor.py:186} INFO - Started process (PID=1684) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:03:03.198+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:03:03.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:03.201+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:03:03.413+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:03.413+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:03.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:03.414+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:03.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:03.415+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:03:03.415+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:03:03.416+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:03:03.433+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.243 seconds
[2025-07-18T11:03:33.870+0000] {processor.py:186} INFO - Started process (PID=1820) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:03:33.871+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:03:33.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:33.874+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:03:34.080+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:34.080+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:34.081+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:34.081+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:34.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:34.082+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:03:34.083+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:03:34.083+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:03:34.100+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.235 seconds
[2025-07-18T11:04:04.273+0000] {processor.py:186} INFO - Started process (PID=1956) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:04:04.274+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:04:04.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:04.277+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:04:04.490+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:04.490+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:04.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:04.491+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:04.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:04.492+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:04:04.492+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:04:04.493+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:04:04.511+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.245 seconds
[2025-07-18T11:04:34.989+0000] {processor.py:186} INFO - Started process (PID=2092) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:04:34.990+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:04:34.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:34.994+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:04:35.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:35.205+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:35.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:35.205+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:35.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:35.206+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:04:35.207+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:04:35.207+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:04:35.220+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.238 seconds
[2025-07-18T11:05:05.535+0000] {processor.py:186} INFO - Started process (PID=2228) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:05:05.536+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:05:05.539+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:05.539+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:05:05.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:05.782+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:05.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:05.783+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:05.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:05.784+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:05:05.786+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:05:05.786+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:05:05.801+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.273 seconds
[2025-07-18T11:05:36.225+0000] {processor.py:186} INFO - Started process (PID=2364) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:05:36.226+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:05:36.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:36.231+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:05:36.490+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:36.490+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:36.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:36.491+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:36.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:36.491+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:05:36.492+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:05:36.493+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:05:36.511+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.295 seconds
[2025-07-18T11:06:48.385+0000] {processor.py:186} INFO - Started process (PID=192) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:06:48.386+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:06:48.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:48.387+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:06:48.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:48.873+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:48.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:48.874+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:48.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:48.875+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:06:48.875+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:06:48.876+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:06:48.886+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.506 seconds
[2025-07-18T11:07:19.557+0000] {processor.py:186} INFO - Started process (PID=333) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:07:19.558+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:07:19.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:19.561+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:07:19.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:19.988+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:19.989+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:19.989+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:19.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:19.990+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:07:19.990+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:07:19.990+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:07:20.000+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.451 seconds
[2025-07-18T11:07:50.133+0000] {processor.py:186} INFO - Started process (PID=474) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:07:50.134+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:07:50.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:50.136+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:07:50.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:50.502+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:50.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:50.503+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:50.504+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:50.504+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:07:50.504+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:07:50.504+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:07:50.514+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.389 seconds
[2025-07-18T11:08:20.596+0000] {processor.py:186} INFO - Started process (PID=615) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:08:20.597+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:08:20.600+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:20.600+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:08:20.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:20.796+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:20.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:20.797+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:20.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:20.798+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:08:20.798+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:08:20.799+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:08:20.810+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.221 seconds
[2025-07-18T11:08:51.497+0000] {processor.py:186} INFO - Started process (PID=756) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:08:51.497+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:08:51.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:51.500+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:08:51.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:51.708+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:51.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:51.709+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:51.710+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:51.710+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:08:51.710+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:08:51.711+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:08:51.721+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.230 seconds
[2025-07-18T11:09:21.962+0000] {processor.py:186} INFO - Started process (PID=899) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:09:21.963+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:09:21.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:21.965+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:09:22.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:22.157+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:22.158+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:22.158+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:22.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:22.159+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:09:22.160+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:09:22.160+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:09:22.174+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.217 seconds
[2025-07-18T11:09:52.610+0000] {processor.py:186} INFO - Started process (PID=1038) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:09:52.611+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:09:52.614+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:52.614+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:09:52.833+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:52.833+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:52.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:52.834+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:52.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:52.835+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:09:52.835+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:09:52.836+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:09:52.847+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.244 seconds
[2025-07-18T11:10:23.553+0000] {processor.py:186} INFO - Started process (PID=1185) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:10:23.554+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:10:23.556+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:23.556+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:10:23.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:23.796+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:23.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:23.798+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:23.799+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:23.798+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:10:23.799+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:10:23.799+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:10:23.815+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.268 seconds
[2025-07-18T11:10:54.570+0000] {processor.py:186} INFO - Started process (PID=1326) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:10:54.571+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:10:54.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:54.573+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:10:54.776+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:54.776+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:54.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:54.777+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:54.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:54.777+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:10:54.778+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:10:54.778+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:10:54.788+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.224 seconds
[2025-07-18T11:11:25.085+0000] {processor.py:186} INFO - Started process (PID=1469) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:11:25.086+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:11:25.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:25.088+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:11:25.286+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:25.286+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:25.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:25.288+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:25.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:25.288+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:11:25.289+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:11:25.289+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:11:25.305+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.226 seconds
[2025-07-18T11:11:55.425+0000] {processor.py:186} INFO - Started process (PID=1610) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:11:55.426+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:11:55.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:55.427+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:11:55.614+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:55.614+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:55.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:55.615+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:55.616+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:55.616+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:11:55.616+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:11:55.617+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:11:55.631+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.212 seconds
[2025-07-18T11:12:25.731+0000] {processor.py:186} INFO - Started process (PID=1751) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:12:25.732+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:12:25.735+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:25.734+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:12:25.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:25.918+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:25.919+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:25.919+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:25.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:25.920+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:12:25.920+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:12:25.921+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:12:25.935+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.211 seconds
[2025-07-18T11:12:56.081+0000] {processor.py:186} INFO - Started process (PID=1890) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:12:56.082+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:12:56.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:56.084+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:12:56.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:56.291+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:56.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:56.292+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:56.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:56.292+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:12:56.293+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:12:56.293+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:12:56.306+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.233 seconds
[2025-07-18T11:13:26.995+0000] {processor.py:186} INFO - Started process (PID=2031) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:13:26.996+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:13:26.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:26.998+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:13:27.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:27.194+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:27.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:27.195+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:27.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:27.195+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:13:27.196+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:13:27.196+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:13:27.209+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.219 seconds
[2025-07-18T11:13:57.597+0000] {processor.py:186} INFO - Started process (PID=2172) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:13:57.598+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:13:57.601+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:57.601+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:13:57.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:57.829+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:57.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:57.830+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:57.831+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:57.831+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:13:57.831+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:13:57.832+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:13:57.843+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.251 seconds
[2025-07-18T11:14:28.231+0000] {processor.py:186} INFO - Started process (PID=2313) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:14:28.232+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:14:28.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:28.234+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:14:28.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:28.445+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:28.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:28.446+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:28.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:28.447+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:14:28.447+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:14:28.448+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:14:28.459+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.234 seconds
[2025-07-18T11:14:59.423+0000] {processor.py:186} INFO - Started process (PID=2454) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:14:59.424+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:14:59.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:59.426+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:14:59.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:59.648+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:59.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:59.649+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:59.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:59.650+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:14:59.651+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:14:59.651+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:14:59.665+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.248 seconds
[2025-07-18T11:15:30.061+0000] {processor.py:186} INFO - Started process (PID=2595) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:15:30.062+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:15:30.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:30.066+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:15:30.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:30.324+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:30.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:30.325+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:30.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:30.325+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:15:30.326+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:15:30.326+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:15:30.341+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.289 seconds
[2025-07-18T11:16:00.729+0000] {processor.py:186} INFO - Started process (PID=2736) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:16:00.731+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:16:00.735+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:00.734+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:16:00.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:00.960+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:00.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:00.961+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:00.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:00.962+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:16:00.963+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:16:00.963+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:16:00.974+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.253 seconds
[2025-07-18T11:16:31.526+0000] {processor.py:186} INFO - Started process (PID=2877) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:16:31.527+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:16:31.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:31.529+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:16:31.764+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:31.764+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:31.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:31.766+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:31.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:31.767+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:16:31.768+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:16:31.768+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:16:31.783+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.263 seconds
[2025-07-18T11:17:02.250+0000] {processor.py:186} INFO - Started process (PID=3018) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:17:02.251+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:17:02.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:02.254+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:17:02.605+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:02.605+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:02.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:02.608+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:02.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:02.609+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:17:02.610+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:17:02.610+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:17:02.626+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.383 seconds
[2025-07-18T11:17:32.894+0000] {processor.py:186} INFO - Started process (PID=3159) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:17:32.895+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:17:32.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:32.898+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:17:33.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:33.120+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:33.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:33.121+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:33.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:33.121+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:17:33.122+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:17:33.122+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:17:33.135+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.247 seconds
[2025-07-18T11:18:03.512+0000] {processor.py:186} INFO - Started process (PID=3300) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:18:03.513+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:18:03.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:03.515+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:18:03.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:03.750+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:03.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:03.751+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:03.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:03.752+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:18:03.753+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:18:03.754+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:18:03.767+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.261 seconds
[2025-07-18T11:18:34.449+0000] {processor.py:186} INFO - Started process (PID=3441) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:18:34.450+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:18:34.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:34.453+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:18:34.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:34.720+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:34.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:34.721+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:34.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:34.722+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:18:34.722+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:18:34.723+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:18:34.736+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.293 seconds
[2025-07-18T11:19:04.893+0000] {processor.py:186} INFO - Started process (PID=3582) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:19:04.894+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:19:04.896+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:04.896+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:19:05.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:05.112+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:05.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:05.113+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:05.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:05.113+0000] {cost_tracking.py:129} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:19:05.114+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:19:05.115+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:19:05.130+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.244 seconds
[2025-07-18T11:19:35.638+0000] {processor.py:186} INFO - Started process (PID=3730) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:19:35.639+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:19:35.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:35.641+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:19:35.846+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:35.846+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:35.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:35.847+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:35.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:35.847+0000] {cost_tracking.py:123} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:19:35.848+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:19:35.849+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:19:35.862+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.231 seconds
[2025-07-18T11:20:06.099+0000] {processor.py:186} INFO - Started process (PID=3871) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:20:06.101+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:20:06.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:06.103+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:20:06.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:06.310+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:06.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:06.311+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:06.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:06.312+0000] {cost_tracking.py:123} ERROR - Ошибка записи в Google Sheets: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:20:06.312+0000] {logging_mixin.py:190} INFO - Ошибка: [Errno 2] No such file or directory: '/opt/airflow/src/secrets/service_account.json'
[2025-07-18T11:20:06.313+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:20:06.326+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.233 seconds
[2025-07-18T11:20:27.407+0000] {processor.py:186} INFO - Started process (PID=4015) to work on /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:20:27.409+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/test_gsheets.py for tasks to queue
[2025-07-18T11:20:27.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:27.413+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:20:27.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:27.547+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:27.550+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:27.549+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:27.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:27.550+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/test_gsheets.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/test_gsheets.py", line 17, in <module>
    tracker.save_cost_to_gsheets(test_data, creds_path="/opt/airflow/src/secrets/service_account.json")
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'CostTracker' object has no attribute 'save_cost_to_gsheets'
[2025-07-18T11:20:27.563+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/test_gsheets.py
[2025-07-18T11:20:27.589+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/test_gsheets.py took 0.190 seconds
