[2025-07-18T10:16:50.989+0000] {processor.py:186} INFO - Started process (PID=296) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:16:50.990+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:16:50.993+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.992+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:16:51.006+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:51.005+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:16:51.007+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:16:51.029+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.046 seconds
[2025-07-18T10:17:21.203+0000] {processor.py:186} INFO - Started process (PID=429) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:17:21.204+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:17:21.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:21.206+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:17:21.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:21.220+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:17:21.221+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:17:21.240+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T10:17:52.178+0000] {processor.py:186} INFO - Started process (PID=558) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:17:52.179+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:17:52.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:52.180+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:17:52.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:52.192+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:17:52.194+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:17:52.212+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T10:18:23.039+0000] {processor.py:186} INFO - Started process (PID=689) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:18:23.040+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:18:23.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:23.041+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:18:23.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:23.056+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:18:23.059+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:18:23.076+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-18T10:18:53.999+0000] {processor.py:186} INFO - Started process (PID=820) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:18:53.999+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:18:54.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:54.000+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:18:54.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:54.010+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:18:54.012+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:18:54.031+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.037 seconds
[2025-07-18T10:19:25.006+0000] {processor.py:186} INFO - Started process (PID=953) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:19:25.007+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:19:25.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:25.008+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:19:25.021+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:25.019+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:19:25.021+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:19:25.039+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.038 seconds
[2025-07-18T10:19:56.031+0000] {processor.py:186} INFO - Started process (PID=1084) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:19:56.032+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:19:56.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:56.033+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:19:56.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:56.044+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:19:56.045+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:19:56.062+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.036 seconds
[2025-07-18T10:20:27.019+0000] {processor.py:186} INFO - Started process (PID=1215) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:20:27.020+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:20:27.022+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:27.021+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:20:27.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:27.033+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:20:27.034+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:20:27.052+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.039 seconds
[2025-07-18T10:20:57.964+0000] {processor.py:186} INFO - Started process (PID=1346) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:20:57.965+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:20:57.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.966+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:20:57.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.975+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:20:57.977+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:20:57.994+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.035 seconds
[2025-07-18T10:21:28.981+0000] {processor.py:186} INFO - Started process (PID=1477) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:21:28.982+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:21:28.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.983+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:21:28.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.993+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:21:28.995+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:21:29.011+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.035 seconds
[2025-07-18T10:21:59.139+0000] {processor.py:186} INFO - Started process (PID=1608) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:21:59.140+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:21:59.141+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:59.141+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:21:59.152+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:59.151+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:21:59.153+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:21:59.169+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.036 seconds
[2025-07-18T10:22:30.156+0000] {processor.py:186} INFO - Started process (PID=1739) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:22:30.157+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:22:30.158+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:30.158+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:22:30.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:30.168+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:22:30.170+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:22:30.187+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.037 seconds
[2025-07-18T10:23:01.130+0000] {processor.py:186} INFO - Started process (PID=1870) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:23:01.131+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:23:01.132+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:01.132+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:23:01.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:01.143+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:23:01.145+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:23:01.163+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T10:23:31.962+0000] {processor.py:186} INFO - Started process (PID=2001) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:23:31.963+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:23:31.964+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.964+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:23:31.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.973+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:23:31.975+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:23:31.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.036 seconds
[2025-07-18T10:24:02.185+0000] {processor.py:186} INFO - Started process (PID=2130) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:24:02.185+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:24:02.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:02.186+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:24:02.199+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:02.198+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:24:02.200+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:24:02.217+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.038 seconds
[2025-07-18T10:24:33.143+0000] {processor.py:186} INFO - Started process (PID=2261) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:24:33.144+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:24:33.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:33.145+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:24:33.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:33.158+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:24:33.160+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:24:33.176+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T10:25:06.811+0000] {processor.py:186} INFO - Started process (PID=2392) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:25:06.812+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:25:06.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.813+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:25:06.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.823+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:25:06.825+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:25:06.840+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.034 seconds
[2025-07-18T10:26:25.372+0000] {processor.py:186} INFO - Started process (PID=296) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:26:25.372+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:26:25.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.374+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:26:25.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.387+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:26:25.389+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:26:25.407+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T10:26:57.178+0000] {processor.py:186} INFO - Started process (PID=422) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:26:57.179+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:26:57.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.181+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:26:57.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.192+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:26:57.194+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:26:57.212+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T10:27:28.432+0000] {processor.py:186} INFO - Started process (PID=555) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:27:28.433+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:27:28.436+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.436+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:27:28.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.448+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:27:28.450+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:27:28.468+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T10:27:59.687+0000] {processor.py:186} INFO - Started process (PID=684) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:27:59.688+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:27:59.690+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.690+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:27:59.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.702+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:27:59.703+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:27:59.724+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-18T10:28:30.170+0000] {processor.py:186} INFO - Started process (PID=815) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:28:30.171+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:28:30.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.174+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:28:30.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.187+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:28:30.189+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:28:30.212+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.047 seconds
[2025-07-18T10:29:00.955+0000] {processor.py:186} INFO - Started process (PID=946) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:29:00.956+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:29:00.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.959+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:29:00.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.971+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:29:00.973+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:29:00.994+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.046 seconds
[2025-07-18T10:29:31.602+0000] {processor.py:186} INFO - Started process (PID=1079) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:29:31.603+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:29:31.605+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.605+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:29:31.617+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.616+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:29:31.618+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:29:31.635+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.039 seconds
[2025-07-18T10:30:02.367+0000] {processor.py:186} INFO - Started process (PID=1210) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:30:02.368+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:30:02.371+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.370+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:30:02.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.383+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:30:02.384+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:30:02.404+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T10:30:32.811+0000] {processor.py:186} INFO - Started process (PID=1339) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:30:32.812+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:30:32.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.814+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:30:32.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.825+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:30:32.827+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:30:32.843+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.038 seconds
[2025-07-18T10:31:03.450+0000] {processor.py:186} INFO - Started process (PID=1470) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:31:03.451+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:31:03.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.454+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:31:03.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.470+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:31:03.472+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:31:03.493+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.049 seconds
[2025-07-18T10:31:33.870+0000] {processor.py:186} INFO - Started process (PID=1596) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:31:33.871+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:31:33.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.873+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:31:33.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.883+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:31:33.884+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:31:33.901+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.036 seconds
[2025-07-18T10:32:04.671+0000] {processor.py:186} INFO - Started process (PID=1727) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:32:04.672+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:32:04.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.675+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:32:04.692+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.690+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:32:04.693+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:32:04.718+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.056 seconds
[2025-07-18T10:32:35.343+0000] {processor.py:186} INFO - Started process (PID=1860) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:32:35.344+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:32:35.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.346+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:32:35.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.357+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:32:35.358+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:32:35.374+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.037 seconds
[2025-07-18T10:33:05.809+0000] {processor.py:186} INFO - Started process (PID=1994) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:33:05.810+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:33:05.812+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.812+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:33:05.823+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.822+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:33:05.824+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:33:05.842+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.039 seconds
[2025-07-18T10:33:36.430+0000] {processor.py:186} INFO - Started process (PID=2122) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:33:36.431+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:33:36.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.433+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:33:36.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.447+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:33:36.449+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:33:36.470+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.045 seconds
[2025-07-18T10:34:07.085+0000] {processor.py:186} INFO - Started process (PID=2253) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:34:07.086+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:34:07.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.088+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:34:07.101+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.100+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:34:07.102+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:34:07.119+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T10:34:38.033+0000] {processor.py:186} INFO - Started process (PID=2382) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:34:38.034+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:34:38.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.035+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:34:38.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.045+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:34:38.047+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:34:38.064+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.036 seconds
[2025-07-18T10:35:08.585+0000] {processor.py:186} INFO - Started process (PID=2513) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:35:08.586+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:35:08.588+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.587+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:35:08.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.598+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:35:08.600+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:35:08.618+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T10:35:39.368+0000] {processor.py:186} INFO - Started process (PID=2644) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:35:39.369+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:35:39.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.371+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:35:39.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.383+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:35:39.385+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:35:39.405+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T10:36:10.528+0000] {processor.py:186} INFO - Started process (PID=2780) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:36:10.529+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:36:10.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.531+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:36:10.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.545+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:36:10.550+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:36:10.580+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.059 seconds
[2025-07-18T10:36:41.054+0000] {processor.py:186} INFO - Started process (PID=2906) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:36:41.055+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:36:41.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:41.057+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:36:41.071+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:41.069+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:36:41.071+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:36:41.089+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T10:37:11.697+0000] {processor.py:186} INFO - Started process (PID=3039) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:37:11.698+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:37:11.702+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.701+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:37:11.717+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.715+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:37:11.718+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:37:11.739+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.049 seconds
[2025-07-18T10:37:43.130+0000] {processor.py:186} INFO - Started process (PID=3173) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:37:43.130+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:37:43.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.132+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:37:43.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.143+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:37:43.145+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:37:43.161+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.038 seconds
[2025-07-18T10:38:13.779+0000] {processor.py:186} INFO - Started process (PID=3304) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:38:13.780+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:38:13.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.782+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:38:13.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.794+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:38:13.796+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:38:13.816+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T10:38:44.192+0000] {processor.py:186} INFO - Started process (PID=3435) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:38:44.193+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:38:44.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.195+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:38:44.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.205+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:38:44.207+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:38:44.223+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.036 seconds
[2025-07-18T10:39:15.513+0000] {processor.py:186} INFO - Started process (PID=3571) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:39:15.514+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:39:15.517+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.517+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:39:15.534+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.533+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:39:15.535+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:39:15.558+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.052 seconds
[2025-07-18T10:39:46.062+0000] {processor.py:186} INFO - Started process (PID=3704) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:39:46.065+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:39:46.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:46.069+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:39:46.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:46.088+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:39:46.091+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:39:46.115+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.063 seconds
[2025-07-18T10:40:17.006+0000] {processor.py:186} INFO - Started process (PID=3835) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:40:17.007+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:40:17.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:17.009+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:40:17.022+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:17.021+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:40:17.023+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:40:17.042+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T10:40:47.885+0000] {processor.py:186} INFO - Started process (PID=3966) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:40:47.886+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:40:47.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.888+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:40:47.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.901+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:40:47.903+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:40:47.921+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T10:41:18.390+0000] {processor.py:186} INFO - Started process (PID=4095) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:41:18.392+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:41:18.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:18.394+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:41:18.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:18.409+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:41:18.411+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:41:18.430+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.047 seconds
[2025-07-18T10:41:49.107+0000] {processor.py:186} INFO - Started process (PID=4231) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:41:49.108+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:41:49.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:49.110+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:41:49.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:49.124+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:41:49.126+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:41:49.144+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.042 seconds
[2025-07-18T10:42:20.015+0000] {processor.py:186} INFO - Started process (PID=4367) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:42:20.016+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:42:20.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:20.018+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:42:20.031+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:20.030+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:42:20.032+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:42:20.049+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T10:43:01.832+0000] {processor.py:186} INFO - Started process (PID=301) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:43:01.833+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:43:01.836+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.835+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:43:01.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.846+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:43:01.848+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:43:01.867+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T10:43:34.439+0000] {processor.py:186} INFO - Started process (PID=437) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:43:34.440+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:43:34.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:34.442+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:43:34.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:34.454+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:43:34.455+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:43:34.473+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.039 seconds
[2025-07-18T10:44:05.515+0000] {processor.py:186} INFO - Started process (PID=575) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:44:05.516+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:44:05.518+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.518+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:44:05.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.529+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:44:05.530+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:44:05.547+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.038 seconds
[2025-07-18T10:44:36.795+0000] {processor.py:186} INFO - Started process (PID=711) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:44:36.796+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:44:36.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:36.798+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:44:36.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:36.809+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:44:36.810+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:44:36.827+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.037 seconds
[2025-07-18T10:45:07.922+0000] {processor.py:186} INFO - Started process (PID=847) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:45:07.923+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:45:07.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:07.925+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:45:07.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:07.943+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:45:07.945+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:45:07.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.051 seconds
[2025-07-18T10:45:38.372+0000] {processor.py:186} INFO - Started process (PID=983) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:45:38.373+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:45:38.375+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:38.375+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:45:38.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:38.388+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:45:38.390+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:45:38.409+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T10:46:09.115+0000] {processor.py:186} INFO - Started process (PID=1119) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:46:09.116+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:46:09.118+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:09.118+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:46:09.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:09.129+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:46:09.131+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:46:09.148+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.039 seconds
[2025-07-18T10:46:40.491+0000] {processor.py:186} INFO - Started process (PID=1255) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:46:40.492+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:46:40.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.494+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:46:40.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.511+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:46:40.513+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:46:40.534+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.050 seconds
[2025-07-18T10:48:07.201+0000] {processor.py:186} INFO - Started process (PID=301) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:48:07.202+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:48:07.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.204+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:48:07.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.217+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:48:07.219+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:48:07.240+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.045 seconds
[2025-07-18T10:48:38.165+0000] {processor.py:186} INFO - Started process (PID=432) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:48:38.167+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:48:38.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.169+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:48:38.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.190+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:48:38.192+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:48:38.223+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.068 seconds
[2025-07-18T10:49:09.264+0000] {processor.py:186} INFO - Started process (PID=563) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:49:09.265+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:49:09.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:09.268+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:49:09.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:09.287+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:49:09.289+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:49:09.465+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.207 seconds
[2025-07-18T10:49:39.999+0000] {processor.py:186} INFO - Started process (PID=699) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:49:40.001+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:49:40.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.003+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:49:40.018+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.017+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:49:40.019+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:49:40.041+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.048 seconds
[2025-07-18T10:50:10.694+0000] {processor.py:186} INFO - Started process (PID=832) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:50:10.695+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:50:10.698+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.697+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:50:10.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.711+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:50:10.714+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:50:10.731+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T10:50:41.488+0000] {processor.py:186} INFO - Started process (PID=968) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:50:41.489+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:50:41.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.491+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:50:41.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.504+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:50:41.506+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:50:41.527+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.045 seconds
[2025-07-18T10:51:12.195+0000] {processor.py:186} INFO - Started process (PID=1104) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:51:12.196+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:51:12.199+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.199+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:51:12.211+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.210+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:51:12.212+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:51:12.230+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.042 seconds
[2025-07-18T10:51:42.548+0000] {processor.py:186} INFO - Started process (PID=1239) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:51:42.549+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:51:42.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.552+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:51:42.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.563+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:51:42.564+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:51:42.582+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T10:52:13.013+0000] {processor.py:186} INFO - Started process (PID=1375) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:52:13.014+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:52:13.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:13.016+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:52:13.029+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:13.028+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:52:13.030+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:52:13.050+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T10:52:43.749+0000] {processor.py:186} INFO - Started process (PID=1513) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:52:43.750+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:52:43.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.751+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:52:43.764+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.763+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:52:43.765+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:52:43.783+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T10:53:14.291+0000] {processor.py:186} INFO - Started process (PID=1649) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:53:14.292+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:53:14.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:14.295+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:53:14.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:14.305+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:53:14.307+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:53:14.326+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T10:53:45.001+0000] {processor.py:186} INFO - Started process (PID=1783) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:53:45.002+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:53:45.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:45.004+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:53:45.015+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:45.013+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:53:45.015+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:53:45.033+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.037 seconds
[2025-07-18T10:54:16.263+0000] {processor.py:186} INFO - Started process (PID=1919) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:54:16.264+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:54:16.266+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:16.266+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:54:16.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:16.278+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:54:16.280+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:54:16.300+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.045 seconds
[2025-07-18T10:54:46.737+0000] {processor.py:186} INFO - Started process (PID=2055) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:54:46.739+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:54:46.743+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:46.743+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:54:46.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:46.759+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:54:46.762+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:54:46.792+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.063 seconds
[2025-07-18T10:55:17.469+0000] {processor.py:186} INFO - Started process (PID=2191) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:55:17.470+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:55:17.473+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:17.473+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:55:17.488+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:17.487+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:55:17.489+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:55:17.512+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.050 seconds
[2025-07-18T10:57:31.941+0000] {processor.py:186} INFO - Started process (PID=301) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:57:31.942+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:57:31.948+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.948+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:57:31.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.961+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:57:31.963+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:57:31.982+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.047 seconds
[2025-07-18T10:58:03.378+0000] {processor.py:186} INFO - Started process (PID=437) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:58:03.379+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:58:03.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.381+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:58:03.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.393+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:58:03.395+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:58:03.413+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T10:58:35.209+0000] {processor.py:186} INFO - Started process (PID=573) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:58:35.210+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:58:35.212+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:35.211+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:58:35.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:35.224+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:58:35.226+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:58:35.243+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T10:59:05.680+0000] {processor.py:186} INFO - Started process (PID=711) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:59:05.681+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:59:05.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.683+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:59:05.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.694+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:59:05.695+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:59:05.713+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.039 seconds
[2025-07-18T10:59:36.203+0000] {processor.py:186} INFO - Started process (PID=847) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:59:36.204+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T10:59:36.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:36.205+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:59:36.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:36.217+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T10:59:36.219+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T10:59:36.237+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T11:00:06.612+0000] {processor.py:186} INFO - Started process (PID=983) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:00:06.613+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:00:06.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.615+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:00:06.626+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.625+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:00:06.626+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:00:06.645+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.038 seconds
[2025-07-18T11:00:37.266+0000] {processor.py:186} INFO - Started process (PID=1117) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:00:37.267+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:00:37.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:37.269+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:00:37.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:37.282+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:00:37.284+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:00:37.302+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.042 seconds
[2025-07-18T11:01:07.884+0000] {processor.py:186} INFO - Started process (PID=1253) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:01:07.885+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:01:07.888+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.888+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:01:07.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.899+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:01:07.900+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:01:07.920+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.042 seconds
[2025-07-18T11:01:38.806+0000] {processor.py:186} INFO - Started process (PID=1384) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:01:38.807+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:01:38.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.809+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:01:38.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.820+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:01:38.822+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:01:38.839+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.038 seconds
[2025-07-18T11:02:10.915+0000] {processor.py:186} INFO - Started process (PID=1527) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:02:10.916+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:02:10.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.918+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:02:10.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.927+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:02:10.929+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:02:10.945+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.036 seconds
[2025-07-18T11:02:41.610+0000] {processor.py:186} INFO - Started process (PID=1663) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:02:41.611+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:02:41.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.614+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:02:41.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.627+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:02:41.628+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:02:41.649+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-18T11:03:12.277+0000] {processor.py:186} INFO - Started process (PID=1799) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:03:12.279+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:03:12.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:12.281+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:03:12.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:12.291+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:03:12.294+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:03:12.314+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.042 seconds
[2025-07-18T11:03:43.324+0000] {processor.py:186} INFO - Started process (PID=1935) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:03:43.325+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:03:43.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:43.328+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:03:43.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:43.339+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:03:43.341+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:03:43.362+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.045 seconds
[2025-07-18T11:04:14.345+0000] {processor.py:186} INFO - Started process (PID=2071) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:04:14.346+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:04:14.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:14.348+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:04:14.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:14.359+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:04:14.361+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:04:14.380+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T11:04:44.455+0000] {processor.py:186} INFO - Started process (PID=2205) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:04:44.456+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:04:44.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.458+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:04:44.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.469+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:04:44.471+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:04:44.491+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.042 seconds
[2025-07-18T11:05:15.235+0000] {processor.py:186} INFO - Started process (PID=2336) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:05:15.236+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:05:15.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.239+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:05:15.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.266+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:05:15.268+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:05:15.290+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.063 seconds
[2025-07-18T11:06:54.713+0000] {processor.py:186} INFO - Started process (PID=313) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:06:54.714+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:06:54.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.716+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:06:54.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.726+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:06:54.728+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:06:54.748+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T11:07:26.143+0000] {processor.py:186} INFO - Started process (PID=454) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:07:26.143+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:07:26.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:26.146+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:07:26.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:26.160+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:07:26.162+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:07:26.185+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.048 seconds
[2025-07-18T11:07:56.886+0000] {processor.py:186} INFO - Started process (PID=595) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:07:56.887+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:07:56.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.889+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:07:56.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.901+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:07:56.903+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:07:56.920+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T11:08:27.724+0000] {processor.py:186} INFO - Started process (PID=736) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:08:27.725+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:08:27.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.727+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:08:27.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.738+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:08:27.740+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:08:27.758+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T11:08:58.781+0000] {processor.py:186} INFO - Started process (PID=877) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:08:58.782+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:08:58.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.784+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:08:58.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.797+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:08:58.799+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:08:58.819+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.045 seconds
[2025-07-18T11:09:29.633+0000] {processor.py:186} INFO - Started process (PID=1018) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:09:29.635+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:09:29.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.637+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:09:29.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.653+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:09:29.655+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:09:29.678+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.052 seconds
[2025-07-18T11:10:00.745+0000] {processor.py:186} INFO - Started process (PID=1159) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:10:00.746+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:10:00.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.748+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:10:00.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.760+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:10:00.762+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:10:00.782+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T11:10:31.435+0000] {processor.py:186} INFO - Started process (PID=1295) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:10:31.436+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:10:31.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:31.441+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:10:31.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:31.457+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:10:31.460+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:10:31.483+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.057 seconds
[2025-07-18T11:11:02.029+0000] {processor.py:186} INFO - Started process (PID=1441) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:11:02.030+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:11:02.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:02.032+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:11:02.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:02.042+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:11:02.044+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:11:02.061+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.038 seconds
[2025-07-18T11:11:32.802+0000] {processor.py:186} INFO - Started process (PID=1579) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:11:32.803+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:11:32.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.805+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:11:32.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.816+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:11:32.817+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:11:32.837+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T11:12:03.581+0000] {processor.py:186} INFO - Started process (PID=1720) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:12:03.582+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:12:03.584+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.584+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:12:03.595+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.594+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:12:03.595+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:12:03.613+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.037 seconds
[2025-07-18T11:12:34.101+0000] {processor.py:186} INFO - Started process (PID=1864) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:12:34.102+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:12:34.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:34.104+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:12:34.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:34.115+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:12:34.117+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:12:34.136+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T11:13:06.118+0000] {processor.py:186} INFO - Started process (PID=2005) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:13:06.120+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:13:06.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:06.122+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:13:06.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:06.136+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:13:06.138+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:13:06.156+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.046 seconds
[2025-07-18T11:13:36.677+0000] {processor.py:186} INFO - Started process (PID=2146) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:13:36.678+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:13:36.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.679+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:13:36.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.690+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:13:36.691+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:13:36.708+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.037 seconds
[2025-07-18T11:14:08.042+0000] {processor.py:186} INFO - Started process (PID=2282) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:14:08.043+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:14:08.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:08.046+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:14:08.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:08.058+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:14:08.061+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:14:08.081+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.046 seconds
[2025-07-18T11:14:38.464+0000] {processor.py:186} INFO - Started process (PID=2423) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:14:38.465+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:14:38.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:38.467+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:14:38.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:38.478+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:14:38.480+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:14:38.498+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T11:15:08.766+0000] {processor.py:186} INFO - Started process (PID=2564) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:15:08.768+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:15:08.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.770+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:15:08.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.784+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:15:08.787+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:15:08.814+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.056 seconds
[2025-07-18T11:15:39.732+0000] {processor.py:186} INFO - Started process (PID=2705) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:15:39.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:15:39.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.735+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:15:39.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.748+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:15:39.749+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:15:39.770+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-18T11:16:10.605+0000] {processor.py:186} INFO - Started process (PID=2846) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:16:10.606+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:16:10.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.608+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:16:10.626+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.624+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:16:10.626+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:16:10.647+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.048 seconds
[2025-07-18T11:16:41.091+0000] {processor.py:186} INFO - Started process (PID=2982) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:16:41.093+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:16:41.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:41.095+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:16:41.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:41.106+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:16:41.109+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:16:41.128+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T11:17:12.860+0000] {processor.py:186} INFO - Started process (PID=3130) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:17:12.861+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:17:12.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.863+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:17:12.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.879+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:17:12.881+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:17:12.901+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.049 seconds
[2025-07-18T11:17:43.500+0000] {processor.py:186} INFO - Started process (PID=3271) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:17:43.501+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:17:43.504+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.503+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:17:43.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.515+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:17:43.517+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:17:43.536+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-18T11:18:14.407+0000] {processor.py:186} INFO - Started process (PID=3412) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:18:14.408+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:18:14.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.410+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:18:14.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.425+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:18:14.427+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:18:14.448+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.048 seconds
[2025-07-18T11:18:44.856+0000] {processor.py:186} INFO - Started process (PID=3553) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:18:44.858+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:18:44.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.861+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:18:44.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.875+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:18:44.877+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:18:44.899+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.051 seconds
[2025-07-18T11:19:16.484+0000] {processor.py:186} INFO - Started process (PID=3697) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:19:16.485+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:19:16.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:16.487+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:19:16.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:16.509+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:19:16.512+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:19:16.541+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.064 seconds
[2025-07-18T11:19:46.896+0000] {processor.py:186} INFO - Started process (PID=3838) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:19:46.897+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:19:46.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:46.900+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:19:46.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:46.912+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:19:46.914+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:19:46.935+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-18T11:20:17.900+0000] {processor.py:186} INFO - Started process (PID=3981) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:20:17.901+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:20:17.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.903+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:20:17.921+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.919+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:20:17.922+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:20:17.940+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.048 seconds
