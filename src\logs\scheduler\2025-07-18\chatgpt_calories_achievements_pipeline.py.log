[2025-07-18T10:16:49.695+0000] {processor.py:186} INFO - Started process (PID=202) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:16:49.697+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:16:49.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.700+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:16:49.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.757+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:49.762+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:16:49.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.097 seconds
[2025-07-18T10:17:19.934+0000] {processor.py:186} INFO - Started process (PID=337) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:17:19.935+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:17:19.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:19.936+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:17:19.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:19.965+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:19.969+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:17:19.985+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.057 seconds
[2025-07-18T10:17:50.402+0000] {processor.py:186} INFO - Started process (PID=470) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:17:50.403+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:17:50.405+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.404+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:17:50.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.436+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:50.441+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:17:50.461+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.065 seconds
[2025-07-18T10:18:21.073+0000] {processor.py:186} INFO - Started process (PID=601) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:18:21.074+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:18:21.076+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.075+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:18:21.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.104+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.107+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:18:21.121+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.053 seconds
[2025-07-18T10:18:51.978+0000] {processor.py:186} INFO - Started process (PID=730) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:18:51.979+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:18:51.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:51.980+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:18:52.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.009+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:52.012+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:18:52.027+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.055 seconds
[2025-07-18T10:19:22.908+0000] {processor.py:186} INFO - Started process (PID=863) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:19:22.908+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:19:22.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:22.909+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:19:22.942+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:22.940+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:22.943+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:19:22.958+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.057 seconds
[2025-07-18T10:19:53.871+0000] {processor.py:186} INFO - Started process (PID=992) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:19:53.872+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:19:53.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:53.874+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:19:53.921+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:53.918+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:53.922+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:19:53.941+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.077 seconds
[2025-07-18T10:20:24.906+0000] {processor.py:186} INFO - Started process (PID=1123) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:20:24.907+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:20:24.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:24.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:20:24.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:24.940+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:24.944+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:20:24.961+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.061 seconds
[2025-07-18T10:20:55.871+0000] {processor.py:186} INFO - Started process (PID=1254) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:20:55.872+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:20:55.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:55.873+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:20:55.907+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:55.904+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:55.908+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:20:55.923+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.058 seconds
[2025-07-18T10:21:26.842+0000] {processor.py:186} INFO - Started process (PID=1385) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:21:26.843+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:21:26.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:26.844+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:21:26.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:26.877+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:26.881+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:21:26.897+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.060 seconds
[2025-07-18T10:21:56.999+0000] {processor.py:186} INFO - Started process (PID=1516) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:21:57.000+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:21:57.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.001+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:21:57.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.029+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:57.033+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:21:57.048+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.054 seconds
[2025-07-18T10:22:28.050+0000] {processor.py:186} INFO - Started process (PID=1647) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:22:28.050+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:22:28.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.051+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:22:28.081+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.079+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:28.082+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:22:28.099+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.055 seconds
[2025-07-18T10:22:59.039+0000] {processor.py:186} INFO - Started process (PID=1778) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:22:59.040+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:22:59.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.041+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:22:59.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.069+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:59.073+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:22:59.089+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.056 seconds
[2025-07-18T10:23:29.977+0000] {processor.py:186} INFO - Started process (PID=1909) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:23:29.977+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:23:29.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:29.978+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:23:30.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.011+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:30.015+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:23:30.033+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.064 seconds
[2025-07-18T10:24:00.818+0000] {processor.py:186} INFO - Started process (PID=2040) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:24:00.818+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:24:00.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:00.819+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:24:00.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:00.849+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:00.855+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:24:00.872+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.060 seconds
[2025-07-18T10:24:31.058+0000] {processor.py:186} INFO - Started process (PID=2171) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:24:31.059+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:24:31.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.060+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:24:31.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.091+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.096+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:24:31.112+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.060 seconds
[2025-07-18T10:25:02.874+0000] {processor.py:186} INFO - Started process (PID=2302) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:25:02.875+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:25:02.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:02.876+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:25:03.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.054+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:03.063+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:25:03.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.151+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:03.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.161+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:25:03.182+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.313 seconds
[2025-07-18T10:26:19.980+0000] {processor.py:186} INFO - Started process (PID=206) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:26:19.981+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:26:19.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:19.983+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:26:20.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:20.361+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:20.367+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:26:20.465+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:20.464+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:20.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:20.474+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:26:20.493+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.518 seconds
[2025-07-18T10:26:51.030+0000] {processor.py:186} INFO - Started process (PID=337) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:26:51.031+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:26:51.033+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:51.033+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:26:51.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:51.429+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:51.437+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:26:51.696+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:51.695+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:51.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:51.733+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:26:51.814+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.790 seconds
[2025-07-18T10:27:22.037+0000] {processor.py:186} INFO - Started process (PID=468) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:27:22.038+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:27:22.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:22.041+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:27:22.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:22.365+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:22.374+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:27:22.480+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:22.480+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:22.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:22.493+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:27:22.513+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.481 seconds
[2025-07-18T10:27:52.822+0000] {processor.py:186} INFO - Started process (PID=599) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:27:52.822+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:27:52.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:52.824+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:27:53.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:53.011+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:53.019+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:27:53.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:53.110+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:53.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:53.120+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:27:53.138+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.323 seconds
[2025-07-18T10:28:23.446+0000] {processor.py:186} INFO - Started process (PID=730) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:28:23.446+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:28:23.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:23.448+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:28:23.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:23.691+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:23.698+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:28:23.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:23.796+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:23.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:23.805+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:28:23.823+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.383 seconds
[2025-07-18T10:28:53.905+0000] {processor.py:186} INFO - Started process (PID=861) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:28:53.906+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:28:53.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:53.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:28:54.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:54.099+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:54.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:28:54.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:54.203+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:54.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:54.215+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:28:54.236+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.336 seconds
[2025-07-18T10:29:24.501+0000] {processor.py:186} INFO - Started process (PID=992) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:29:24.502+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:29:24.504+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:24.504+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:29:24.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:24.693+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:24.699+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:29:24.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:24.788+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:24.799+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:24.799+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:29:24.819+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.324 seconds
[2025-07-18T10:29:55.031+0000] {processor.py:186} INFO - Started process (PID=1123) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:29:55.032+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:29:55.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:55.033+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:29:55.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:55.220+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:55.228+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:29:55.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:55.319+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:55.328+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:55.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:29:55.348+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.323 seconds
[2025-07-18T10:30:25.726+0000] {processor.py:186} INFO - Started process (PID=1254) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:30:25.727+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:30:25.730+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:25.730+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:30:25.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:25.928+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:25.937+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:30:26.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:26.040+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:26.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:26.050+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:30:26.067+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.349 seconds
[2025-07-18T10:30:56.212+0000] {processor.py:186} INFO - Started process (PID=1385) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:30:56.213+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:30:56.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:56.215+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:30:56.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:56.430+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:56.439+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:30:56.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:56.537+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:56.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:56.547+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:30:56.567+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.361 seconds
[2025-07-18T10:31:27.160+0000] {processor.py:186} INFO - Started process (PID=1516) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:31:27.161+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:31:27.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:27.163+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:31:27.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:27.349+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:27.358+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:31:27.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:27.448+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:27.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:27.458+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:31:27.476+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.322 seconds
[2025-07-18T10:31:57.769+0000] {processor.py:186} INFO - Started process (PID=1647) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:31:57.770+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:31:57.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:57.773+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:31:57.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:57.986+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:57.993+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:31:58.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:58.089+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:58.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:58.099+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:31:58.120+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.361 seconds
[2025-07-18T10:32:28.244+0000] {processor.py:186} INFO - Started process (PID=1778) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:32:28.245+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:32:28.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:28.247+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:32:28.431+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:28.430+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:28.439+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:32:28.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:28.524+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:28.535+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:28.534+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:32:28.554+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.316 seconds
[2025-07-18T10:32:58.680+0000] {processor.py:186} INFO - Started process (PID=1909) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:32:58.681+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:32:58.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:58.683+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:32:58.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:58.868+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:58.877+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:32:58.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:58.976+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:58.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:58.987+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:32:59.006+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.332 seconds
[2025-07-18T10:33:29.308+0000] {processor.py:186} INFO - Started process (PID=2040) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:33:29.309+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:33:29.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:29.310+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:33:29.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:29.512+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:29.521+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:33:29.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:29.612+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:29.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:29.623+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:33:29.643+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.341 seconds
[2025-07-18T10:33:59.849+0000] {processor.py:186} INFO - Started process (PID=2171) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:33:59.850+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:33:59.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:59.851+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:34:00.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:00.052+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:00.061+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:34:00.154+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:00.154+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:00.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:00.164+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:34:00.183+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.340 seconds
[2025-07-18T10:34:30.995+0000] {processor.py:186} INFO - Started process (PID=2304) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:34:30.996+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:34:30.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:30.998+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:34:31.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:31.187+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:31.196+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:34:31.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:31.288+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:31.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:31.297+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:34:31.314+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.325 seconds
[2025-07-18T10:35:01.829+0000] {processor.py:186} INFO - Started process (PID=2435) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:35:01.831+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:35:01.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:01.833+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:35:02.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:02.009+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:02.017+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:35:02.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:02.103+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:02.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:02.112+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:35:02.129+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.308 seconds
[2025-07-18T10:35:32.442+0000] {processor.py:186} INFO - Started process (PID=2564) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:35:32.444+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:35:32.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:32.446+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:35:32.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:32.664+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:32.672+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:35:32.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:32.769+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:32.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:32.779+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:35:32.798+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.363 seconds
[2025-07-18T10:36:03.232+0000] {processor.py:186} INFO - Started process (PID=2697) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:36:03.233+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:36:03.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:03.235+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:36:03.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:03.414+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:03.420+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:36:03.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:03.513+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:03.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:03.521+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:36:03.538+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.312 seconds
[2025-07-18T10:36:34.219+0000] {processor.py:186} INFO - Started process (PID=2828) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:36:34.220+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:36:34.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:34.223+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:36:34.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:34.424+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:34.433+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:36:34.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:34.532+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:34.542+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:34.542+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:36:34.560+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.347 seconds
[2025-07-18T10:37:05.041+0000] {processor.py:186} INFO - Started process (PID=2959) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:37:05.042+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:37:05.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:05.044+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:37:05.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:05.236+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:05.252+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:37:05.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:05.368+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:05.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:05.379+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:37:05.398+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.364 seconds
[2025-07-18T10:37:35.977+0000] {processor.py:186} INFO - Started process (PID=3090) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:37:35.978+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:37:35.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:35.980+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:37:36.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:36.188+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:36.197+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:37:36.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:36.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:36.309+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:36.309+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:37:36.328+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.358 seconds
[2025-07-18T10:38:06.802+0000] {processor.py:186} INFO - Started process (PID=3221) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:38:06.803+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:38:06.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:06.804+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:38:07.013+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:07.013+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:07.024+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:38:07.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:07.138+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:07.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:07.149+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:38:07.170+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.374 seconds
[2025-07-18T10:38:37.662+0000] {processor.py:186} INFO - Started process (PID=3352) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:38:37.663+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:38:37.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:37.665+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:38:37.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:37.874+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:37.882+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:38:37.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:37.976+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:37.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:37.987+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:38:38.007+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.350 seconds
[2025-07-18T10:39:08.367+0000] {processor.py:186} INFO - Started process (PID=3483) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:39:08.368+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:39:08.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:08.370+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:39:08.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:08.543+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:08.551+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:39:08.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:08.636+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:08.646+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:08.646+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:39:08.663+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.301 seconds
[2025-07-18T10:39:39.015+0000] {processor.py:186} INFO - Started process (PID=3612) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:39:39.016+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:39:39.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:39.018+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:39:39.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:39.240+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:39.248+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:39:39.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:39.344+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:39.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:39.355+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:39:39.374+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.367 seconds
[2025-07-18T10:40:09.756+0000] {processor.py:186} INFO - Started process (PID=3743) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:40:09.757+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:40:09.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:09.760+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:40:09.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:09.975+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:09.983+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:40:10.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:10.073+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:10.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:10.083+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:40:10.101+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.352 seconds
[2025-07-18T10:40:40.690+0000] {processor.py:186} INFO - Started process (PID=3876) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:40:40.691+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:40:40.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:40.693+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:40:40.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:40.874+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:40.883+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:40:40.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:40.975+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:40.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:40.984+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:40:41.002+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.318 seconds
[2025-07-18T10:41:11.589+0000] {processor.py:186} INFO - Started process (PID=4007) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:41:11.590+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:41:11.593+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:11.593+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:41:11.791+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:11.791+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:11.800+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:41:11.896+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:11.896+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:11.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:11.906+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:41:11.925+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.341 seconds
[2025-07-18T10:41:42.231+0000] {processor.py:186} INFO - Started process (PID=4143) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:41:42.233+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:41:42.235+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:42.235+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:41:42.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:42.453+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:42.461+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:41:42.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:42.706+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:42.718+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:42.718+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:41:42.740+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.515 seconds
[2025-07-18T10:42:13.190+0000] {processor.py:186} INFO - Started process (PID=4277) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:42:13.191+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:42:13.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:13.193+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:42:13.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:13.390+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:13.399+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:42:13.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:13.654+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:13.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:13.663+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:42:13.680+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.496 seconds
[2025-07-18T10:42:57.407+0000] {processor.py:186} INFO - Started process (PID=205) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:42:57.408+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:42:57.411+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.410+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:42:57.842+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.841+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:57.847+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:42:57.938+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.937+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:57.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.946+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:42:57.964+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.562 seconds
[2025-07-18T10:43:28.400+0000] {processor.py:186} INFO - Started process (PID=349) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:43:28.401+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:43:28.403+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:28.403+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:43:28.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:28.736+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:28.743+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:43:28.839+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:28.838+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:28.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:28.847+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:43:28.866+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.472 seconds
[2025-07-18T10:43:59.495+0000] {processor.py:186} INFO - Started process (PID=483) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:43:59.496+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:43:59.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.498+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:43:59.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.819+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:59.825+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:43:59.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:59.921+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.921+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:43:59.938+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.450 seconds
[2025-07-18T10:44:30.295+0000] {processor.py:186} INFO - Started process (PID=621) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:44:30.296+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:44:30.298+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.298+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:44:30.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.478+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:30.488+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:44:30.584+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.583+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:30.593+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.593+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:44:30.611+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.322 seconds
[2025-07-18T10:45:01.318+0000] {processor.py:186} INFO - Started process (PID=756) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:45:01.319+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:45:01.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.321+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:45:01.539+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.539+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:01.547+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:45:01.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.644+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:01.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.657+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:45:01.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.363 seconds
[2025-07-18T10:45:31.841+0000] {processor.py:186} INFO - Started process (PID=893) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:45:31.842+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:45:31.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:31.844+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:45:32.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:32.073+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:32.080+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:45:32.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:32.208+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:32.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:32.223+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:45:32.242+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.408 seconds
[2025-07-18T10:46:02.783+0000] {processor.py:186} INFO - Started process (PID=1029) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:46:02.783+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:46:02.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:02.785+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:46:02.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:02.976+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:02.984+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:46:03.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.077+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:03.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.088+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:46:03.107+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.330 seconds
[2025-07-18T10:46:33.565+0000] {processor.py:186} INFO - Started process (PID=1165) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:46:33.567+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:46:33.569+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:33.569+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:46:33.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:33.817+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:33.826+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:46:33.930+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:33.930+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:33.942+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:33.942+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:46:33.965+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.406 seconds
[2025-07-18T10:47:04.306+0000] {processor.py:186} INFO - Started process (PID=1301) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:47:04.307+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:47:04.309+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:04.309+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:47:04.514+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:04.513+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:04.522+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:47:04.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:04.611+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:04.622+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:04.622+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:47:04.640+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.340 seconds
[2025-07-18T10:48:02.840+0000] {processor.py:186} INFO - Started process (PID=211) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:48:02.841+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:48:02.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:02.843+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:48:03.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.179+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:03.185+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:48:03.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.274+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:03.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.283+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:48:03.301+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.467 seconds
[2025-07-18T10:48:33.769+0000] {processor.py:186} INFO - Started process (PID=349) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:48:33.770+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:48:33.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:33.772+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:48:34.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:34.120+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:34.127+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:48:34.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:34.225+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:34.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:34.233+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:48:34.252+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.488 seconds
[2025-07-18T10:49:04.572+0000] {processor.py:186} INFO - Started process (PID=483) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:49:04.573+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:49:04.576+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.575+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:49:04.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.892+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:04.898+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:49:04.992+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.992+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:05.002+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.002+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:49:05.019+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.452 seconds
[2025-07-18T10:49:36.058+0000] {processor.py:186} INFO - Started process (PID=621) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:49:36.059+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:49:36.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.061+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:49:36.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.248+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:36.257+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:49:36.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.345+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:36.355+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.354+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:49:36.372+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.319 seconds
[2025-07-18T10:50:06.569+0000] {processor.py:186} INFO - Started process (PID=757) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:50:06.569+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:50:06.572+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.571+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:50:06.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.769+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:06.777+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:50:06.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.875+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:06.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.884+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:50:06.904+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.342 seconds
[2025-07-18T10:50:37.370+0000] {processor.py:186} INFO - Started process (PID=893) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:50:37.371+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:50:37.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:37.373+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:50:37.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:37.572+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:37.582+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:50:37.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:37.685+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:37.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:37.695+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:50:37.716+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.352 seconds
[2025-07-18T10:51:08.098+0000] {processor.py:186} INFO - Started process (PID=1029) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:51:08.099+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:51:08.101+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.101+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:51:08.331+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.331+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:08.342+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:51:08.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.449+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:08.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.459+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:51:08.478+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.386 seconds
[2025-07-18T10:51:38.668+0000] {processor.py:186} INFO - Started process (PID=1166) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:51:38.669+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:51:38.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.671+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:51:38.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.884+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:38.892+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:51:39.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:39.000+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:39.013+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:39.012+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:51:39.035+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.373 seconds
[2025-07-18T10:52:09.123+0000] {processor.py:186} INFO - Started process (PID=1302) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:52:09.123+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:52:09.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.125+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:52:09.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.320+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:09.331+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:52:09.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.443+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:09.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.457+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:52:09.476+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.359 seconds
[2025-07-18T10:52:39.637+0000] {processor.py:186} INFO - Started process (PID=1438) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:52:39.638+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:52:39.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.640+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:52:39.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.857+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:39.866+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:52:39.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.977+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:39.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.994+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:52:40.024+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.393 seconds
[2025-07-18T10:53:10.253+0000] {processor.py:186} INFO - Started process (PID=1574) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:53:10.254+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:53:10.256+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.256+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:53:10.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.463+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:10.471+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:53:10.565+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.565+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:10.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.578+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:53:10.599+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.352 seconds
[2025-07-18T10:53:40.738+0000] {processor.py:186} INFO - Started process (PID=1710) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:53:40.738+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:53:40.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:53:40.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.919+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:40.929+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:53:41.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:41.025+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:41.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:41.037+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:53:41.056+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.324 seconds
[2025-07-18T10:54:11.549+0000] {processor.py:186} INFO - Started process (PID=1846) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:54:11.550+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:54:11.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.552+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:54:11.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.772+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:11.780+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:54:11.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.882+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:11.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.892+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:54:11.915+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.373 seconds
[2025-07-18T10:54:42.204+0000] {processor.py:186} INFO - Started process (PID=1982) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:54:42.205+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:54:42.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.207+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:54:42.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.406+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:42.415+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:54:42.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.512+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:42.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.524+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:54:42.547+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.348 seconds
[2025-07-18T10:55:12.819+0000] {processor.py:186} INFO - Started process (PID=2118) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:55:12.820+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:55:12.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.821+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:55:13.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.012+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:13.021+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:55:13.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.108+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:13.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.120+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:55:13.140+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.327 seconds
[2025-07-18T10:55:43.669+0000] {processor.py:186} INFO - Started process (PID=2254) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:55:43.670+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:55:43.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.672+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:55:43.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.856+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:43.865+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:55:43.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.958+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:43.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.969+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:55:43.988+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.324 seconds
[2025-07-18T10:57:27.569+0000] {processor.py:186} INFO - Started process (PID=211) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:57:27.570+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:57:27.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.572+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:57:27.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.908+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:27.914+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:57:27.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.998+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:28.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.006+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:57:28.025+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.463 seconds
[2025-07-18T10:57:58.627+0000] {processor.py:186} INFO - Started process (PID=349) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:57:58.628+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:57:58.630+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:58.629+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:57:58.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:58.973+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:58.981+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:57:59.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:59.064+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:59.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:59.073+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:57:59.092+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.471 seconds
[2025-07-18T10:58:29.213+0000] {processor.py:186} INFO - Started process (PID=485) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:58:29.214+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:58:29.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:29.216+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:58:29.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:29.512+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:29.519+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:58:29.614+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:29.613+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:29.622+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:29.622+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:58:29.639+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.431 seconds
[2025-07-18T10:59:00.152+0000] {processor.py:186} INFO - Started process (PID=621) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:59:00.153+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:59:00.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:00.155+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:59:00.338+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:00.337+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:00.346+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:59:00.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:00.443+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:00.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:00.453+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:59:00.471+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.324 seconds
[2025-07-18T10:59:31.053+0000] {processor.py:186} INFO - Started process (PID=757) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:59:31.054+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T10:59:31.056+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:31.056+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:59:31.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:31.234+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:31.242+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T10:59:31.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:31.325+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:31.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:31.334+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T10:59:31.351+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.304 seconds
[2025-07-18T11:00:01.649+0000] {processor.py:186} INFO - Started process (PID=893) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:00:01.650+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:00:01.652+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:01.652+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:00:01.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:01.843+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:01.853+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:00:01.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:01.943+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:01.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:01.952+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:00:01.972+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.328 seconds
[2025-07-18T11:00:32.381+0000] {processor.py:186} INFO - Started process (PID=1029) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:00:32.382+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:00:32.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:32.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:00:32.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:32.555+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:32.563+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:00:32.646+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:32.646+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:32.655+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:32.654+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:00:32.672+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.297 seconds
[2025-07-18T11:01:02.836+0000] {processor.py:186} INFO - Started process (PID=1163) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:01:02.837+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:01:02.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.840+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:01:03.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:03.052+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:03.063+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:01:03.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:03.179+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:03.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:03.191+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:01:03.215+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.385 seconds
[2025-07-18T11:01:33.383+0000] {processor.py:186} INFO - Started process (PID=1299) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:01:33.384+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:01:33.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.387+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:01:33.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.579+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:33.587+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:01:33.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.679+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:33.689+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.689+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:01:33.708+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.331 seconds
[2025-07-18T11:02:04.672+0000] {processor.py:186} INFO - Started process (PID=1437) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:02:04.673+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:02:04.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.675+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:02:04.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.875+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:04.885+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:02:04.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.978+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:04.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.988+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:02:05.006+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.341 seconds
[2025-07-18T11:02:35.586+0000] {processor.py:186} INFO - Started process (PID=1573) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:02:35.587+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:02:35.590+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.590+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:02:35.787+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.787+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:35.797+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:02:35.896+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.895+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:35.905+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.905+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:02:35.925+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.345 seconds
[2025-07-18T11:03:06.096+0000] {processor.py:186} INFO - Started process (PID=1709) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:03:06.097+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:03:06.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:06.099+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:03:06.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:06.289+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:06.298+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:03:06.405+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:06.404+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:06.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:06.416+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:03:06.439+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.350 seconds
[2025-07-18T11:03:36.751+0000] {processor.py:186} INFO - Started process (PID=1845) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:03:36.752+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:03:36.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.754+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:03:36.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.950+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:36.960+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:03:37.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:37.057+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:37.067+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:37.067+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:03:37.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.343 seconds
[2025-07-18T11:04:07.911+0000] {processor.py:186} INFO - Started process (PID=1981) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:04:07.912+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:04:07.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:07.914+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:04:08.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:08.130+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:08.140+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:04:08.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:08.239+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:08.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:08.253+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:04:08.274+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.369 seconds
[2025-07-18T11:04:38.539+0000] {processor.py:186} INFO - Started process (PID=2117) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:04:38.540+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:04:38.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.543+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:04:38.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.747+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:38.757+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:04:38.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.851+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:38.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.864+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:04:38.887+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.354 seconds
[2025-07-18T11:05:09.175+0000] {processor.py:186} INFO - Started process (PID=2253) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:05:09.176+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:05:09.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:09.179+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:05:09.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:09.416+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:09.429+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:05:09.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:09.531+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:09.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:09.543+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:05:09.564+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.396 seconds
[2025-07-18T11:05:40.203+0000] {processor.py:186} INFO - Started process (PID=2389) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:05:40.204+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:05:40.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:40.206+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:05:40.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:40.434+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:40.443+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:05:40.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:40.555+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:40.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:40.567+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:05:40.587+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.390 seconds
[2025-07-18T11:06:50.112+0000] {processor.py:186} INFO - Started process (PID=217) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:06:50.113+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:06:50.115+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.114+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:06:50.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.453+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:50.458+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:06:50.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.543+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:50.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.552+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:06:50.570+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.464 seconds
[2025-07-18T11:07:21.273+0000] {processor.py:186} INFO - Started process (PID=358) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:07:21.274+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:07:21.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:21.276+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:07:21.629+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:21.629+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:21.638+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:07:21.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:21.746+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:21.757+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:21.757+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:07:21.775+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.509 seconds
[2025-07-18T11:07:52.714+0000] {processor.py:186} INFO - Started process (PID=505) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:07:52.715+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:07:52.718+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:52.718+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:07:53.084+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.083+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:53.089+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:07:53.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.202+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:53.212+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.212+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:07:53.233+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.525 seconds
[2025-07-18T11:08:23.717+0000] {processor.py:186} INFO - Started process (PID=646) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:08:23.718+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:08:23.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.720+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:08:23.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.946+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:23.956+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:08:24.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.077+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:24.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.090+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:08:24.108+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.397 seconds
[2025-07-18T11:08:54.610+0000] {processor.py:186} INFO - Started process (PID=787) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:08:54.611+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:08:54.614+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.613+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:08:54.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.819+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:54.828+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:08:54.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:54.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.943+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:08:54.973+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.369 seconds
[2025-07-18T11:09:25.120+0000] {processor.py:186} INFO - Started process (PID=928) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:09:25.121+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:09:25.123+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.123+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:09:25.331+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.331+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:25.338+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:09:25.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.434+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:25.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.446+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:09:25.468+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.354 seconds
[2025-07-18T11:09:55.706+0000] {processor.py:186} INFO - Started process (PID=1069) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:09:55.706+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:09:55.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.709+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:09:55.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.898+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:55.906+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:09:56.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:56.005+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:56.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:56.016+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:09:56.035+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.335 seconds
[2025-07-18T11:10:26.622+0000] {processor.py:186} INFO - Started process (PID=1210) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:10:26.623+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:10:26.625+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.625+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:10:26.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.816+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:26.824+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:10:26.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.917+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:26.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.929+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:10:26.948+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.332 seconds
[2025-07-18T11:10:57.625+0000] {processor.py:186} INFO - Started process (PID=1351) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:10:57.626+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:10:57.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.628+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:10:57.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.816+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:57.825+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:10:57.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.918+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:57.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.928+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:10:57.947+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.327 seconds
[2025-07-18T11:11:28.145+0000] {processor.py:186} INFO - Started process (PID=1492) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:11:28.146+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:11:28.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.148+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:11:28.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.346+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:28.355+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:11:28.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.456+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:28.467+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.467+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:11:28.491+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.351 seconds
[2025-07-18T11:11:59.270+0000] {processor.py:186} INFO - Started process (PID=1633) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:11:59.271+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:11:59.274+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.273+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:11:59.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.465+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:59.473+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:11:59.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.566+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:59.577+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.577+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:11:59.597+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.332 seconds
[2025-07-18T11:12:30.226+0000] {processor.py:186} INFO - Started process (PID=1776) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:12:30.226+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:12:30.229+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.228+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:12:30.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.428+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:30.437+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:12:30.535+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.534+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:30.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.546+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:12:30.567+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.347 seconds
[2025-07-18T11:13:01.608+0000] {processor.py:186} INFO - Started process (PID=1917) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:13:01.609+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:13:01.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:01.611+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:13:01.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:01.850+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:01.859+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:13:01.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:01.997+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:02.021+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.021+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:13:02.052+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.450 seconds
[2025-07-18T11:13:32.106+0000] {processor.py:186} INFO - Started process (PID=2058) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:13:32.107+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:13:32.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:32.109+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:13:32.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:32.285+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:32.293+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:13:32.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:32.378+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:32.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:32.389+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:13:32.408+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.308 seconds
[2025-07-18T11:14:03.204+0000] {processor.py:186} INFO - Started process (PID=2199) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:14:03.205+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:14:03.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.207+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:14:03.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.423+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:03.432+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:14:03.542+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.542+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:03.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.553+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:14:03.575+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.377 seconds
[2025-07-18T11:14:33.801+0000] {processor.py:186} INFO - Started process (PID=2340) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:14:33.802+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:14:33.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:33.804+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:14:34.002+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.002+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:34.011+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:14:34.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.121+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:34.132+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.132+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:14:34.154+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.358 seconds
[2025-07-18T11:15:04.468+0000] {processor.py:186} INFO - Started process (PID=2479) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:15:04.469+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:15:04.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.471+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:15:04.692+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.691+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:04.702+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:15:04.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.812+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:04.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.825+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:15:04.846+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.384 seconds
[2025-07-18T11:15:35.328+0000] {processor.py:186} INFO - Started process (PID=2620) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:15:35.329+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:15:35.331+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:35.331+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:15:35.520+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:35.519+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:35.528+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:15:35.629+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:35.629+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:35.639+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:35.639+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:15:35.659+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.337 seconds
[2025-07-18T11:16:05.930+0000] {processor.py:186} INFO - Started process (PID=2761) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:16:05.930+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:16:05.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.932+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:16:06.136+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.136+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:06.146+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:16:06.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.240+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:06.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.253+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:16:06.273+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.349 seconds
[2025-07-18T11:16:36.722+0000] {processor.py:186} INFO - Started process (PID=2902) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:16:36.722+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:16:36.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.724+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:16:36.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.920+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:36.929+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:16:37.027+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.026+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:37.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.040+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:16:37.060+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.345 seconds
[2025-07-18T11:17:07.681+0000] {processor.py:186} INFO - Started process (PID=3043) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:17:07.682+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:17:07.686+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:07.686+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:17:07.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:07.927+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:07.937+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:17:08.067+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:08.066+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:08.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:08.079+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:17:08.102+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.430 seconds
[2025-07-18T11:17:38.713+0000] {processor.py:186} INFO - Started process (PID=3184) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:17:38.714+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:17:38.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:38.715+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:17:38.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:38.947+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:38.956+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:17:39.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.077+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:39.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.090+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:17:39.111+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.405 seconds
[2025-07-18T11:18:09.379+0000] {processor.py:186} INFO - Started process (PID=3325) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:18:09.380+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:18:09.382+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:09.382+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:18:09.600+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:09.599+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:09.609+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:18:09.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:09.708+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:09.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:09.720+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:18:09.741+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.368 seconds
[2025-07-18T11:18:40.349+0000] {processor.py:186} INFO - Started process (PID=3466) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:18:40.350+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:18:40.354+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:40.353+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:18:40.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:40.562+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:40.571+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:18:40.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:40.671+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:40.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:40.682+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:18:40.704+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.363 seconds
[2025-07-18T11:19:11.062+0000] {processor.py:186} INFO - Started process (PID=3607) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:19:11.063+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:19:11.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.065+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:19:11.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.304+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:11.313+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:19:11.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.425+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:11.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.439+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:19:11.460+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.406 seconds
[2025-07-18T11:19:41.646+0000] {processor.py:186} INFO - Started process (PID=3753) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:19:41.648+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:19:41.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.651+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:19:41.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.861+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:41.870+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:19:41.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.976+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:41.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.988+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:19:42.007+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.369 seconds
[2025-07-18T11:20:12.097+0000] {processor.py:186} INFO - Started process (PID=3894) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:20:12.098+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:20:12.101+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.101+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:20:12.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.324+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:12.334+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:20:12.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.436+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:12.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.447+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:20:12.466+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.376 seconds
[2025-07-18T12:38:07.789+0000] {processor.py:186} INFO - Started process (PID=212) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:38:07.790+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T12:38:07.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.793+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:38:07.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.882+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:07.890+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:38:07.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.985+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_calories_achievements_pipeline
[2025-07-18T12:38:07.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.994+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_calories_achievements_pipeline
[2025-07-18T12:38:08.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.001+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_calories_achievements_pipeline
[2025-07-18T12:38:08.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.007+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_calories_achievements_pipeline
[2025-07-18T12:38:08.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.012+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_calories_achievements_pipeline
[2025-07-18T12:38:08.018+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.018+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_calories_achievements_pipeline
[2025-07-18T12:38:08.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.024+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_calories_achievements_pipeline
[2025-07-18T12:38:08.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.024+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:08.174+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T12:38:08.175+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.175+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_calories_achievements_pipeline
[2025-07-18T12:38:08.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.176+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T12:38:08.192+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.411 seconds
[2025-07-18T12:38:38.453+0000] {processor.py:186} INFO - Started process (PID=348) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:38:38.454+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T12:38:38.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:38.456+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:38:38.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:38.531+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:38.539+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:38:38.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:38.774+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:38.787+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:38.787+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T12:38:38.811+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.363 seconds
[2025-07-18T12:39:09.056+0000] {processor.py:186} INFO - Started process (PID=484) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:39:09.057+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T12:39:09.059+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:09.059+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:39:09.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:09.246+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:09.251+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:39:09.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:09.341+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:09.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:09.351+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T12:39:09.371+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.320 seconds
[2025-07-18T12:39:39.532+0000] {processor.py:186} INFO - Started process (PID=622) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:39:39.532+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T12:39:39.535+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:39.534+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:39:39.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:39.597+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:39.606+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:39:39.690+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:39.690+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:39.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:39.700+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T12:39:39.717+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.191 seconds
[2025-07-18T12:40:10.261+0000] {processor.py:186} INFO - Started process (PID=757) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:40:10.262+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T12:40:10.264+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:10.264+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:40:10.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:10.336+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:10.345+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:40:10.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:10.433+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:10.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:10.443+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T12:40:10.460+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.205 seconds
[2025-07-18T12:40:41.202+0000] {processor.py:186} INFO - Started process (PID=893) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:40:41.202+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T12:40:41.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:41.204+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:40:41.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:41.272+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:41.282+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:40:41.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:41.370+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:41.380+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:41.380+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T12:40:41.410+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.213 seconds
[2025-07-18T12:41:11.777+0000] {processor.py:186} INFO - Started process (PID=1029) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:41:11.778+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T12:41:11.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:11.780+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:41:11.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:11.852+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:41:11.860+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T12:41:11.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:11.952+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:41:11.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:11.961+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T12:41:11.978+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.207 seconds
