[2025-07-18T10:16:49.501+0000] {processor.py:186} INFO - Started process (PID=192) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:16:49.503+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:16:49.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.505+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:16:49.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.592+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:49.602+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:16:49.635+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.141 seconds
[2025-07-18T10:17:19.803+0000] {processor.py:186} INFO - Started process (PID=323) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:17:19.804+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:17:19.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:19.806+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:17:19.842+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:19.838+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:19.844+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:17:19.868+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.072 seconds
[2025-07-18T10:17:50.211+0000] {processor.py:186} INFO - Started process (PID=460) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:17:50.212+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:17:50.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.213+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:17:50.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.247+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:50.250+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:17:50.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.062 seconds
[2025-07-18T10:18:20.989+0000] {processor.py:186} INFO - Started process (PID=591) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:18:20.990+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:18:20.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:20.991+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:18:21.021+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.019+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.022+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:18:21.038+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.054 seconds
[2025-07-18T10:18:51.879+0000] {processor.py:186} INFO - Started process (PID=722) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:18:51.880+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:18:51.881+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:51.881+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:18:51.917+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:51.913+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:51.918+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:18:51.936+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.062 seconds
[2025-07-18T10:19:22.818+0000] {processor.py:186} INFO - Started process (PID=853) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:19:22.819+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:19:22.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:22.820+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:19:22.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:22.850+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:22.854+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:19:22.872+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.059 seconds
[2025-07-18T10:19:53.749+0000] {processor.py:186} INFO - Started process (PID=984) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:19:53.750+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:19:53.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:53.751+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:19:53.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:53.785+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:53.790+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:19:53.807+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.063 seconds
[2025-07-18T10:20:24.797+0000] {processor.py:186} INFO - Started process (PID=1115) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:20:24.798+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:20:24.799+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:24.799+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:20:24.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:24.832+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:24.836+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:20:24.853+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.063 seconds
[2025-07-18T10:20:55.772+0000] {processor.py:186} INFO - Started process (PID=1246) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:20:55.773+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:20:55.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:55.774+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:20:55.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:55.808+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:55.812+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:20:55.827+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.061 seconds
[2025-07-18T10:21:26.741+0000] {processor.py:186} INFO - Started process (PID=1377) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:21:26.742+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:21:26.743+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:26.743+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:21:26.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:26.776+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:26.780+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:21:26.796+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.061 seconds
[2025-07-18T10:21:56.893+0000] {processor.py:186} INFO - Started process (PID=1508) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:21:56.894+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:21:56.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:56.895+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:21:56.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:56.927+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:56.930+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:21:56.947+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.059 seconds
[2025-07-18T10:22:27.955+0000] {processor.py:186} INFO - Started process (PID=1639) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:22:27.956+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:22:27.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:27.957+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:22:27.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:27.989+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:27.992+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:22:28.006+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.058 seconds
[2025-07-18T10:22:58.943+0000] {processor.py:186} INFO - Started process (PID=1770) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:22:58.944+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:22:58.945+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:58.945+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:22:58.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:58.976+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:58.979+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:22:58.994+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.056 seconds
[2025-07-18T10:23:29.878+0000] {processor.py:186} INFO - Started process (PID=1901) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:23:29.879+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:23:29.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:29.880+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:23:29.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:29.909+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:29.914+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:23:29.931+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.059 seconds
[2025-07-18T10:24:00.721+0000] {processor.py:186} INFO - Started process (PID=2032) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:24:00.722+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:24:00.723+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:00.723+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:24:00.758+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:00.756+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:00.759+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:24:00.775+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.060 seconds
[2025-07-18T10:24:30.947+0000] {processor.py:186} INFO - Started process (PID=2163) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:24:30.948+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:24:30.950+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:30.949+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:24:30.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:30.980+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_recount_calories_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:30.984+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:24:31.000+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.059 seconds
[2025-07-18T10:25:02.530+0000] {processor.py:186} INFO - Started process (PID=2294) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:25:02.531+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:25:02.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:02.532+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:25:02.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:02.706+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:02.715+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:25:02.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:02.801+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:02.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:02.810+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:25:02.828+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.304 seconds
[2025-07-18T10:26:19.419+0000] {processor.py:186} INFO - Started process (PID=198) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:26:19.421+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:26:19.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:19.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:26:19.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:19.797+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:19.805+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:26:19.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:19.902+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:19.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:19.911+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:26:19.931+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.520 seconds
[2025-07-18T10:26:49.989+0000] {processor.py:186} INFO - Started process (PID=329) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:26:49.990+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:26:49.992+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:49.992+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:26:50.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:50.322+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:50.328+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:26:50.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:50.414+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:50.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:50.424+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:26:50.441+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.457 seconds
[2025-07-18T10:27:20.962+0000] {processor.py:186} INFO - Started process (PID=458) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:27:20.963+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:27:20.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:20.965+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:27:21.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:21.283+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:21.289+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:27:21.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:21.376+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:21.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:21.385+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:27:21.405+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.449 seconds
[2025-07-18T10:27:51.790+0000] {processor.py:186} INFO - Started process (PID=591) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:27:51.791+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:27:51.793+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:51.793+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:27:51.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:51.987+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:51.996+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:27:52.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:52.089+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:52.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:52.100+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:27:52.119+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.335 seconds
[2025-07-18T10:28:22.411+0000] {processor.py:186} INFO - Started process (PID=722) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:28:22.412+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:28:22.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:22.413+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:28:22.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:22.606+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:22.615+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:28:22.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:22.709+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:22.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:22.719+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:28:22.738+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.334 seconds
[2025-07-18T10:28:52.868+0000] {processor.py:186} INFO - Started process (PID=853) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:28:52.869+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:28:52.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:52.871+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:28:53.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:53.061+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:53.070+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:28:53.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:53.167+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:53.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:53.177+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:28:53.196+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.333 seconds
[2025-07-18T10:29:23.468+0000] {processor.py:186} INFO - Started process (PID=984) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:29:23.469+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:29:23.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:23.471+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:29:23.711+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:23.711+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:23.718+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:29:23.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:23.808+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:23.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:23.819+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:29:23.837+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.375 seconds
[2025-07-18T10:29:54.001+0000] {processor.py:186} INFO - Started process (PID=1115) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:29:54.002+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:29:54.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:54.004+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:29:54.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:54.214+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:54.223+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:29:54.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:54.321+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:54.331+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:54.331+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:29:54.351+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.358 seconds
[2025-07-18T10:30:24.671+0000] {processor.py:186} INFO - Started process (PID=1246) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:30:24.674+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:30:24.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:24.676+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:30:24.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:24.861+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:24.870+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:30:24.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:24.960+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:24.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:24.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:30:24.987+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.324 seconds
[2025-07-18T10:30:55.174+0000] {processor.py:186} INFO - Started process (PID=1377) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:30:55.174+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:30:55.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:55.176+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:30:55.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:55.386+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:55.395+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:30:55.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:55.490+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:55.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:55.500+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:30:55.519+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.351 seconds
[2025-07-18T10:31:26.126+0000] {processor.py:186} INFO - Started process (PID=1508) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:31:26.127+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:31:26.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:26.128+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:31:26.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:26.326+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:26.335+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:31:26.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:26.421+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:26.431+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:26.431+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:31:26.448+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.328 seconds
[2025-07-18T10:31:56.721+0000] {processor.py:186} INFO - Started process (PID=1639) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:31:56.723+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:31:56.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:56.726+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:31:56.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:56.940+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:56.949+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:31:57.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:57.053+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:57.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:57.064+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:31:57.082+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.367 seconds
[2025-07-18T10:32:27.212+0000] {processor.py:186} INFO - Started process (PID=1770) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:32:27.213+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:32:27.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:27.216+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:32:27.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:27.414+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:27.423+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:32:27.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:27.512+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:27.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:27.522+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:32:27.539+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.334 seconds
[2025-07-18T10:32:57.640+0000] {processor.py:186} INFO - Started process (PID=1901) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:32:57.641+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:32:57.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:57.643+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:32:57.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:57.815+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:57.824+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:32:57.907+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:57.907+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:57.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:57.916+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:32:57.932+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.297 seconds
[2025-07-18T10:33:28.270+0000] {processor.py:186} INFO - Started process (PID=2032) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:33:28.271+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:33:28.273+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:28.273+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:33:28.469+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:28.469+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:28.478+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:33:28.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:28.580+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:28.591+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:28.591+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:33:28.613+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.349 seconds
[2025-07-18T10:33:59.483+0000] {processor.py:186} INFO - Started process (PID=2163) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:33:59.484+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:33:59.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:59.486+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:33:59.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:59.669+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:59.678+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:33:59.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:59.767+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:59.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:59.777+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:33:59.795+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.317 seconds
[2025-07-18T10:34:30.546+0000] {processor.py:186} INFO - Started process (PID=2294) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:34:30.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:34:30.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:30.550+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:34:30.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:30.796+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:30.806+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:34:30.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:30.928+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:30.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:30.944+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:34:30.965+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.428 seconds
[2025-07-18T10:35:01.387+0000] {processor.py:186} INFO - Started process (PID=2425) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:35:01.388+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:35:01.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:01.390+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:35:01.604+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:01.604+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:01.613+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:35:01.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:01.722+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:01.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:01.732+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:35:01.750+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.368 seconds
[2025-07-18T10:35:31.975+0000] {processor.py:186} INFO - Started process (PID=2556) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:35:31.976+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:35:31.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:31.979+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:35:32.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:32.189+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:32.198+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:35:32.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:32.356+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:32.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:32.372+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:35:32.397+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.428 seconds
[2025-07-18T10:36:02.839+0000] {processor.py:186} INFO - Started process (PID=2687) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:36:02.840+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:36:02.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:02.842+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:36:03.055+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:03.055+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:03.063+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:36:03.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:03.161+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:03.172+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:03.172+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:36:03.194+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.361 seconds
[2025-07-18T10:36:33.781+0000] {processor.py:186} INFO - Started process (PID=2818) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:36:33.782+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:36:33.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:33.785+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:36:34.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:34.043+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:34.050+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:36:34.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:34.161+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:34.171+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:34.171+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:36:34.186+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.412 seconds
[2025-07-18T10:37:04.675+0000] {processor.py:186} INFO - Started process (PID=2949) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:37:04.676+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:37:04.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:04.678+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:37:04.857+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:04.857+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:04.866+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:37:04.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:04.968+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:04.981+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:04.980+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:37:05.001+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.332 seconds
[2025-07-18T10:37:35.560+0000] {processor.py:186} INFO - Started process (PID=3080) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:37:35.561+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:37:35.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:35.563+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:37:35.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:35.795+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:35.809+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:37:35.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:35.902+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:35.913+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:35.912+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:37:35.931+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.377 seconds
[2025-07-18T10:38:06.371+0000] {processor.py:186} INFO - Started process (PID=3211) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:38:06.372+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:38:06.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:06.374+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:38:06.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:06.580+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:06.589+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:38:06.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:06.726+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:06.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:06.740+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:38:06.762+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.397 seconds
[2025-07-18T10:38:37.265+0000] {processor.py:186} INFO - Started process (PID=3342) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:38:37.266+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:38:37.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:37.268+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:38:37.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:37.475+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:37.483+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:38:37.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:37.586+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:37.597+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:37.597+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:38:37.617+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.359 seconds
[2025-07-18T10:39:07.671+0000] {processor.py:186} INFO - Started process (PID=3473) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:39:07.672+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:39:07.674+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:07.674+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:39:07.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:07.844+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:07.852+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:39:07.939+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:07.939+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:07.948+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:07.948+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:39:07.966+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.301 seconds
[2025-07-18T10:39:38.582+0000] {processor.py:186} INFO - Started process (PID=3604) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:39:38.583+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:39:38.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:38.586+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:39:38.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:38.815+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:38.822+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:39:38.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:38.925+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:38.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:38.940+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:39:39.100+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.524 seconds
[2025-07-18T10:40:09.329+0000] {processor.py:186} INFO - Started process (PID=3735) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:40:09.330+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:40:09.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:09.332+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:40:09.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:09.541+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:09.548+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:40:09.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:09.647+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:09.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:09.835+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:40:09.853+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.530 seconds
[2025-07-18T10:40:40.209+0000] {processor.py:186} INFO - Started process (PID=3866) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:40:40.210+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:40:40.212+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:40.212+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:40:40.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:40.400+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:40.410+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:40:40.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:40.500+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:40.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:40.631+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:40:40.648+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.446 seconds
[2025-07-18T10:41:11.037+0000] {processor.py:186} INFO - Started process (PID=3997) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:41:11.038+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:41:11.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:11.040+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:41:11.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:11.251+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:11.261+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:41:11.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:11.365+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:11.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:11.527+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:41:11.546+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.516 seconds
[2025-07-18T10:41:41.636+0000] {processor.py:186} INFO - Started process (PID=4133) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:41:41.636+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:41:41.638+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:41.638+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:41:41.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:41.861+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:41.872+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:41:42.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:42.166+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:42.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:42.179+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:41:42.199+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.569 seconds
[2025-07-18T10:42:12.640+0000] {processor.py:186} INFO - Started process (PID=4269) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:42:12.642+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:42:12.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:12.644+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:42:12.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:12.882+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:12.890+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:42:13.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:13.124+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:13.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:13.133+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:42:13.151+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.517 seconds
[2025-07-18T10:42:56.854+0000] {processor.py:186} INFO - Started process (PID=195) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:42:56.856+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:42:56.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:56.858+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:42:57.222+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.222+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:57.231+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:42:57.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:57.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.345+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:42:57.362+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.513 seconds
[2025-07-18T10:43:27.871+0000] {processor.py:186} INFO - Started process (PID=339) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:43:27.872+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:43:27.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:27.874+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:43:28.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:28.223+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:28.228+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:43:28.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:28.316+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:28.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:28.325+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:43:28.344+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.478 seconds
[2025-07-18T10:43:59.005+0000] {processor.py:186} INFO - Started process (PID=475) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:43:59.006+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:43:59.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.008+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:43:59.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.323+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:59.330+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:43:59.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.416+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:59.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.429+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:43:59.453+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.453 seconds
[2025-07-18T10:44:29.919+0000] {processor.py:186} INFO - Started process (PID=611) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:44:29.920+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:44:29.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:29.922+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:44:30.118+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.118+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:30.125+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:44:30.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.226+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:30.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.238+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:44:30.256+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.342 seconds
[2025-07-18T10:45:00.927+0000] {processor.py:186} INFO - Started process (PID=747) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:45:00.928+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:45:00.930+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:00.930+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:45:01.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.123+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:01.132+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:45:01.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.236+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:01.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.248+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:45:01.270+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.348 seconds
[2025-07-18T10:45:31.450+0000] {processor.py:186} INFO - Started process (PID=883) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:45:31.451+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:45:31.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:31.453+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:45:31.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:31.649+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:31.660+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:45:31.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:31.756+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:31.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:31.768+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:45:31.785+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.341 seconds
[2025-07-18T10:46:02.366+0000] {processor.py:186} INFO - Started process (PID=1019) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:46:02.367+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:46:02.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:02.369+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:46:02.582+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:02.582+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:02.592+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:46:02.711+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:02.710+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:02.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:02.722+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:46:02.743+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.383 seconds
[2025-07-18T10:46:33.112+0000] {processor.py:186} INFO - Started process (PID=1155) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:46:33.113+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:46:33.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:33.116+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:46:33.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:33.350+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:33.361+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:46:33.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:33.485+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:33.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:33.498+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:46:33.521+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.415 seconds
[2025-07-18T10:47:03.916+0000] {processor.py:186} INFO - Started process (PID=1291) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:47:03.917+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:47:03.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:03.919+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:47:04.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:04.105+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:04.114+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:47:04.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:04.221+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:04.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:04.233+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:47:04.255+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.344 seconds
[2025-07-18T10:48:02.258+0000] {processor.py:186} INFO - Started process (PID=201) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:48:02.259+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:48:02.261+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:02.261+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:48:02.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:02.659+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:02.666+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:48:02.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:02.767+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:02.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:02.777+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:48:02.798+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.548 seconds
[2025-07-18T10:48:33.184+0000] {processor.py:186} INFO - Started process (PID=339) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:48:33.185+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:48:33.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:33.188+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:48:33.572+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:33.572+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:33.581+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:48:33.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:33.684+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:33.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:33.693+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:48:33.715+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.536 seconds
[2025-07-18T10:49:04.052+0000] {processor.py:186} INFO - Started process (PID=473) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:49:04.053+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:49:04.056+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.056+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:49:04.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.387+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:04.395+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:49:04.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.494+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:04.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.503+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:49:04.520+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.475 seconds
[2025-07-18T10:49:35.290+0000] {processor.py:186} INFO - Started process (PID=609) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:49:35.291+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:49:35.294+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:35.294+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:49:35.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:35.496+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:35.505+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:49:35.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:35.607+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:35.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:35.620+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:49:35.644+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.359 seconds
[2025-07-18T10:50:05.704+0000] {processor.py:186} INFO - Started process (PID=742) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:50:05.705+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:50:05.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:05.707+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:50:05.925+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:05.925+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:05.935+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:50:06.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.040+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:06.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.054+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:50:06.080+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.382 seconds
[2025-07-18T10:50:36.580+0000] {processor.py:186} INFO - Started process (PID=881) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:50:36.581+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:50:36.583+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:36.583+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:50:36.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:36.785+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:36.794+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:50:36.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:36.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:36.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:36.923+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:50:36.943+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.369 seconds
[2025-07-18T10:51:07.352+0000] {processor.py:186} INFO - Started process (PID=1017) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:51:07.353+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:51:07.355+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:07.355+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:51:07.554+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:07.553+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:07.562+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:51:07.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:07.671+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:07.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:07.683+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:51:07.703+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.357 seconds
[2025-07-18T10:51:37.833+0000] {processor.py:186} INFO - Started process (PID=1154) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:51:37.833+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:51:37.836+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:37.835+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:51:38.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.040+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:38.050+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:51:38.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.156+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:38.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.168+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:51:38.188+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.362 seconds
[2025-07-18T10:52:08.277+0000] {processor.py:186} INFO - Started process (PID=1290) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:52:08.278+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:52:08.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:08.281+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:52:08.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:08.532+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:08.539+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:52:08.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:08.641+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:08.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:08.653+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:52:08.676+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.404 seconds
[2025-07-18T10:52:38.834+0000] {processor.py:186} INFO - Started process (PID=1426) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:52:38.835+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:52:38.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:38.837+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:52:39.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.038+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:39.046+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:52:39.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.162+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:39.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.174+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:52:39.196+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.368 seconds
[2025-07-18T10:53:09.462+0000] {processor.py:186} INFO - Started process (PID=1562) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:53:09.463+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:53:09.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:09.465+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:53:09.667+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:09.667+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:09.675+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:53:09.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:09.782+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:09.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:09.794+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:53:09.817+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.362 seconds
[2025-07-18T10:53:40.006+0000] {processor.py:186} INFO - Started process (PID=1698) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:53:40.007+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:53:40.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.009+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:53:40.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.196+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:40.204+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:53:40.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.295+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:40.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.306+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:53:40.326+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.325 seconds
[2025-07-18T10:54:10.660+0000] {processor.py:186} INFO - Started process (PID=1834) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:54:10.661+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:54:10.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:10.664+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:54:10.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:10.877+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:10.886+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:54:10.992+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:10.992+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:11.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.004+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:54:11.026+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.372 seconds
[2025-07-18T10:54:41.421+0000] {processor.py:186} INFO - Started process (PID=1970) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:54:41.422+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:54:41.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:41.424+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:54:41.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:41.643+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:41.652+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:54:41.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:41.748+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:41.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:41.760+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:54:41.779+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.364 seconds
[2025-07-18T10:55:12.068+0000] {processor.py:186} INFO - Started process (PID=2106) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:55:12.069+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:55:12.071+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.071+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:55:12.265+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.265+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:12.273+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:55:12.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.378+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:12.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.389+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:55:12.408+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.345 seconds
[2025-07-18T10:55:42.952+0000] {processor.py:186} INFO - Started process (PID=2242) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:55:42.953+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:55:42.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:42.955+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:55:43.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.144+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:43.153+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:55:43.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.245+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:43.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.257+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:55:43.277+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.331 seconds
[2025-07-18T10:57:26.992+0000] {processor.py:186} INFO - Started process (PID=195) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:57:26.993+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:57:26.996+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:26.995+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:57:27.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.388+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:27.393+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:57:27.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.498+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:27.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.508+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:57:27.527+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.540 seconds
[2025-07-18T10:57:58.067+0000] {processor.py:186} INFO - Started process (PID=339) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:57:58.068+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:57:58.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:58.070+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:57:58.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:58.461+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:58.470+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:57:58.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:58.561+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:58.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:58.570+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:57:58.586+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.525 seconds
[2025-07-18T10:58:28.712+0000] {processor.py:186} INFO - Started process (PID=475) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:58:28.713+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:58:28.715+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:28.714+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:58:29.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:29.045+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:29.054+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:58:29.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:29.138+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:29.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:29.148+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:58:29.165+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.458 seconds
[2025-07-18T10:58:59.785+0000] {processor.py:186} INFO - Started process (PID=611) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:58:59.786+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:58:59.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:59.788+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:58:59.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:59.967+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:59.978+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:59:00.084+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:00.084+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:00.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:00.096+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:59:00.113+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.333 seconds
[2025-07-18T10:59:30.685+0000] {processor.py:186} INFO - Started process (PID=747) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:59:30.686+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T10:59:30.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:30.688+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:59:30.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:30.879+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:30.889+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T10:59:30.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:30.986+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:30.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:30.998+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T10:59:31.019+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.339 seconds
[2025-07-18T11:00:01.230+0000] {processor.py:186} INFO - Started process (PID=883) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:00:01.231+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:00:01.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:01.233+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:00:01.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:01.463+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:01.472+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:00:01.572+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:01.572+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:01.584+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:01.584+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:00:01.606+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.383 seconds
[2025-07-18T11:00:31.694+0000] {processor.py:186} INFO - Started process (PID=1019) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:00:31.695+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:00:31.697+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:31.697+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:00:31.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:31.876+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:31.884+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:00:31.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:31.967+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:31.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:31.976+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:00:31.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.304 seconds
[2025-07-18T11:01:02.408+0000] {processor.py:186} INFO - Started process (PID=1155) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:01:02.409+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:01:02.412+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.412+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:01:02.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.631+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:02.641+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:01:02.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.746+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:02.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.755+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:01:02.774+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.372 seconds
[2025-07-18T11:01:33.020+0000] {processor.py:186} INFO - Started process (PID=1291) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:01:33.021+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:01:33.023+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.023+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:01:33.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.193+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:33.202+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:01:33.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.292+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:33.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.303+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:01:33.323+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.308 seconds
[2025-07-18T11:02:04.293+0000] {processor.py:186} INFO - Started process (PID=1427) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:02:04.294+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:02:04.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.296+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:02:04.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.486+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:04.496+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:02:04.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.591+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:04.603+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.602+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:02:04.622+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.334 seconds
[2025-07-18T11:02:35.177+0000] {processor.py:186} INFO - Started process (PID=1561) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:02:35.178+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:02:35.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.180+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:02:35.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.386+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:35.394+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:02:35.502+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.501+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:35.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.513+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:02:35.532+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.361 seconds
[2025-07-18T11:03:05.680+0000] {processor.py:186} INFO - Started process (PID=1699) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:03:05.681+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:03:05.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:05.683+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:03:05.887+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:05.887+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:05.897+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:03:06.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:06.009+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:06.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:06.020+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:03:06.042+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.368 seconds
[2025-07-18T11:03:36.338+0000] {processor.py:186} INFO - Started process (PID=1835) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:03:36.338+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:03:36.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.340+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:03:36.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.548+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:36.558+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:03:36.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.663+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:36.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.672+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:03:36.692+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.360 seconds
[2025-07-18T11:04:07.075+0000] {processor.py:186} INFO - Started process (PID=1971) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:04:07.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:04:07.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:07.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:04:07.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:07.283+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:07.291+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:04:07.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:07.396+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:07.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:07.406+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:04:07.427+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.358 seconds
[2025-07-18T11:04:37.722+0000] {processor.py:186} INFO - Started process (PID=2107) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:04:37.723+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:04:37.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:37.725+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:04:37.919+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:37.918+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:37.927+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:04:38.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.020+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:38.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.030+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:04:38.049+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.333 seconds
[2025-07-18T11:05:08.220+0000] {processor.py:186} INFO - Started process (PID=2243) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:05:08.222+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:05:08.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:08.225+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:05:08.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:08.446+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:08.456+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:05:08.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:08.569+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:08.582+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:08.581+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:05:08.603+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.390 seconds
[2025-07-18T11:05:38.771+0000] {processor.py:186} INFO - Started process (PID=2379) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:05:38.772+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:05:38.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:38.774+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:05:38.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:38.977+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:38.987+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:05:39.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:39.085+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:39.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:39.095+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:05:39.117+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.353 seconds
[2025-07-18T11:06:49.549+0000] {processor.py:186} INFO - Started process (PID=207) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:06:49.550+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:06:49.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:49.552+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:06:49.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:49.959+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:49.965+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:06:50.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.050+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:50.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.058+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:06:50.072+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.528 seconds
[2025-07-18T11:07:20.631+0000] {processor.py:186} INFO - Started process (PID=350) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:07:20.633+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:07:20.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:20.635+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:07:21.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:21.054+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:21.062+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:07:21.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:21.177+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:21.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:21.186+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:07:21.213+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.589 seconds
[2025-07-18T11:07:52.188+0000] {processor.py:186} INFO - Started process (PID=491) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:07:52.190+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:07:52.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:52.193+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:07:52.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:52.545+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:52.552+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:07:52.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:52.642+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:52.652+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:52.652+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:07:52.672+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.492 seconds
[2025-07-18T11:08:23.291+0000] {processor.py:186} INFO - Started process (PID=638) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:08:23.292+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:08:23.294+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.294+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:08:23.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.505+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:23.514+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:08:23.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.613+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:23.625+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.624+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:08:23.648+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.363 seconds
[2025-07-18T11:08:54.182+0000] {processor.py:186} INFO - Started process (PID=779) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:08:54.183+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:08:54.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.185+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:08:54.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.391+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:54.402+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:08:54.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.505+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:54.517+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.517+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:08:54.550+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.373 seconds
[2025-07-18T11:09:24.741+0000] {processor.py:186} INFO - Started process (PID=920) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:09:24.741+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:09:24.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:24.744+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:09:24.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:24.934+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:24.944+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:09:25.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.034+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:25.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.044+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:09:25.063+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.328 seconds
[2025-07-18T11:09:55.329+0000] {processor.py:186} INFO - Started process (PID=1061) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:09:55.329+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:09:55.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.331+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:09:55.517+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.517+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:55.525+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:09:55.616+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.616+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:55.626+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.626+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:09:55.644+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.322 seconds
[2025-07-18T11:10:26.266+0000] {processor.py:186} INFO - Started process (PID=1202) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:10:26.267+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:10:26.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.269+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:10:26.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.443+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:26.452+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:10:26.535+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.535+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:26.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.546+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:10:26.567+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.306 seconds
[2025-07-18T11:10:57.236+0000] {processor.py:186} INFO - Started process (PID=1343) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:10:57.237+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:10:57.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.239+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:10:57.435+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.435+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:57.443+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:10:57.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.537+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:57.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.548+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:10:57.565+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.335 seconds
[2025-07-18T11:11:27.792+0000] {processor.py:186} INFO - Started process (PID=1484) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:11:27.793+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:11:27.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:27.795+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:11:27.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:27.968+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:27.976+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:11:28.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.061+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:28.071+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.070+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:11:28.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.301 seconds
[2025-07-18T11:11:58.899+0000] {processor.py:186} INFO - Started process (PID=1623) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:11:58.901+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:11:58.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:58.903+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:11:59.101+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.100+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:59.109+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:11:59.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.205+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:59.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.215+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:11:59.232+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.341 seconds
[2025-07-18T11:12:29.476+0000] {processor.py:186} INFO - Started process (PID=1766) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:12:29.477+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:12:29.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:29.478+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:12:29.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:29.677+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:29.686+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:12:29.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:29.784+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:29.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:29.795+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:12:29.815+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.345 seconds
[2025-07-18T11:13:00.822+0000] {processor.py:186} INFO - Started process (PID=1907) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:13:00.823+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:13:00.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:00.825+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:13:01.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:01.036+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:01.045+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:13:01.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:01.145+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:01.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:01.156+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:13:01.175+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.359 seconds
[2025-07-18T11:13:31.712+0000] {processor.py:186} INFO - Started process (PID=2048) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:13:31.713+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:13:31.715+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:31.714+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:13:31.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:31.916+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:31.923+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:13:32.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:32.011+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:32.022+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:32.021+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:13:32.039+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.333 seconds
[2025-07-18T11:14:02.422+0000] {processor.py:186} INFO - Started process (PID=2189) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:14:02.423+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:14:02.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:02.426+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:14:02.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:02.624+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:02.633+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:14:02.729+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:02.728+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:02.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:02.741+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:14:02.762+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.348 seconds
[2025-07-18T11:14:32.999+0000] {processor.py:186} INFO - Started process (PID=2330) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:14:33.000+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:14:33.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:33.002+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:14:33.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:33.194+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:33.202+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:14:33.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:33.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:33.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:33.310+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:14:33.331+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.338 seconds
[2025-07-18T11:15:04.048+0000] {processor.py:186} INFO - Started process (PID=2471) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:15:04.049+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:15:04.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.052+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:15:04.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.262+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:04.272+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:15:04.375+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.375+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:04.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.386+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:15:04.405+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.364 seconds
[2025-07-18T11:15:34.920+0000] {processor.py:186} INFO - Started process (PID=2612) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:15:34.921+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:15:34.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:34.923+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:15:35.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:35.121+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:35.129+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:15:35.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:35.230+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:35.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:35.242+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:15:35.263+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.349 seconds
[2025-07-18T11:16:05.537+0000] {processor.py:186} INFO - Started process (PID=2753) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:16:05.538+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:16:05.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.540+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:16:05.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.730+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:05.740+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:16:05.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.838+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:05.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.847+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:16:05.868+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.336 seconds
[2025-07-18T11:16:36.308+0000] {processor.py:186} INFO - Started process (PID=2894) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:16:36.309+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:16:36.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.312+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:16:36.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.523+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:36.531+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:16:36.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.634+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:36.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.645+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:16:36.665+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.363 seconds
[2025-07-18T11:17:07.180+0000] {processor.py:186} INFO - Started process (PID=3035) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:17:07.181+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:17:07.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:07.183+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:17:07.403+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:07.402+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:07.412+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:17:07.554+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:07.553+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:07.571+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:07.570+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:17:07.594+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.420 seconds
[2025-07-18T11:17:37.667+0000] {processor.py:186} INFO - Started process (PID=3176) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:17:37.668+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:17:37.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:37.671+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:17:37.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:37.874+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:37.883+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:17:37.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:37.979+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:37.992+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:37.991+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:17:38.012+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.351 seconds
[2025-07-18T11:18:08.336+0000] {processor.py:186} INFO - Started process (PID=3317) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:18:08.337+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:18:08.339+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:08.339+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:18:08.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:08.546+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:08.555+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:18:08.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:08.660+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:08.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:08.673+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:18:08.695+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.366 seconds
[2025-07-18T11:18:39.266+0000] {processor.py:186} INFO - Started process (PID=3457) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:18:39.267+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:18:39.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:39.269+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:18:39.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:39.476+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:39.485+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:18:39.588+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:39.588+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:39.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:39.598+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:18:39.619+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.359 seconds
[2025-07-18T11:19:10.584+0000] {processor.py:186} INFO - Started process (PID=3599) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:19:10.585+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:19:10.588+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:10.587+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:19:10.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:10.841+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:10.850+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:19:10.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:10.955+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:10.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:10.966+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:19:10.988+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.410 seconds
[2025-07-18T11:19:41.243+0000] {processor.py:186} INFO - Started process (PID=3745) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:19:41.244+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:19:41.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.247+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:19:41.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.442+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:41.451+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:19:41.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.547+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:41.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.558+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:19:41.578+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.340 seconds
[2025-07-18T11:20:11.692+0000] {processor.py:186} INFO - Started process (PID=3886) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:20:11.693+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:20:11.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:11.695+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:20:11.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:11.897+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:11.905+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:20:12.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.004+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:12.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.014+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:20:12.034+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.349 seconds
