[2025-07-18T10:16:50.165+0000] {processor.py:186} INFO - Started process (PID=243) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:16:50.166+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:16:50.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.168+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:16:50.212+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.208+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:50.213+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:16:50.234+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.075 seconds
[2025-07-18T10:17:20.404+0000] {processor.py:186} INFO - Started process (PID=372) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:17:20.405+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:17:20.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.407+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:17:20.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.435+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.442+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:17:20.461+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.062 seconds
[2025-07-18T10:17:50.963+0000] {processor.py:186} INFO - Started process (PID=503) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:17:50.964+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:17:50.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.965+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:17:51.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.124+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:51.129+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:17:51.142+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.186 seconds
[2025-07-18T10:18:21.440+0000] {processor.py:186} INFO - Started process (PID=634) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:18:21.441+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:18:21.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.442+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:18:21.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.472+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.477+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:18:21.496+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.062 seconds
[2025-07-18T10:18:52.402+0000] {processor.py:186} INFO - Started process (PID=767) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:18:52.403+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:18:52.405+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.404+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:18:52.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.436+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:52.440+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:18:52.457+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.059 seconds
[2025-07-18T10:19:23.382+0000] {processor.py:186} INFO - Started process (PID=898) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:19:23.383+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:19:23.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:19:23.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.414+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:23.418+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:19:23.435+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.058 seconds
[2025-07-18T10:19:54.350+0000] {processor.py:186} INFO - Started process (PID=1029) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:19:54.351+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:19:54.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.352+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:19:54.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.382+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:54.386+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:19:54.403+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.059 seconds
[2025-07-18T10:20:25.355+0000] {processor.py:186} INFO - Started process (PID=1160) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:20:25.356+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:20:25.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.357+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:20:25.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.390+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:25.395+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:20:25.411+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.062 seconds
[2025-07-18T10:20:56.303+0000] {processor.py:186} INFO - Started process (PID=1291) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:20:56.304+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:20:56.305+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.305+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:20:56.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.337+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:56.340+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:20:56.358+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.060 seconds
[2025-07-18T10:21:27.287+0000] {processor.py:186} INFO - Started process (PID=1422) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:21:27.288+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:21:27.289+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.289+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:21:27.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.324+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:27.328+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:21:27.344+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.063 seconds
[2025-07-18T10:21:57.456+0000] {processor.py:186} INFO - Started process (PID=1553) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:21:57.457+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:21:57.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.458+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:21:57.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.489+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:57.493+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:21:57.508+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.057 seconds
[2025-07-18T10:22:28.513+0000] {processor.py:186} INFO - Started process (PID=1684) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:22:28.514+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:22:28.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.515+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:22:28.550+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.547+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:28.551+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:22:28.567+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.059 seconds
[2025-07-18T10:22:59.463+0000] {processor.py:186} INFO - Started process (PID=1815) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:22:59.464+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:22:59.465+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.465+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:22:59.502+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.499+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:59.504+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:22:59.521+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.063 seconds
[2025-07-18T10:23:30.420+0000] {processor.py:186} INFO - Started process (PID=1946) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:23:30.421+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:23:30.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.422+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:23:30.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.455+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:30.460+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:23:30.477+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.062 seconds
[2025-07-18T10:24:01.300+0000] {processor.py:186} INFO - Started process (PID=2077) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:24:01.301+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:24:01.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.302+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:24:01.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.336+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:01.341+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:24:01.357+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.063 seconds
[2025-07-18T10:24:31.541+0000] {processor.py:186} INFO - Started process (PID=2208) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:24:31.542+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:24:31.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.543+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:24:31.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.575+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_goals_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_goals_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.579+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:24:31.596+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.061 seconds
[2025-07-18T10:25:04.638+0000] {processor.py:186} INFO - Started process (PID=2339) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:25:04.639+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:25:04.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.641+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:25:04.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.816+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:04.825+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:25:04.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.914+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:04.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.923+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:25:04.943+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.310 seconds
[2025-07-18T10:26:22.594+0000] {processor.py:186} INFO - Started process (PID=242) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:26:22.595+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:26:22.597+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:22.596+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:26:22.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:22.958+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:22.964+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:26:23.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.063+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:23.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.073+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:26:23.090+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.502 seconds
[2025-07-18T10:26:54.773+0000] {processor.py:186} INFO - Started process (PID=372) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:26:54.774+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:26:54.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.777+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:26:55.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.112+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:55.119+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:26:55.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.218+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:55.229+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.229+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:26:55.249+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.481 seconds
[2025-07-18T10:27:25.766+0000] {processor.py:186} INFO - Started process (PID=503) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:27:25.767+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:27:25.770+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:25.770+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:27:26.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.110+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:26.117+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:27:26.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.233+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:26.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.243+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:27:26.262+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.503 seconds
[2025-07-18T10:27:56.672+0000] {processor.py:186} INFO - Started process (PID=634) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:27:56.673+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:27:56.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.675+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:27:56.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.860+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:56.870+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:27:56.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.965+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:56.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.975+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:27:56.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.326 seconds
[2025-07-18T10:28:27.722+0000] {processor.py:186} INFO - Started process (PID=765) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:28:27.723+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:28:27.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:27.725+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:28:27.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:27.912+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:27.922+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:28:28.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.024+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:28.035+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.035+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:28:28.053+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.337 seconds
[2025-07-18T10:28:58.519+0000] {processor.py:186} INFO - Started process (PID=898) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:28:58.520+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:28:58.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.522+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:28:58.710+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.710+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:58.720+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:28:58.842+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.842+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:58.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.857+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:28:58.880+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.367 seconds
[2025-07-18T10:29:29.207+0000] {processor.py:186} INFO - Started process (PID=1029) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:29:29.208+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:29:29.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.210+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:29:29.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.408+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:29.417+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:29:29.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.513+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:29.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.523+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:29:29.542+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.342 seconds
[2025-07-18T10:29:59.645+0000] {processor.py:186} INFO - Started process (PID=1160) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:29:59.645+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:29:59.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.647+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:29:59.832+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.832+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:59.838+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:29:59.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:59.942+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.942+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:29:59.961+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.322 seconds
[2025-07-18T10:30:30.917+0000] {processor.py:186} INFO - Started process (PID=1291) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:30:30.918+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:30:30.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:30.920+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:30:31.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.096+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:31.106+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:30:31.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.196+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:31.207+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.207+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:30:31.225+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.313 seconds
[2025-07-18T10:31:01.469+0000] {processor.py:186} INFO - Started process (PID=1422) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:31:01.470+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:31:01.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:01.472+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:31:01.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:01.668+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:01.677+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:31:01.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:01.770+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:01.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:01.780+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:31:01.798+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.335 seconds
[2025-07-18T10:31:31.987+0000] {processor.py:186} INFO - Started process (PID=1553) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:31:31.987+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:31:31.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:31.989+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:31:32.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.163+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:32.173+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:31:32.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.267+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:32.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.280+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:31:32.299+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.318 seconds
[2025-07-18T10:32:02.603+0000] {processor.py:186} INFO - Started process (PID=1682) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:32:02.604+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:32:02.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:02.606+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:32:02.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:02.825+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:02.835+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:32:02.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:02.942+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:02.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:02.954+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:32:02.975+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.377 seconds
[2025-07-18T10:32:33.061+0000] {processor.py:186} INFO - Started process (PID=1813) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:32:33.062+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:32:33.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.064+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:32:33.261+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.261+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:33.270+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:32:33.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.362+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:33.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.374+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:32:33.393+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.340 seconds
[2025-07-18T10:33:03.537+0000] {processor.py:186} INFO - Started process (PID=1944) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:33:03.538+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:33:03.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:03.540+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:33:03.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:03.722+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:03.730+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:33:03.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:03.859+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:03.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:03.874+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:33:03.891+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.359 seconds
[2025-07-18T10:33:34.167+0000] {processor.py:186} INFO - Started process (PID=2075) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:33:34.168+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:33:34.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.170+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:33:34.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.349+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:34.359+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:33:34.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.455+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:34.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.466+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:33:34.487+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.325 seconds
[2025-07-18T10:34:04.749+0000] {processor.py:186} INFO - Started process (PID=2206) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:34:04.750+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:34:04.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:04.752+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:34:04.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:04.931+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:04.939+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:34:05.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.040+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:05.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.051+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:34:05.073+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.329 seconds
[2025-07-18T10:34:35.825+0000] {processor.py:186} INFO - Started process (PID=2337) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:34:35.826+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:34:35.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:35.828+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:34:36.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.023+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:36.032+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:34:36.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.128+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:36.141+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.140+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:34:36.161+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.341 seconds
[2025-07-18T10:35:06.334+0000] {processor.py:186} INFO - Started process (PID=2468) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:35:06.335+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:35:06.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:06.337+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:35:06.517+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:06.517+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:06.526+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:35:06.626+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:06.626+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:06.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:06.635+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:35:06.652+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.324 seconds
[2025-07-18T10:35:37.327+0000] {processor.py:186} INFO - Started process (PID=2599) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:35:37.328+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:35:37.330+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:37.330+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:35:37.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:37.547+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:37.555+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:35:37.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:37.653+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:37.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:37.663+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:35:37.683+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.362 seconds
[2025-07-18T10:36:08.110+0000] {processor.py:186} INFO - Started process (PID=2730) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:36:08.111+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:36:08.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.113+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:36:08.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.312+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:08.320+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:36:08.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.418+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:08.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.429+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:36:08.446+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.342 seconds
[2025-07-18T10:36:38.740+0000] {processor.py:186} INFO - Started process (PID=2861) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:36:38.741+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:36:38.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:38.743+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:36:38.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:38.927+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:38.937+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:36:39.033+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.032+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:39.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.042+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:36:39.063+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.328 seconds
[2025-07-18T10:37:09.573+0000] {processor.py:186} INFO - Started process (PID=2992) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:37:09.574+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:37:09.577+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:09.577+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:37:09.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:09.784+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:09.793+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:37:09.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:09.901+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:09.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:09.913+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:37:09.933+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.366 seconds
[2025-07-18T10:37:40.484+0000] {processor.py:186} INFO - Started process (PID=3123) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:37:40.485+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:37:40.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:40.487+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:37:40.674+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:40.673+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:40.682+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:37:40.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:40.771+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:40.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:40.782+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:37:40.802+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.323 seconds
[2025-07-18T10:38:10.954+0000] {processor.py:186} INFO - Started process (PID=3254) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:38:10.955+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:38:10.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:10.957+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:38:11.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:11.159+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:11.168+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:38:11.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:11.269+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:11.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:11.281+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:38:11.299+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.352 seconds
[2025-07-18T10:38:41.913+0000] {processor.py:186} INFO - Started process (PID=3385) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:38:41.913+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:38:41.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:41.915+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:38:42.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.108+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:42.117+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:38:42.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.221+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:42.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.233+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:38:42.254+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.347 seconds
[2025-07-18T10:39:12.549+0000] {processor.py:186} INFO - Started process (PID=3516) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:39:12.550+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:39:12.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:12.552+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:39:12.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:12.759+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:12.768+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:39:12.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:12.872+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:12.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:12.884+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:39:12.902+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.359 seconds
[2025-07-18T10:39:43.275+0000] {processor.py:186} INFO - Started process (PID=3647) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:39:43.276+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:39:43.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:43.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:39:43.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:43.476+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:43.485+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:39:43.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:43.596+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:43.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:43.608+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:39:43.628+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.359 seconds
[2025-07-18T10:40:13.968+0000] {processor.py:186} INFO - Started process (PID=3778) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:40:13.969+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:40:13.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:13.971+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:40:14.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.231+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:14.239+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:40:14.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.351+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:14.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.362+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:40:14.379+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.417 seconds
[2025-07-18T10:40:44.542+0000] {processor.py:186} INFO - Started process (PID=3909) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:40:44.543+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:40:44.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:44.545+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:40:44.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:44.728+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:44.737+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:40:44.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:44.825+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:44.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:44.837+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:40:44.857+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.321 seconds
[2025-07-18T10:41:15.236+0000] {processor.py:186} INFO - Started process (PID=4040) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:41:15.237+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:41:15.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:15.239+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:41:15.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:15.443+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:15.451+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:41:15.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:15.553+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:15.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:15.564+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:41:15.583+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.355 seconds
[2025-07-18T10:41:45.861+0000] {processor.py:186} INFO - Started process (PID=4176) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:41:45.862+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:41:45.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:45.865+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:41:46.067+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:46.067+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:46.076+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:41:46.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:46.169+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:46.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:46.179+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:41:46.201+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.346 seconds
[2025-07-18T10:42:16.905+0000] {processor.py:186} INFO - Started process (PID=4312) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:42:16.906+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:42:16.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:16.909+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:42:17.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:17.136+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:17.148+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:42:17.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:17.267+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:17.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:17.283+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:42:17.311+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.415 seconds
[2025-07-18T10:42:59.252+0000] {processor.py:186} INFO - Started process (PID=246) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:42:59.253+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:42:59.255+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.255+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:42:59.588+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.588+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:59.596+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:42:59.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.684+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:59.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.694+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:42:59.715+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.469 seconds
[2025-07-18T10:43:31.425+0000] {processor.py:186} INFO - Started process (PID=382) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:43:31.426+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:43:31.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:31.427+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:43:31.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:31.752+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:31.758+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:43:31.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:31.849+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:31.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:31.858+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:43:31.874+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.454 seconds
[2025-07-18T10:44:01.960+0000] {processor.py:186} INFO - Started process (PID=518) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:44:01.961+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:44:01.963+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:01.962+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:44:02.305+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:02.305+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:02.313+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:44:02.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:02.409+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:02.420+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:02.420+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:44:02.438+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.484 seconds
[2025-07-18T10:44:33.449+0000] {processor.py:186} INFO - Started process (PID=654) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:44:33.450+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:44:33.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:33.453+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:44:33.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:33.669+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:33.680+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:44:33.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:33.778+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:33.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:33.789+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:44:33.808+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.366 seconds
[2025-07-18T10:45:03.928+0000] {processor.py:186} INFO - Started process (PID=790) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:45:03.929+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:45:03.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:03.931+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:45:04.125+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:04.125+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:04.133+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:45:04.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:04.245+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:04.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:04.256+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:45:04.277+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.355 seconds
[2025-07-18T10:45:34.553+0000] {processor.py:186} INFO - Started process (PID=926) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:45:34.554+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:45:34.556+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:34.556+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:45:34.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:34.759+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:34.768+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:45:34.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:34.869+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:34.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:34.881+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:45:34.903+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.355 seconds
[2025-07-18T10:46:05.473+0000] {processor.py:186} INFO - Started process (PID=1062) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:46:05.474+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:46:05.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:05.476+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:46:05.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:05.684+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:05.692+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:46:05.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:05.802+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:05.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:05.816+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:46:05.837+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.370 seconds
[2025-07-18T10:46:36.393+0000] {processor.py:186} INFO - Started process (PID=1198) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:46:36.394+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:46:36.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:36.396+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:46:36.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:36.636+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:36.646+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:46:36.764+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:36.764+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:36.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:36.776+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:46:36.799+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.413 seconds
[2025-07-18T10:47:06.900+0000] {processor.py:186} INFO - Started process (PID=1334) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:47:06.901+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:47:06.903+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:06.903+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:47:07.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.103+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:07.112+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:47:07.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.216+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:07.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.230+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:47:07.251+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.358 seconds
[2025-07-18T10:48:04.663+0000] {processor.py:186} INFO - Started process (PID=246) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:48:04.664+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:48:04.666+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.666+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:48:05.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.000+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:05.008+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:48:05.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.106+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:05.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.116+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:48:05.137+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.480 seconds
[2025-07-18T10:48:35.802+0000] {processor.py:186} INFO - Started process (PID=382) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:48:35.803+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:48:35.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.805+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:48:36.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.145+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:36.153+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:48:36.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.248+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:36.258+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.257+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:48:36.276+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.480 seconds
[2025-07-18T10:49:06.609+0000] {processor.py:186} INFO - Started process (PID=520) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:49:06.610+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:49:06.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:06.612+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:49:06.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:06.944+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:06.951+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:49:07.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:07.053+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:07.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:07.062+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:49:07.081+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.477 seconds
[2025-07-18T10:49:37.896+0000] {processor.py:186} INFO - Started process (PID=656) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:49:37.897+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:49:37.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.899+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:49:38.093+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.093+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:38.102+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:49:38.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.230+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:38.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.248+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:49:38.278+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.387 seconds
[2025-07-18T10:50:08.651+0000] {processor.py:186} INFO - Started process (PID=792) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:50:08.652+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:50:08.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.654+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:50:08.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.848+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:08.858+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:50:08.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.958+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:08.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.968+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:50:08.986+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.342 seconds
[2025-07-18T10:50:39.386+0000] {processor.py:186} INFO - Started process (PID=928) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:50:39.386+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:50:39.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.388+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:50:39.588+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.588+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:39.598+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:50:39.698+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.697+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:39.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.708+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:50:39.728+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.348 seconds
[2025-07-18T10:51:10.112+0000] {processor.py:186} INFO - Started process (PID=1064) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:51:10.113+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:51:10.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.116+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:51:10.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.311+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:10.320+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:51:10.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.420+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:10.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.429+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:51:10.449+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.344 seconds
[2025-07-18T10:51:40.617+0000] {processor.py:186} INFO - Started process (PID=1201) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:51:40.618+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:51:40.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.620+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:51:40.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.809+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:40.819+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:51:40.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.916+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:40.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.926+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:51:40.948+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.336 seconds
[2025-07-18T10:52:11.050+0000] {processor.py:186} INFO - Started process (PID=1337) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:52:11.051+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:52:11.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:11.054+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:52:11.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:11.236+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:11.245+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:52:11.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:11.340+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:11.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:11.350+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:52:11.368+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.323 seconds
[2025-07-18T10:52:41.699+0000] {processor.py:186} INFO - Started process (PID=1473) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:52:41.700+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:52:41.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:41.702+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:52:41.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:41.897+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:41.906+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:52:42.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.006+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:42.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.017+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:52:42.039+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.347 seconds
[2025-07-18T10:53:12.253+0000] {processor.py:186} INFO - Started process (PID=1609) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:53:12.254+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:53:12.256+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.255+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:53:12.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.447+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:12.457+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:53:12.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.551+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:12.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.561+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:53:12.581+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.335 seconds
[2025-07-18T10:53:43.202+0000] {processor.py:186} INFO - Started process (PID=1745) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:53:43.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:53:43.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.205+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:53:43.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.394+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:43.403+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:53:43.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.496+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:43.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.508+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:53:43.527+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.330 seconds
[2025-07-18T10:54:14.189+0000] {processor.py:186} INFO - Started process (PID=1881) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:54:14.190+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:54:14.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.192+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:54:14.395+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.395+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:14.405+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:54:14.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.512+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:14.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.525+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:54:14.549+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.366 seconds
[2025-07-18T10:54:44.773+0000] {processor.py:186} INFO - Started process (PID=2017) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:54:44.774+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:54:44.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.776+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:54:44.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.971+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:44.982+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:54:45.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.079+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:45.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.090+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:54:45.109+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.342 seconds
[2025-07-18T10:55:15.266+0000] {processor.py:186} INFO - Started process (PID=2153) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:55:15.267+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:55:15.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.269+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:55:15.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.449+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:15.459+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:55:15.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.546+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:15.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.557+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:55:15.577+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.316 seconds
[2025-07-18T10:55:45.806+0000] {processor.py:186} INFO - Started process (PID=2287) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:55:45.807+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:55:45.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:45.809+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:55:46.006+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.006+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:46.016+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:55:46.118+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.117+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:46.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.128+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:55:46.146+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.347 seconds
[2025-07-18T10:57:29.342+0000] {processor.py:186} INFO - Started process (PID=246) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:57:29.343+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:57:29.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.345+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:57:29.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.657+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:29.663+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:57:29.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.752+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:29.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.762+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:57:29.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.447 seconds
[2025-07-18T10:58:00.779+0000] {processor.py:186} INFO - Started process (PID=382) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:58:00.780+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:58:00.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.782+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:58:01.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.121+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:01.128+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:58:01.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.223+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:01.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.234+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:58:01.250+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.476 seconds
[2025-07-18T10:58:31.824+0000] {processor.py:186} INFO - Started process (PID=518) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:58:31.825+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:58:31.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.826+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:58:32.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:32.201+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:32.209+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:58:32.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:32.302+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:32.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:32.311+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:58:32.329+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.511 seconds
[2025-07-18T10:59:02.632+0000] {processor.py:186} INFO - Started process (PID=654) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:59:02.633+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:59:02.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.634+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:59:02.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.824+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:02.834+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:59:02.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.952+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:02.964+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.963+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:59:02.984+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.357 seconds
[2025-07-18T10:59:33.521+0000] {processor.py:186} INFO - Started process (PID=792) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:59:33.522+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T10:59:33.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.524+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:59:33.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.708+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:33.717+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T10:59:33.807+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.806+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:33.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.815+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T10:59:33.832+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.316 seconds
[2025-07-18T11:00:04.298+0000] {processor.py:186} INFO - Started process (PID=928) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:00:04.299+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:00:04.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.301+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:00:04.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.493+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:04.503+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:00:04.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.635+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:04.646+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.646+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:00:04.662+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.369 seconds
[2025-07-18T11:00:35.064+0000] {processor.py:186} INFO - Started process (PID=1064) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:00:35.065+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:00:35.067+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:35.067+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:00:35.255+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:35.255+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:35.262+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:00:35.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:35.356+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:35.367+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:35.366+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:00:35.385+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.327 seconds
[2025-07-18T11:01:05.495+0000] {processor.py:186} INFO - Started process (PID=1200) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:01:05.496+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:01:05.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.498+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:01:05.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.677+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:05.684+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:01:05.770+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.769+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:05.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.779+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:01:05.796+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.307 seconds
[2025-07-18T11:01:35.862+0000] {processor.py:186} INFO - Started process (PID=1334) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:01:35.863+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:01:35.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.865+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:01:36.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:36.047+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:36.056+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:01:36.147+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:36.147+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:36.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:36.157+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:01:36.175+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.318 seconds
[2025-07-18T11:02:07.187+0000] {processor.py:186} INFO - Started process (PID=1470) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:02:07.188+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:02:07.190+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:07.190+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:02:07.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:07.397+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:07.406+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:02:07.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:07.516+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:07.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:07.527+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:02:07.549+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.368 seconds
[2025-07-18T11:02:38.205+0000] {processor.py:186} INFO - Started process (PID=1608) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:02:38.206+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:02:38.209+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:38.208+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:02:38.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:38.398+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:38.407+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:02:38.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:38.496+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:38.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:38.507+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:02:38.525+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.326 seconds
[2025-07-18T11:03:09.326+0000] {processor.py:186} INFO - Started process (PID=1742) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:03:09.327+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:03:09.330+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.329+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:03:09.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.545+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:09.554+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:03:09.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.684+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:09.698+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.698+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:03:09.717+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.396 seconds
[2025-07-18T11:03:40.429+0000] {processor.py:186} INFO - Started process (PID=1880) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:03:40.431+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:03:40.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:40.433+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:03:40.640+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:40.640+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:40.649+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:03:40.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:40.754+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:40.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:40.767+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:03:40.788+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.364 seconds
[2025-07-18T11:04:11.168+0000] {processor.py:186} INFO - Started process (PID=2016) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:04:11.169+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:04:11.171+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.171+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:04:11.382+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.382+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:11.392+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:04:11.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.493+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:11.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.504+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:04:11.525+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.363 seconds
[2025-07-18T11:04:41.859+0000] {processor.py:186} INFO - Started process (PID=2152) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:04:41.860+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:04:41.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:41.862+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:04:42.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:42.094+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:42.103+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:04:42.199+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:42.198+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:42.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:42.210+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:04:42.228+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.376 seconds
[2025-07-18T11:05:12.572+0000] {processor.py:186} INFO - Started process (PID=2288) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:05:12.573+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:05:12.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:12.575+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:05:12.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:12.809+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:12.817+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:05:12.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:12.923+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:12.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:12.934+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:05:12.955+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.390 seconds
[2025-07-18T11:06:51.953+0000] {processor.py:186} INFO - Started process (PID=252) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:06:51.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:06:51.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.956+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:06:52.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.329+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:52.335+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:06:52.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.426+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:52.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.438+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:06:52.459+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.511 seconds
[2025-07-18T11:07:23.337+0000] {processor.py:186} INFO - Started process (PID=399) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:07:23.338+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:07:23.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.340+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:07:23.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.680+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:23.688+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:07:23.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.798+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:23.807+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.807+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:07:23.824+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.493 seconds
[2025-07-18T11:07:54.802+0000] {processor.py:186} INFO - Started process (PID=540) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:07:54.804+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:07:54.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.806+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:07:55.015+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.014+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:55.030+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:07:55.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.165+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:55.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.177+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:07:55.195+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.399 seconds
[2025-07-18T11:08:25.711+0000] {processor.py:186} INFO - Started process (PID=681) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:08:25.712+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:08:25.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.716+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:08:25.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.967+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:25.979+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:08:26.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.097+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:26.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.108+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:08:26.127+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.424 seconds
[2025-07-18T11:08:56.233+0000] {processor.py:186} INFO - Started process (PID=824) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:08:56.234+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:08:56.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.237+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:08:56.450+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.450+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:56.457+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:08:56.569+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.568+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:56.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.579+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:08:56.599+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.371 seconds
[2025-07-18T11:09:27.240+0000] {processor.py:186} INFO - Started process (PID=965) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:09:27.241+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:09:27.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.243+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:09:27.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.444+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:27.455+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:09:27.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.552+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:27.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.564+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:09:27.598+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.364 seconds
[2025-07-18T11:09:57.696+0000] {processor.py:186} INFO - Started process (PID=1104) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:09:57.697+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:09:57.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.699+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:09:57.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.912+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:57.922+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:09:58.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.045+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:58.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.058+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:09:58.080+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.390 seconds
[2025-07-18T11:10:28.470+0000] {processor.py:186} INFO - Started process (PID=1245) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:10:28.471+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:10:28.473+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.473+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:10:28.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.660+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:28.671+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:10:28.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.771+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:28.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.781+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:10:28.801+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.337 seconds
[2025-07-18T11:10:59.512+0000] {processor.py:186} INFO - Started process (PID=1386) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:10:59.513+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:10:59.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.515+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:10:59.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.732+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:59.742+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:10:59.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.901+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:59.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.914+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:10:59.938+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.432 seconds
[2025-07-18T11:11:30.469+0000] {processor.py:186} INFO - Started process (PID=1529) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:11:30.470+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:11:30.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.472+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:11:30.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.662+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:30.673+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:11:30.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.765+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:30.776+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.776+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:11:30.795+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.331 seconds
[2025-07-18T11:12:01.215+0000] {processor.py:186} INFO - Started process (PID=1670) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:12:01.216+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:12:01.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.218+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:12:01.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.419+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:01.429+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:12:01.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.537+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:01.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.551+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:12:01.574+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.366 seconds
[2025-07-18T11:12:31.713+0000] {processor.py:186} INFO - Started process (PID=1811) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:12:31.714+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:12:31.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.716+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:12:31.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.898+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:31.905+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:12:31.999+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.999+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:32.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.010+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:12:32.030+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.322 seconds
[2025-07-18T11:13:03.505+0000] {processor.py:186} INFO - Started process (PID=1950) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:13:03.506+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:13:03.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:03.508+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:13:03.758+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:03.758+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:03.766+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:13:03.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:03.858+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:03.867+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:03.867+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:13:03.884+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.385 seconds
[2025-07-18T11:13:34.339+0000] {processor.py:186} INFO - Started process (PID=2093) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:13:34.340+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:13:34.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:34.342+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:13:34.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:34.533+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:34.544+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:13:34.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:34.648+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:34.660+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:34.660+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:13:34.682+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.348 seconds
[2025-07-18T11:14:04.955+0000] {processor.py:186} INFO - Started process (PID=2232) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:14:04.956+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:14:04.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.958+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:14:05.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.166+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:05.175+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:14:05.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.277+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:05.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.288+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:14:05.308+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.360 seconds
[2025-07-18T11:14:35.437+0000] {processor.py:186} INFO - Started process (PID=2373) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:14:35.438+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:14:35.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:35.441+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:14:35.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:35.649+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:35.660+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:14:35.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:35.772+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:35.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:35.783+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:14:35.804+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.373 seconds
[2025-07-18T11:15:06.187+0000] {processor.py:186} INFO - Started process (PID=2514) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:15:06.188+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:15:06.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.190+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:15:06.402+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.401+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:06.411+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:15:06.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.535+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:06.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.550+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:15:06.571+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.391 seconds
[2025-07-18T11:15:37.464+0000] {processor.py:186} INFO - Started process (PID=2657) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:15:37.465+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:15:37.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.467+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:15:37.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.705+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:37.717+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:15:37.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.819+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:37.831+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.831+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:15:37.848+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.391 seconds
[2025-07-18T11:16:08.086+0000] {processor.py:186} INFO - Started process (PID=2798) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:16:08.087+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:16:08.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.089+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:16:08.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.312+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:08.321+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:16:08.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.429+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:08.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.445+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:16:08.470+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.390 seconds
[2025-07-18T11:16:38.701+0000] {processor.py:186} INFO - Started process (PID=2939) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:16:38.702+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:16:38.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:38.704+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:16:38.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:38.899+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:38.908+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:16:39.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.013+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:39.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.025+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:16:39.044+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.349 seconds
[2025-07-18T11:17:10.132+0000] {processor.py:186} INFO - Started process (PID=3080) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:17:10.132+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:17:10.136+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.135+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:17:10.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.348+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:10.357+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:17:10.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.486+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:10.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.499+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:17:10.523+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.399 seconds
[2025-07-18T11:17:40.927+0000] {processor.py:186} INFO - Started process (PID=3221) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:17:40.928+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:17:40.930+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:40.930+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:17:41.147+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:41.147+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:41.158+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:17:41.264+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:41.264+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:41.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:41.275+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:17:41.296+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.376 seconds
[2025-07-18T11:18:11.649+0000] {processor.py:186} INFO - Started process (PID=3362) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:18:11.650+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:18:11.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.653+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:18:11.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.873+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:11.882+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:18:11.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.985+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:11.996+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.996+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:18:12.018+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.376 seconds
[2025-07-18T11:18:42.352+0000] {processor.py:186} INFO - Started process (PID=3503) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:18:42.353+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:18:42.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.355+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:18:42.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.562+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:42.573+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:18:42.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.675+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:42.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.687+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:18:42.707+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.362 seconds
[2025-07-18T11:19:13.334+0000] {processor.py:186} INFO - Started process (PID=3644) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:19:13.335+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:19:13.339+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:13.338+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:19:13.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:13.614+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:13.626+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:19:13.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:13.761+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:13.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:13.771+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:19:13.790+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.463 seconds
[2025-07-18T11:19:44.366+0000] {processor.py:186} INFO - Started process (PID=3790) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:19:44.367+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:19:44.371+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.370+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:19:44.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.578+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:44.586+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:19:44.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.683+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:44.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.694+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:19:44.715+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.359 seconds
[2025-07-18T11:20:15.186+0000] {processor.py:186} INFO - Started process (PID=3931) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:20:15.187+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:20:15.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.188+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:20:15.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.388+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:15.398+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:20:15.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.497+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:15.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.508+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:20:15.527+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.348 seconds
