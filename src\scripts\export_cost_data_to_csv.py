#!/usr/bin/env python3
"""
Простой скрипт для экспорта данных о затратах из Redis в CSV файл
Включает детальную информацию по каждой операции
"""

import redis
import json
import csv
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any
import argparse


def connect_to_redis():
    """Подключение к Redis"""
    try:
        redis_host = os.getenv('REDIS_HOST', 'redis')
        r = redis.Redis(host=redis_host, port=6379, db=0, decode_responses=True)
        r.ping()
        print("✅ Подключение к Redis успешно")
        return r
    except Exception as e:
        print(f"❌ Ошибка подключения к Redis: {e}")
        return None


def get_cost_data_from_redis(r: redis.Redis, days: int = 30) -> List[Dict[str, Any]]:
    """
    Получает детальные данные о затратах из Redis
    
    Args:
        r: Клиент Redis
        days: Количество дней для выборки
        
    Returns:
        Список словарей с данными о затратах
    """
    cost_data = []
    cutoff_date = datetime.now() - timedelta(days=days)
    
    print(f"📊 Получение данных за последние {days} дней...")
    
    # Получаем все ключи cost_tracking
    cost_keys = []
    for key in r.scan_iter("cost_tracking:*"):
        cost_keys.append(key)
    
    print(f"🔍 Найдено {len(cost_keys)} записей в Redis")
    
    for key in cost_keys:
        try:
            data = json.loads(r.get(key))
            
            # Проверяем дату записи
            timestamp_str = data.get('timestamp', '')
            if timestamp_str:
                try:
                    record_date = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00').replace('+00:00', ''))
                    if record_date < cutoff_date:
                        continue
                except:
                    pass
            
            # Нормализуем данные
            normalized_data = {
                'timestamp': data.get('timestamp', ''),
                'dag_id': data.get('dag_id', 'unknown'),
                'task_id': data.get('task_id', 'unknown'),
                'run_id': data.get('run_id', ''),
                'model': data.get('model', 'unknown'),
                'tokens_input': data.get('tokens_input', 0),
                'tokens_output': data.get('tokens_output', 0),
                'total_tokens': data.get('total_tokens', data.get('tokens_input', 0) + data.get('tokens_output', 0)),
                'cost_input': data.get('cost_input', 0.0),
                'cost_output': data.get('cost_output', 0.0),
                'total_cost': data.get('total_cost', 0.0),
                'user_id': data.get('user_id', ''),
                'redis_key': key
            }
            
            cost_data.append(normalized_data)
            
        except Exception as e:
            print(f"⚠️ Ошибка обработки ключа {key}: {e}")
            continue
    
    # Сортируем по времени (новые сначала)
    cost_data.sort(key=lambda x: x['timestamp'], reverse=True)
    
    print(f"✅ Обработано {len(cost_data)} записей")
    return cost_data


def export_to_csv(cost_data: List[Dict[str, Any]], filename: str = None) -> str:
    """Экспортирует данные в CSV файл"""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"cost_data_detailed_{timestamp}.csv"
    
    fieldnames = [
        'timestamp',
        'dag_id', 
        'task_id',
        'run_id',
        'model',
        'tokens_input',
        'tokens_output', 
        'total_tokens',
        'cost_input',
        'cost_output',
        'total_cost',
        'user_id'
    ]
    
    print(f"💾 Экспорт в CSV: {filename}")
    
    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for row in cost_data:
            # Исключаем redis_key из CSV
            csv_row = {k: v for k, v in row.items() if k != 'redis_key'}
            writer.writerow(csv_row)
    
    print(f"✅ CSV экспорт завершен: {len(cost_data)} записей")
    return filename


def print_summary(cost_data: List[Dict[str, Any]]):
    """Выводит сводную статистику"""
    if not cost_data:
        print("📊 Нет данных для анализа")
        return
    
    total_cost = sum(float(row['total_cost']) for row in cost_data)
    total_tokens_input = sum(int(row['tokens_input']) for row in cost_data)
    total_tokens_output = sum(int(row['tokens_output']) for row in cost_data)
    
    # Статистика по DAG
    dag_stats = {}
    for row in cost_data:
        dag_id = row['dag_id']
        if dag_id not in dag_stats:
            dag_stats[dag_id] = {
                'count': 0,
                'cost': 0.0,
                'tokens_input': 0,
                'tokens_output': 0
            }
        
        dag_stats[dag_id]['count'] += 1
        dag_stats[dag_id]['cost'] += float(row['total_cost'])
        dag_stats[dag_id]['tokens_input'] += int(row['tokens_input'])
        dag_stats[dag_id]['tokens_output'] += int(row['tokens_output'])
    
    print(f"\n📈 СВОДНАЯ СТАТИСТИКА:")
    print("=" * 60)
    print(f"💰 Общая стоимость: ${total_cost:.6f}")
    print(f"🔢 Всего операций: {len(cost_data)}")
    print(f"📥 Входные токены: {total_tokens_input:,}")
    print(f"📤 Выходные токены: {total_tokens_output:,}")
    print(f"📊 Всего токенов: {total_tokens_input + total_tokens_output:,}")
    
    print(f"\n📋 СТАТИСТИКА ПО DAG:")
    print("-" * 60)
    for dag_id, stats in sorted(dag_stats.items(), key=lambda x: x[1]['cost'], reverse=True):
        print(f"  {dag_id}:")
        print(f"    Операций: {stats['count']}")
        print(f"    Стоимость: ${stats['cost']:.6f}")
        print(f"    Входные токены: {stats['tokens_input']:,}")
        print(f"    Выходные токены: {stats['tokens_output']:,}")
        print()


def main():
    """Основная функция"""
    parser = argparse.ArgumentParser(description='Экспорт детальных данных о затратах из Redis в CSV')
    parser.add_argument('--days', type=int, default=30, help='Количество дней для выборки (по умолчанию 30)')
    parser.add_argument('--output', type=str, help='Имя выходного файла CSV')
    parser.add_argument('--summary', action='store_true', help='Показать только сводную статистику без экспорта')
    
    args = parser.parse_args()
    
    print("🚀 ЭКСПОРТ ДЕТАЛЬНЫХ ДАННЫХ О ЗАТРАТАХ")
    print("=" * 50)
    
    # Подключаемся к Redis
    r = connect_to_redis()
    if not r:
        return
    
    # Получаем данные
    cost_data = get_cost_data_from_redis(r, days=args.days)
    
    if not cost_data:
        print("❌ Нет данных для экспорта")
        return
    
    # Показываем статистику
    print_summary(cost_data)
    
    # Экспортируем в CSV если не только статистика
    if not args.summary:
        try:
            csv_file = export_to_csv(cost_data, args.output)
            print(f"\n🎉 ГОТОВО! Файл сохранен: {csv_file}")
        except Exception as e:
            print(f"\n❌ Ошибка создания CSV: {e}")
    else:
        print(f"\n🎉 ГОТОВО! Показана статистика за {args.days} дней")


if __name__ == "__main__":
    main()
