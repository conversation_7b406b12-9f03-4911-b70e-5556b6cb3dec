[2025-07-18T10:16:49.830+0000] {processor.py:186} INFO - Started process (PID=218) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:16:49.831+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:16:49.833+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.833+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:16:49.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.872+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:49.877+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:16:49.896+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.073 seconds
[2025-07-18T10:17:20.115+0000] {processor.py:186} INFO - Started process (PID=349) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:17:20.116+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:17:20.118+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.118+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:17:20.154+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.151+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.155+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:17:20.173+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.063 seconds
[2025-07-18T10:17:50.498+0000] {processor.py:186} INFO - Started process (PID=480) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:17:50.499+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:17:50.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.500+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:17:50.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.530+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:50.534+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:17:50.551+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.058 seconds
[2025-07-18T10:18:21.155+0000] {processor.py:186} INFO - Started process (PID=611) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:18:21.157+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:18:21.158+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.157+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:18:21.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.191+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.194+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:18:21.209+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.058 seconds
[2025-07-18T10:18:52.145+0000] {processor.py:186} INFO - Started process (PID=742) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:18:52.146+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:18:52.147+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.147+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:18:52.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.176+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:52.180+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:18:52.195+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.055 seconds
[2025-07-18T10:19:23.003+0000] {processor.py:186} INFO - Started process (PID=871) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:19:23.004+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:19:23.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.005+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:19:23.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.042+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:23.046+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:19:23.064+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.067 seconds
[2025-07-18T10:19:54.056+0000] {processor.py:186} INFO - Started process (PID=1004) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:19:54.056+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:19:54.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.057+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:19:54.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.085+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:54.089+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:19:54.104+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.053 seconds
[2025-07-18T10:20:25.086+0000] {processor.py:186} INFO - Started process (PID=1135) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:20:25.087+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:20:25.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.088+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:20:25.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.119+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:25.123+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:20:25.139+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.059 seconds
[2025-07-18T10:20:56.040+0000] {processor.py:186} INFO - Started process (PID=1266) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:20:56.040+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:20:56.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.041+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:20:56.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.070+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:56.074+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:20:56.090+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.057 seconds
[2025-07-18T10:21:27.015+0000] {processor.py:186} INFO - Started process (PID=1397) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:21:27.015+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:21:27.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.017+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:21:27.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.049+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:27.053+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:21:27.070+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.062 seconds
[2025-07-18T10:21:57.177+0000] {processor.py:186} INFO - Started process (PID=1528) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:21:57.178+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:21:57.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.178+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:21:57.209+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.207+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:57.210+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:21:57.225+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.054 seconds
[2025-07-18T10:22:28.222+0000] {processor.py:186} INFO - Started process (PID=1659) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:22:28.223+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:22:28.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.224+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:22:28.265+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.261+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:28.266+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:22:28.284+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.070 seconds
[2025-07-18T10:22:59.204+0000] {processor.py:186} INFO - Started process (PID=1790) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:22:59.204+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:22:59.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.205+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:22:59.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.233+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:59.237+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:22:59.253+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.054 seconds
[2025-07-18T10:23:30.069+0000] {processor.py:186} INFO - Started process (PID=1919) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:23:30.070+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:23:30.071+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.070+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:23:30.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.102+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:30.106+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:23:30.123+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.059 seconds
[2025-07-18T10:24:01.002+0000] {processor.py:186} INFO - Started process (PID=2052) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:24:01.003+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:24:01.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.004+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:24:01.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.036+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:01.040+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:24:01.059+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.062 seconds
[2025-07-18T10:24:31.246+0000] {processor.py:186} INFO - Started process (PID=2183) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:24:31.247+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:24:31.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.248+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:24:31.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.280+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.285+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:24:31.303+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.062 seconds
[2025-07-18T10:25:03.553+0000] {processor.py:186} INFO - Started process (PID=2314) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:25:03.553+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:25:03.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.554+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:25:03.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.727+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:03.737+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:25:03.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.828+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:03.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.838+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:25:03.858+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.311 seconds
[2025-07-18T10:26:21.038+0000] {processor.py:186} INFO - Started process (PID=218) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:26:21.040+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:26:21.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:21.042+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:26:21.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:21.385+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:21.392+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:26:21.486+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:21.485+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:21.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:21.495+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:26:21.518+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.486 seconds
[2025-07-18T10:26:52.212+0000] {processor.py:186} INFO - Started process (PID=347) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:26:52.230+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:26:52.255+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:52.254+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:26:53.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:53.529+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:53.543+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:26:53.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:53.749+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:53.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:53.766+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:26:53.798+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 1.595 seconds
[2025-07-18T10:27:24.070+0000] {processor.py:186} INFO - Started process (PID=480) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:27:24.071+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:27:24.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:24.073+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:27:24.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:24.428+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:24.436+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:27:24.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:24.551+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:24.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:24.563+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:27:24.579+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.515 seconds
[2025-07-18T10:27:55.229+0000] {processor.py:186} INFO - Started process (PID=611) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:27:55.230+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:27:55.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:55.232+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:27:55.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:55.433+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:55.443+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:27:55.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:55.548+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:55.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:55.561+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:27:55.582+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.358 seconds
[2025-07-18T10:28:25.910+0000] {processor.py:186} INFO - Started process (PID=742) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:28:25.911+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:28:25.913+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:25.912+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:28:26.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:26.114+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:26.124+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:28:26.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:26.253+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:26.265+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:26.265+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:28:26.290+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.386 seconds
[2025-07-18T10:28:56.707+0000] {processor.py:186} INFO - Started process (PID=873) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:28:56.707+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:28:56.710+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:56.709+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:28:56.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:56.909+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:56.919+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:28:57.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:57.017+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:57.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:57.029+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:28:57.051+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.350 seconds
[2025-07-18T10:29:27.375+0000] {processor.py:186} INFO - Started process (PID=1004) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:29:27.375+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:29:27.377+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:27.377+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:29:27.591+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:27.590+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:27.600+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:29:27.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:27.708+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:27.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:27.720+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:29:27.740+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.372 seconds
[2025-07-18T10:29:57.833+0000] {processor.py:186} INFO - Started process (PID=1135) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:29:57.834+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:29:57.836+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:57.836+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:29:58.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:58.046+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:58.056+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:29:58.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:58.157+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:58.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:58.167+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:29:58.187+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.360 seconds
[2025-07-18T10:30:28.484+0000] {processor.py:186} INFO - Started process (PID=1266) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:30:28.485+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:30:28.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:28.487+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:30:28.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:28.691+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:28.701+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:30:28.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:28.815+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:28.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:28.827+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:30:28.848+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.370 seconds
[2025-07-18T10:30:59.024+0000] {processor.py:186} INFO - Started process (PID=1397) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:30:59.025+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:30:59.027+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:59.027+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:30:59.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:59.225+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:59.236+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:30:59.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:59.339+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:59.353+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:59.352+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:30:59.374+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.356 seconds
[2025-07-18T10:31:29.552+0000] {processor.py:186} INFO - Started process (PID=1528) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:31:29.553+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:31:29.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:29.555+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:31:29.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:29.760+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:29.770+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:31:29.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:29.872+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:29.883+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:29.883+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:31:29.902+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.355 seconds
[2025-07-18T10:32:00.161+0000] {processor.py:186} INFO - Started process (PID=1659) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:32:00.162+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:32:00.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:00.163+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:32:00.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:00.359+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:00.367+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:32:00.467+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:00.467+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:00.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:00.478+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:32:00.497+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.342 seconds
[2025-07-18T10:32:30.636+0000] {processor.py:186} INFO - Started process (PID=1790) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:32:30.637+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:32:30.639+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:30.638+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:32:30.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:30.824+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:30.832+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:32:30.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:30.929+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:30.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:30.941+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:32:30.962+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.331 seconds
[2025-07-18T10:33:01.086+0000] {processor.py:186} INFO - Started process (PID=1921) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:33:01.087+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:33:01.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:01.089+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:33:01.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:01.278+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:01.288+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:33:01.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:01.388+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:01.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:01.400+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:33:01.421+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.339 seconds
[2025-07-18T10:33:31.729+0000] {processor.py:186} INFO - Started process (PID=2052) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:33:31.730+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:33:31.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:31.732+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:33:31.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:31.923+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:31.933+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:33:32.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:32.030+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:32.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:32.044+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:33:32.064+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.341 seconds
[2025-07-18T10:34:02.283+0000] {processor.py:186} INFO - Started process (PID=2183) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:34:02.284+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:34:02.286+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:02.286+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:34:02.480+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:02.480+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:02.489+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:34:02.595+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:02.594+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:02.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:02.606+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:34:02.628+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.351 seconds
[2025-07-18T10:34:33.404+0000] {processor.py:186} INFO - Started process (PID=2314) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:34:33.405+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:34:33.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:33.407+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:34:33.593+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:33.593+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:33.602+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:34:33.697+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:33.697+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:33.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:33.707+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:34:33.726+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.327 seconds
[2025-07-18T10:35:03.897+0000] {processor.py:186} INFO - Started process (PID=2445) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:35:03.898+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:35:03.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:03.901+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:35:04.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:04.109+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:04.118+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:35:04.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:04.212+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:04.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:04.224+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:35:04.243+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.357 seconds
[2025-07-18T10:35:34.894+0000] {processor.py:186} INFO - Started process (PID=2576) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:35:34.895+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:35:34.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:34.897+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:35:35.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:35.090+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:35.099+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:35:35.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:35.192+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:35.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:35.202+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:35:35.221+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.333 seconds
[2025-07-18T10:36:05.627+0000] {processor.py:186} INFO - Started process (PID=2707) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:36:05.628+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:36:05.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:05.630+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:36:05.842+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:05.842+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:05.850+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:36:05.954+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:05.954+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:05.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:05.969+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:36:05.994+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.374 seconds
[2025-07-18T10:36:36.269+0000] {processor.py:186} INFO - Started process (PID=2838) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:36:36.270+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:36:36.273+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:36.272+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:36:36.469+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:36.469+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:36.478+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:36:36.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:36.586+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:36.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:36.599+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:36:36.623+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.359 seconds
[2025-07-18T10:37:07.101+0000] {processor.py:186} INFO - Started process (PID=2969) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:37:07.102+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:37:07.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:07.104+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:37:07.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:07.316+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:07.324+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:37:07.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:07.426+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:07.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:07.437+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:37:07.456+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.360 seconds
[2025-07-18T10:37:38.032+0000] {processor.py:186} INFO - Started process (PID=3100) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:37:38.033+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:37:38.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:38.036+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:37:38.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:38.238+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:38.248+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:37:38.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:38.344+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:38.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:38.355+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:37:38.373+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.347 seconds
[2025-07-18T10:38:08.859+0000] {processor.py:186} INFO - Started process (PID=3231) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:38:08.860+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:38:08.862+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:08.862+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:38:09.056+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:09.056+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:09.066+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:38:09.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:09.176+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:09.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:09.187+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:38:09.208+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.355 seconds
[2025-07-18T10:38:40.099+0000] {processor.py:186} INFO - Started process (PID=3362) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:38:40.100+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:38:40.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:40.102+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:38:40.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:40.303+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:40.311+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:38:40.412+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:40.411+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:40.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:40.423+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:38:40.444+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.351 seconds
[2025-07-18T10:39:10.756+0000] {processor.py:186} INFO - Started process (PID=3493) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:39:10.757+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:39:10.759+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:10.759+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:39:10.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:10.952+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:10.960+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:39:11.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:11.054+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:11.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:11.066+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:39:11.085+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.334 seconds
[2025-07-18T10:39:41.468+0000] {processor.py:186} INFO - Started process (PID=3624) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:39:41.468+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:39:41.470+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:41.470+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:39:41.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:41.672+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:41.682+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:39:41.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:41.784+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:41.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:41.795+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:39:41.816+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.355 seconds
[2025-07-18T10:40:12.189+0000] {processor.py:186} INFO - Started process (PID=3755) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:40:12.189+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:40:12.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:12.191+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:40:12.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:12.379+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:12.388+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:40:12.486+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:12.486+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:12.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:12.497+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:40:12.516+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.333 seconds
[2025-07-18T10:40:42.762+0000] {processor.py:186} INFO - Started process (PID=3886) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:40:42.763+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:40:42.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:42.765+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:40:42.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:42.946+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:42.955+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:40:43.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:43.050+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:43.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:43.062+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:40:43.081+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.324 seconds
[2025-07-18T10:41:13.145+0000] {processor.py:186} INFO - Started process (PID=4017) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:41:13.146+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:41:13.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:13.149+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:41:13.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:13.345+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:13.355+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:41:13.652+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:13.652+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:13.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:13.664+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:41:13.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.548 seconds
[2025-07-18T10:41:43.823+0000] {processor.py:186} INFO - Started process (PID=4153) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:41:43.824+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:41:43.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:43.826+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:41:44.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:44.034+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:44.042+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:41:44.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:44.360+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:44.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:44.369+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:41:44.386+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.569 seconds
[2025-07-18T10:42:14.755+0000] {processor.py:186} INFO - Started process (PID=4287) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:42:14.757+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:42:14.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:14.759+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:42:14.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:14.971+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:14.980+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:42:15.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:15.268+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:15.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:15.278+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:42:15.293+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.547 seconds
[2025-07-18T10:42:58.010+0000] {processor.py:186} INFO - Started process (PID=221) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:42:58.011+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:42:58.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.014+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:42:58.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.340+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:58.348+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:42:58.432+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.432+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:58.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.442+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:42:58.461+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.457 seconds
[2025-07-18T10:43:29.426+0000] {processor.py:186} INFO - Started process (PID=359) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:43:29.427+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:43:29.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:29.429+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:43:29.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:29.738+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:29.745+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:43:29.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:29.841+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:29.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:29.851+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:43:29.870+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.449 seconds
[2025-07-18T10:43:59.987+0000] {processor.py:186} INFO - Started process (PID=493) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:43:59.988+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:43:59.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.989+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:44:00.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:00.309+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:00.317+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:44:00.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:00.400+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:00.412+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:00.411+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:44:00.426+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.445 seconds
[2025-07-18T10:44:30.659+0000] {processor.py:186} INFO - Started process (PID=631) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:44:30.659+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:44:30.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.661+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:44:30.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.871+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:30.879+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:44:30.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.967+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:30.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.976+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:44:30.994+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.341 seconds
[2025-07-18T10:45:01.730+0000] {processor.py:186} INFO - Started process (PID=767) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:45:01.731+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:45:01.734+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.733+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:45:01.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.926+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:01.935+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:45:02.031+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.031+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:02.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.042+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:45:02.062+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.337 seconds
[2025-07-18T10:45:32.303+0000] {processor.py:186} INFO - Started process (PID=903) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:45:32.304+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:45:32.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:32.307+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:45:32.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:32.524+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:32.534+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:45:32.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:32.631+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:32.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:32.643+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:45:32.662+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.365 seconds
[2025-07-18T10:46:03.667+0000] {processor.py:186} INFO - Started process (PID=1039) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:46:03.668+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:46:03.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.671+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:46:03.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.870+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:03.880+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:46:03.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.973+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:03.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.985+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:46:04.008+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.348 seconds
[2025-07-18T10:46:34.490+0000] {processor.py:186} INFO - Started process (PID=1175) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:46:34.491+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:46:34.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:34.493+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:46:34.715+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:34.714+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:34.725+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:46:34.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:34.826+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:34.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:34.837+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:46:34.862+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.379 seconds
[2025-07-18T10:47:05.084+0000] {processor.py:186} INFO - Started process (PID=1311) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:47:05.085+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:47:05.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.087+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:47:05.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.288+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:05.297+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:47:05.395+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.395+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:05.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.406+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:47:05.427+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.348 seconds
[2025-07-18T10:48:03.343+0000] {processor.py:186} INFO - Started process (PID=221) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:48:03.344+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:48:03.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.346+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:48:03.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.723+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:03.730+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:48:03.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.816+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:03.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.825+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:48:03.848+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.511 seconds
[2025-07-18T10:48:34.312+0000] {processor.py:186} INFO - Started process (PID=357) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:48:34.313+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:48:34.315+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:34.315+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:48:34.667+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:34.667+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:34.673+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:48:34.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:34.780+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:34.791+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:34.791+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:48:34.806+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.500 seconds
[2025-07-18T10:49:05.057+0000] {processor.py:186} INFO - Started process (PID=493) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:49:05.059+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:49:05.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.061+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:49:05.395+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.394+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:05.401+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:49:05.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.498+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:05.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.508+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:49:05.523+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.472 seconds
[2025-07-18T10:49:36.433+0000] {processor.py:186} INFO - Started process (PID=629) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:49:36.433+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:49:36.436+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.435+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:49:36.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.621+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:36.630+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:49:36.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.721+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:36.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.732+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:49:36.751+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.324 seconds
[2025-07-18T10:50:06.965+0000] {processor.py:186} INFO - Started process (PID=765) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:50:06.966+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:50:06.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.968+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:50:07.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:07.169+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:07.178+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:50:07.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:07.279+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:07.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:07.291+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:50:07.313+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.354 seconds
[2025-07-18T10:50:37.769+0000] {processor.py:186} INFO - Started process (PID=903) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:50:37.770+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:50:37.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:37.772+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:50:37.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:37.976+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:37.985+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:50:38.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:38.084+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:38.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:38.096+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:50:38.116+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.353 seconds
[2025-07-18T10:51:08.530+0000] {processor.py:186} INFO - Started process (PID=1039) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:51:08.531+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:51:08.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.533+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:51:08.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.716+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:08.724+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:51:08.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.824+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:08.836+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.835+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:51:08.855+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.332 seconds
[2025-07-18T10:51:39.087+0000] {processor.py:186} INFO - Started process (PID=1176) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:51:39.088+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:51:39.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:39.090+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:51:39.284+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:39.283+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:39.293+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:51:39.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:39.389+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:39.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:39.399+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:51:39.419+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.337 seconds
[2025-07-18T10:52:09.528+0000] {processor.py:186} INFO - Started process (PID=1312) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:52:09.529+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:52:09.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.531+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:52:09.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.733+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:09.741+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:52:09.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.830+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:09.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.841+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:52:09.861+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.341 seconds
[2025-07-18T10:52:40.089+0000] {processor.py:186} INFO - Started process (PID=1448) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:52:40.090+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:52:40.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:40.093+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:52:40.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:40.312+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:40.321+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:52:40.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:40.416+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:40.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:40.427+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:52:40.446+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.365 seconds
[2025-07-18T10:53:10.655+0000] {processor.py:186} INFO - Started process (PID=1584) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:53:10.656+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:53:10.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.658+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:53:10.867+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.867+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:10.877+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:53:10.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.975+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:10.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.986+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:53:11.005+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.356 seconds
[2025-07-18T10:53:41.108+0000] {processor.py:186} INFO - Started process (PID=1720) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:53:41.109+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:53:41.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:41.111+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:53:41.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:41.295+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:41.303+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:53:41.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:41.393+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:41.403+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:41.403+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:53:41.421+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.320 seconds
[2025-07-18T10:54:11.958+0000] {processor.py:186} INFO - Started process (PID=1855) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:54:11.959+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:54:11.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.961+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:54:12.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:12.163+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:12.171+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:54:12.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:12.276+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:12.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:12.288+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:54:12.308+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.356 seconds
[2025-07-18T10:54:42.602+0000] {processor.py:186} INFO - Started process (PID=1992) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:54:42.603+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:54:42.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.605+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:54:42.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.794+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:42.803+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:54:42.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.902+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:42.913+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.913+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:54:42.933+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.337 seconds
[2025-07-18T10:55:13.188+0000] {processor.py:186} INFO - Started process (PID=2128) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:55:13.189+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:55:13.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.191+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:55:13.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.361+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:13.369+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:55:13.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.453+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:13.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.463+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:55:13.480+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.297 seconds
[2025-07-18T10:55:44.028+0000] {processor.py:186} INFO - Started process (PID=2264) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:55:44.029+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:55:44.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:44.031+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:55:44.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:44.204+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:44.213+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:55:44.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:44.294+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:44.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:44.304+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:55:44.324+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.301 seconds
[2025-07-18T10:57:28.069+0000] {processor.py:186} INFO - Started process (PID=221) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:57:28.070+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:57:28.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.073+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:57:28.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.427+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:28.434+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:57:28.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.522+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:28.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.530+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:57:28.549+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.487 seconds
[2025-07-18T10:57:59.659+0000] {processor.py:186} INFO - Started process (PID=359) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:57:59.661+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:57:59.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:59.663+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:58:00.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.028+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:00.034+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:58:00.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.139+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:00.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.155+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:58:00.173+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.521 seconds
[2025-07-18T10:58:30.716+0000] {processor.py:186} INFO - Started process (PID=495) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:58:30.717+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:58:30.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:30.718+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:58:31.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.065+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:31.073+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:58:31.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.184+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:31.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.196+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:58:31.215+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.505 seconds
[2025-07-18T10:59:01.548+0000] {processor.py:186} INFO - Started process (PID=631) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:59:01.549+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:59:01.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:01.551+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:59:01.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:01.739+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:01.748+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:59:01.846+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:01.845+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:01.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:01.856+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:59:01.874+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.332 seconds
[2025-07-18T10:59:32.429+0000] {processor.py:186} INFO - Started process (PID=767) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:59:32.430+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T10:59:32.432+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:32.431+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:59:32.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:32.624+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:32.632+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T10:59:32.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:32.737+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:32.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:32.748+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T10:59:32.767+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.343 seconds
[2025-07-18T11:00:03.061+0000] {processor.py:186} INFO - Started process (PID=903) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:00:03.062+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:00:03.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.064+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:00:03.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.283+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:03.292+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:00:03.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.400+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:03.412+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.412+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:00:03.434+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.380 seconds
[2025-07-18T11:00:33.758+0000] {processor.py:186} INFO - Started process (PID=1039) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:00:33.759+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:00:33.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:33.761+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:00:33.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:33.983+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:33.992+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:00:34.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.090+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:34.101+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.101+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:00:34.121+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.370 seconds
[2025-07-18T11:01:04.294+0000] {processor.py:186} INFO - Started process (PID=1175) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:01:04.295+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:01:04.298+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:04.297+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:01:04.519+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:04.519+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:04.530+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:01:04.660+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:04.659+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:04.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:04.670+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:01:04.695+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.407 seconds
[2025-07-18T11:01:34.777+0000] {processor.py:186} INFO - Started process (PID=1311) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:01:34.778+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:01:34.780+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:34.780+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:01:34.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:34.976+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:34.984+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:01:35.084+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.084+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:35.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.094+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:01:35.110+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.338 seconds
[2025-07-18T11:02:06.078+0000] {processor.py:186} INFO - Started process (PID=1447) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:02:06.079+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:02:06.081+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:06.080+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:02:06.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:06.278+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:06.287+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:02:06.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:06.390+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:06.401+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:06.400+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:02:06.422+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.351 seconds
[2025-07-18T11:02:36.993+0000] {processor.py:186} INFO - Started process (PID=1583) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:02:36.994+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:02:36.996+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:36.996+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:02:37.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:37.195+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:37.204+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:02:37.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:37.312+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:37.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:37.324+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:02:37.347+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.360 seconds
[2025-07-18T11:03:07.525+0000] {processor.py:186} INFO - Started process (PID=1719) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:03:07.525+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:03:07.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:07.528+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:03:07.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:07.739+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:07.747+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:03:07.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:07.849+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:07.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:07.859+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:03:07.881+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.362 seconds
[2025-07-18T11:03:38.172+0000] {processor.py:186} INFO - Started process (PID=1855) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:03:38.173+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:03:38.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:38.175+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:03:38.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:38.396+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:38.407+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:03:38.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:38.516+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:38.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:38.531+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:03:38.550+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.383 seconds
[2025-07-18T11:04:08.983+0000] {processor.py:186} INFO - Started process (PID=1991) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:04:08.987+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:04:08.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:08.991+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:04:09.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:09.238+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:09.247+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:04:09.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:09.366+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:09.380+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:09.380+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:04:09.405+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.431 seconds
[2025-07-18T11:04:39.581+0000] {processor.py:186} INFO - Started process (PID=2127) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:04:39.582+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:04:39.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:39.586+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:04:39.804+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:39.804+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:39.815+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:04:39.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:39.926+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:39.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:39.940+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:04:39.959+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.386 seconds
[2025-07-18T11:05:10.219+0000] {processor.py:186} INFO - Started process (PID=2263) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:05:10.220+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:05:10.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:10.223+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:05:10.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:10.468+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:10.477+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:05:10.605+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:10.604+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:10.618+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:10.618+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:05:10.639+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.426 seconds
[2025-07-18T11:05:41.670+0000] {processor.py:186} INFO - Started process (PID=2399) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:05:41.671+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:05:41.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:41.673+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:05:41.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:41.942+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:41.952+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:05:42.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:42.113+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:42.131+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:42.131+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:05:42.159+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.496 seconds
[2025-07-18T11:06:50.609+0000] {processor.py:186} INFO - Started process (PID=227) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:06:50.610+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:06:50.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.612+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:06:50.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.957+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:50.963+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:06:51.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.050+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:51.059+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.059+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:06:51.079+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.475 seconds
[2025-07-18T11:07:21.824+0000] {processor.py:186} INFO - Started process (PID=368) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:07:21.825+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:07:21.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:21.827+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:07:22.173+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.173+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:22.180+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:07:22.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.277+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:22.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.287+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:07:22.305+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.488 seconds
[2025-07-18T11:07:53.273+0000] {processor.py:186} INFO - Started process (PID=515) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:07:53.274+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:07:53.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.276+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:07:53.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.636+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:53.642+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:07:53.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.737+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:53.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.746+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:07:53.763+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.496 seconds
[2025-07-18T11:08:24.546+0000] {processor.py:186} INFO - Started process (PID=658) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:08:24.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:08:24.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.549+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:08:24.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.762+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:24.772+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:08:24.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.870+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:24.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.882+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:08:24.902+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.361 seconds
[2025-07-18T11:08:55.027+0000] {processor.py:186} INFO - Started process (PID=797) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:08:55.028+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:08:55.031+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.030+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:08:55.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.247+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:55.256+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:08:55.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.363+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:55.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.373+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:08:55.396+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.376 seconds
[2025-07-18T11:09:25.531+0000] {processor.py:186} INFO - Started process (PID=938) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:09:25.532+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:09:25.535+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.534+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:09:25.757+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.757+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:25.772+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:09:25.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:25.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.922+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:09:25.942+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.417 seconds
[2025-07-18T11:09:56.445+0000] {processor.py:186} INFO - Started process (PID=1081) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:09:56.446+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:09:56.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:56.448+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:09:56.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:56.640+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:56.650+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:09:56.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:56.740+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:56.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:56.752+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:09:56.770+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.330 seconds
[2025-07-18T11:10:26.995+0000] {processor.py:186} INFO - Started process (PID=1220) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:10:26.996+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:10:26.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.998+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:10:27.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:27.191+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:27.200+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:10:27.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:27.295+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:27.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:27.306+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:10:27.324+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.335 seconds
[2025-07-18T11:10:58.339+0000] {processor.py:186} INFO - Started process (PID=1363) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:10:58.340+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:10:58.343+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:58.342+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:10:58.539+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:58.539+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:58.547+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:10:58.640+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:58.640+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:58.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:58.651+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:10:58.672+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.340 seconds
[2025-07-18T11:11:28.946+0000] {processor.py:186} INFO - Started process (PID=1504) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:11:28.947+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:11:28.949+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.949+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:11:29.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:29.140+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:29.148+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:11:29.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:29.251+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:29.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:29.261+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:11:29.279+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.339 seconds
[2025-07-18T11:11:59.655+0000] {processor.py:186} INFO - Started process (PID=1643) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:11:59.656+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:11:59.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.658+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:11:59.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.844+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:59.850+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:11:59.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.936+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:59.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.946+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:11:59.963+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.314 seconds
[2025-07-18T11:12:30.604+0000] {processor.py:186} INFO - Started process (PID=1786) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:12:30.605+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:12:30.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.607+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:12:30.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.801+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:30.809+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:12:30.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.899+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:30.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.909+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:12:30.928+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.330 seconds
[2025-07-18T11:13:02.104+0000] {processor.py:186} INFO - Started process (PID=1927) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:13:02.105+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:13:02.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.108+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:13:02.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.351+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:02.362+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:13:02.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.504+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:02.519+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.518+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:13:02.543+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.446 seconds
[2025-07-18T11:13:32.826+0000] {processor.py:186} INFO - Started process (PID=2068) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:13:32.827+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:13:32.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:32.829+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:13:33.013+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.012+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:33.020+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:13:33.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.117+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:33.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.126+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:13:33.141+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.321 seconds
[2025-07-18T11:14:03.618+0000] {processor.py:186} INFO - Started process (PID=2209) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:14:03.619+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:14:03.622+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.621+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:14:03.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.839+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:03.849+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:14:03.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.959+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:03.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.971+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:14:03.993+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.382 seconds
[2025-07-18T11:14:34.214+0000] {processor.py:186} INFO - Started process (PID=2350) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:14:34.215+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:14:34.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.217+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:14:34.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.416+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:34.427+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:14:34.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.537+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:34.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.547+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:14:34.568+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.361 seconds
[2025-07-18T11:15:04.899+0000] {processor.py:186} INFO - Started process (PID=2489) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:15:04.900+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:15:04.903+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.902+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:15:05.125+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.124+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:05.134+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:15:05.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.253+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:05.264+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.264+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:15:05.284+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.391 seconds
[2025-07-18T11:15:36.169+0000] {processor.py:186} INFO - Started process (PID=2632) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:15:36.170+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:15:36.172+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.172+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:15:36.403+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.403+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:36.413+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:15:36.521+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.520+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:36.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.533+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:15:36.559+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.397 seconds
[2025-07-18T11:16:06.742+0000] {processor.py:186} INFO - Started process (PID=2773) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:16:06.743+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:16:06.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.746+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:16:06.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.968+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:06.977+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:16:07.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.110+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:07.125+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.125+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:16:07.146+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.411 seconds
[2025-07-18T11:16:37.492+0000] {processor.py:186} INFO - Started process (PID=2914) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:16:37.493+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:16:37.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.495+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:16:37.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.703+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:37.712+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:16:37.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.812+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:37.823+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.823+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:16:37.843+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.357 seconds
[2025-07-18T11:17:08.662+0000] {processor.py:186} INFO - Started process (PID=3055) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:17:08.663+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:17:08.666+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:08.666+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:17:08.917+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:08.917+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:08.926+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:17:09.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:09.065+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:09.076+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:09.076+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:17:09.097+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.442 seconds
[2025-07-18T11:17:39.576+0000] {processor.py:186} INFO - Started process (PID=3196) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:17:39.577+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:17:39.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.580+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:17:39.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.809+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:39.817+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:17:39.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.931+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:39.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.944+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:17:39.966+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.396 seconds
[2025-07-18T11:18:10.229+0000] {processor.py:186} INFO - Started process (PID=3337) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:18:10.230+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:18:10.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:10.234+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:18:10.477+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:10.476+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:10.486+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:18:10.605+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:10.605+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:10.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:10.619+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:18:10.644+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.423 seconds
[2025-07-18T11:18:41.151+0000] {processor.py:186} INFO - Started process (PID=3478) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:18:41.152+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:18:41.154+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.154+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:18:41.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.368+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:41.376+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:18:41.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.479+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:41.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.491+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:18:41.515+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.371 seconds
[2025-07-18T11:19:11.926+0000] {processor.py:186} INFO - Started process (PID=3619) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:19:11.927+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:19:11.930+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.930+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:19:12.175+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:12.175+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:12.184+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:19:12.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:12.295+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:12.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:12.308+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:19:12.332+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.412 seconds
[2025-07-18T11:19:42.437+0000] {processor.py:186} INFO - Started process (PID=3765) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:19:42.438+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:19:42.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:42.440+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:19:42.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:42.653+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:42.663+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:19:42.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:42.767+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:42.780+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:42.779+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:19:42.800+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.370 seconds
[2025-07-18T11:20:12.905+0000] {processor.py:186} INFO - Started process (PID=3906) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:20:12.906+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:20:12.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.909+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:20:13.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:13.138+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:13.146+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:20:13.243+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:13.243+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:13.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:13.254+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:20:13.273+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.374 seconds
