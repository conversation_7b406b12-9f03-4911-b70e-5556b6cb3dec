[2025-07-18T10:16:50.650+0000] {processor.py:186} INFO - Started process (PID=273) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:16:50.651+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:16:50.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.653+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:16:50.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.700+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:50.707+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:16:50.731+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.087 seconds
[2025-07-18T10:17:20.794+0000] {processor.py:186} INFO - Started process (PID=402) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:17:20.795+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:17:20.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.797+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:17:20.832+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.828+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.833+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:17:20.855+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.068 seconds
[2025-07-18T10:17:51.674+0000] {processor.py:186} INFO - Started process (PID=533) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:17:51.675+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:17:51.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.677+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:17:51.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.855+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:51.859+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:17:51.875+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.207 seconds
[2025-07-18T10:18:22.771+0000] {processor.py:186} INFO - Started process (PID=664) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:18:22.772+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:18:22.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:22.773+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:18:22.807+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:22.803+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:22.809+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:18:22.825+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.060 seconds
[2025-07-18T10:18:53.712+0000] {processor.py:186} INFO - Started process (PID=795) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:18:53.712+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:18:53.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:53.713+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:18:53.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:53.741+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:53.746+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:18:53.767+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.060 seconds
[2025-07-18T10:19:24.690+0000] {processor.py:186} INFO - Started process (PID=926) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:19:24.691+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:19:24.692+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:24.692+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:19:24.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:24.721+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:24.725+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:19:24.740+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.056 seconds
[2025-07-18T10:19:55.678+0000] {processor.py:186} INFO - Started process (PID=1057) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:19:55.679+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:19:55.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:55.680+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:19:55.715+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:55.712+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:55.716+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:19:55.732+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.060 seconds
[2025-07-18T10:20:26.674+0000] {processor.py:186} INFO - Started process (PID=1188) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:20:26.675+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:20:26.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:26.676+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:20:26.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:26.708+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:26.713+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:20:26.729+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.060 seconds
[2025-07-18T10:20:57.629+0000] {processor.py:186} INFO - Started process (PID=1319) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:20:57.630+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:20:57.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.631+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:20:57.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.660+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:57.664+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:20:57.680+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.057 seconds
[2025-07-18T10:21:28.624+0000] {processor.py:186} INFO - Started process (PID=1450) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:21:28.625+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:21:28.627+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.626+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:21:28.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.662+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:28.666+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:21:28.684+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.066 seconds
[2025-07-18T10:21:58.782+0000] {processor.py:186} INFO - Started process (PID=1581) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:21:58.783+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:21:58.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:58.784+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:21:58.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:58.821+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:58.825+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:21:58.841+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.066 seconds
[2025-07-18T10:22:29.814+0000] {processor.py:186} INFO - Started process (PID=1712) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:22:29.815+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:22:29.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:29.817+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:22:29.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:29.847+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:29.854+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:22:29.869+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.061 seconds
[2025-07-18T10:23:00.786+0000] {processor.py:186} INFO - Started process (PID=1843) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:23:00.787+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:23:00.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:00.788+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:23:00.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:00.818+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:00.822+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:23:00.838+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.057 seconds
[2025-07-18T10:23:31.643+0000] {processor.py:186} INFO - Started process (PID=1974) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:23:31.644+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:23:31.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.645+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:23:31.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.672+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:31.676+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:23:31.693+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.056 seconds
[2025-07-18T10:24:01.859+0000] {processor.py:186} INFO - Started process (PID=2107) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:24:01.860+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:24:01.862+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.861+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:24:01.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.901+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:01.912+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:24:01.939+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.086 seconds
[2025-07-18T10:24:32.886+0000] {processor.py:186} INFO - Started process (PID=2238) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:24:32.887+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:24:32.888+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:32.888+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:24:32.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:32.919+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:32.923+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:24:32.942+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.061 seconds
[2025-07-18T10:25:06.038+0000] {processor.py:186} INFO - Started process (PID=2369) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:25:06.039+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:25:06.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.040+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:25:06.222+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.221+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:06.230+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:25:06.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.316+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:06.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.326+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:25:06.345+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.313 seconds
[2025-07-18T10:26:24.247+0000] {processor.py:186} INFO - Started process (PID=271) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:26:24.249+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:26:24.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.251+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:26:24.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.609+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:24.616+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:26:24.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.714+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:24.723+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.723+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:26:24.742+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.502 seconds
[2025-07-18T10:26:56.375+0000] {processor.py:186} INFO - Started process (PID=402) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:26:56.376+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:26:56.377+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.377+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:26:56.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.704+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:56.712+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:26:56.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.811+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:56.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.820+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:26:56.837+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.469 seconds
[2025-07-18T10:27:27.391+0000] {processor.py:186} INFO - Started process (PID=533) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:27:27.392+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:27:27.395+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.394+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:27:27.715+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.714+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:27.722+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:27:27.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.818+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:27.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.828+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:27:27.845+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.460 seconds
[2025-07-18T10:27:58.811+0000] {processor.py:186} INFO - Started process (PID=664) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:27:58.812+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:27:58.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:58.814+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:27:59.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.041+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:59.051+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:27:59.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.165+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:59.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.176+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:27:59.198+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.392 seconds
[2025-07-18T10:28:29.290+0000] {processor.py:186} INFO - Started process (PID=795) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:28:29.291+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:28:29.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:29.293+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:28:29.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:29.495+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:29.508+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:28:29.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:29.608+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:29.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:29.621+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:28:29.643+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.359 seconds
[2025-07-18T10:29:00.174+0000] {processor.py:186} INFO - Started process (PID=928) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:29:00.175+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:29:00.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.177+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:29:00.364+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.363+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:00.373+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:29:00.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.466+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:00.477+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.477+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:29:00.496+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.328 seconds
[2025-07-18T10:29:30.815+0000] {processor.py:186} INFO - Started process (PID=1059) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:29:30.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:29:30.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:30.818+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:29:31.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.010+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:31.019+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:29:31.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.106+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:31.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.116+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:29:31.132+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.323 seconds
[2025-07-18T10:30:01.525+0000] {processor.py:186} INFO - Started process (PID=1190) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:30:01.527+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:30:01.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.528+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:30:01.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.725+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:01.734+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:30:01.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.827+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:01.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.836+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:30:01.856+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.337 seconds
[2025-07-18T10:30:32.074+0000] {processor.py:186} INFO - Started process (PID=1319) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:30:32.075+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:30:32.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.078+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:30:32.267+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.267+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:32.275+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:30:32.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.373+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:32.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.383+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:30:32.401+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.332 seconds
[2025-07-18T10:31:02.620+0000] {processor.py:186} INFO - Started process (PID=1450) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:31:02.621+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:31:02.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.623+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:31:02.807+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.807+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:02.818+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:31:02.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:02.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.944+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:31:02.961+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.346 seconds
[2025-07-18T10:31:33.378+0000] {processor.py:186} INFO - Started process (PID=1583) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:31:33.379+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:31:33.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.381+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:31:33.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.559+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:33.567+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:31:33.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.664+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:33.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.677+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:31:33.704+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.332 seconds
[2025-07-18T10:32:04.203+0000] {processor.py:186} INFO - Started process (PID=1712) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:32:04.204+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:32:04.207+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.206+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:32:04.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.416+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:04.424+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:32:04.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.541+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:04.554+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.553+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:32:04.573+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.377 seconds
[2025-07-18T10:32:34.912+0000] {processor.py:186} INFO - Started process (PID=1845) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:32:34.913+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:32:34.915+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.915+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:32:35.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.095+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:35.105+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:32:35.197+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.197+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:35.207+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.207+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:32:35.226+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.319 seconds
[2025-07-18T10:33:05.364+0000] {processor.py:186} INFO - Started process (PID=1976) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:33:05.365+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:33:05.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.368+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:33:05.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.561+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:05.569+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:33:05.662+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.662+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:05.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.672+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:33:05.690+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.332 seconds
[2025-07-18T10:33:35.974+0000] {processor.py:186} INFO - Started process (PID=2107) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:33:35.976+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:33:35.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.978+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:33:36.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.178+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:36.187+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:33:36.286+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.286+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:36.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.296+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:33:36.318+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.352 seconds
[2025-07-18T10:34:06.652+0000] {processor.py:186} INFO - Started process (PID=2238) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:34:06.653+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:34:06.655+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.655+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:34:06.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.835+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:06.844+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:34:06.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.946+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:06.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.957+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:34:06.976+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.329 seconds
[2025-07-18T10:34:37.305+0000] {processor.py:186} INFO - Started process (PID=2367) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:34:37.307+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:34:37.309+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.309+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:34:37.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.512+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:37.520+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:34:37.618+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.618+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:37.627+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.627+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:34:37.649+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.350 seconds
[2025-07-18T10:35:07.837+0000] {processor.py:186} INFO - Started process (PID=2498) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:35:07.837+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:35:07.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.839+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:35:08.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.043+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:08.051+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:35:08.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.147+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:08.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.158+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:35:08.178+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.347 seconds
[2025-07-18T10:35:38.924+0000] {processor.py:186} INFO - Started process (PID=2631) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:35:38.924+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:35:38.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.926+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:35:39.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.144+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:39.156+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:35:39.267+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.267+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:39.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.280+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:35:39.301+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.383 seconds
[2025-07-18T10:36:10.025+0000] {processor.py:186} INFO - Started process (PID=2762) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:36:10.026+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:36:10.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.028+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:36:10.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.239+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:10.248+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:36:10.353+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.353+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:10.364+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.363+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:36:10.382+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.363 seconds
[2025-07-18T10:36:40.663+0000] {processor.py:186} INFO - Started process (PID=2893) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:36:40.664+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:36:40.667+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.667+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:36:40.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.860+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:40.870+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:36:40.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.965+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:40.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.976+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:36:40.996+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.339 seconds
[2025-07-18T10:37:11.196+0000] {processor.py:186} INFO - Started process (PID=3024) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:37:11.197+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:37:11.199+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.199+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:37:11.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.397+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:11.406+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:37:11.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.526+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:11.542+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.542+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:37:11.570+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.379 seconds
[2025-07-18T10:37:42.355+0000] {processor.py:186} INFO - Started process (PID=3155) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:37:42.356+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:37:42.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:42.357+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:37:42.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:42.545+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:42.556+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:37:42.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:42.662+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:42.674+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:42.674+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:37:42.694+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.344 seconds
[2025-07-18T10:38:12.912+0000] {processor.py:186} INFO - Started process (PID=3286) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:38:12.913+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:38:12.915+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.915+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:38:13.143+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.142+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:13.152+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:38:13.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.268+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:13.282+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.282+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:38:13.307+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.401 seconds
[2025-07-18T10:38:43.371+0000] {processor.py:186} INFO - Started process (PID=3415) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:38:43.372+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:38:43.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.374+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:38:43.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.546+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:43.556+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:38:43.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.648+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:43.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.658+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:38:43.677+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.311 seconds
[2025-07-18T10:39:13.748+0000] {processor.py:186} INFO - Started process (PID=3541) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:39:13.749+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:39:13.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.750+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:39:13.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.943+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:13.952+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:39:14.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:14.053+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:14.069+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:14.069+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:39:14.099+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.357 seconds
[2025-07-18T10:39:44.503+0000] {processor.py:186} INFO - Started process (PID=3672) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:39:44.504+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:39:44.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.506+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:39:44.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.719+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:44.728+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:39:44.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.826+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:44.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.836+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:39:44.856+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.359 seconds
[2025-07-18T10:40:15.600+0000] {processor.py:186} INFO - Started process (PID=3805) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:40:15.601+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:40:15.603+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:15.603+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:40:15.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:15.815+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:15.825+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:40:15.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:15.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:15.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:15.943+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:40:15.964+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.371 seconds
[2025-07-18T10:40:47.046+0000] {processor.py:186} INFO - Started process (PID=3941) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:40:47.047+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:40:47.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.049+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:40:47.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.239+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:47.249+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:40:47.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.339+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:47.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.350+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:40:47.370+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.330 seconds
[2025-07-18T10:41:17.489+0000] {processor.py:186} INFO - Started process (PID=4070) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:41:17.490+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:41:17.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.493+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:41:17.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.718+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:17.729+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:41:17.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.838+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:17.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.851+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:41:17.874+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.393 seconds
[2025-07-18T10:41:48.039+0000] {processor.py:186} INFO - Started process (PID=4208) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:41:48.040+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:41:48.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:48.043+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:41:48.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:48.220+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:48.229+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:41:48.317+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:48.316+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:48.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:48.325+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:41:48.341+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.308 seconds
[2025-07-18T10:42:18.960+0000] {processor.py:186} INFO - Started process (PID=4344) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:42:18.961+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:42:18.964+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.963+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:42:19.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.165+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:19.175+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:42:19.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.280+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:19.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.292+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:42:19.311+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.356 seconds
[2025-07-18T10:43:00.780+0000] {processor.py:186} INFO - Started process (PID=276) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:43:00.781+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:43:00.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.783+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:43:01.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.150+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:01.155+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:43:01.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.249+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:01.264+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.264+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:43:01.283+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.508 seconds
[2025-07-18T10:43:32.919+0000] {processor.py:186} INFO - Started process (PID=412) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:43:32.920+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:43:32.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.922+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:43:33.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.277+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:33.283+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:43:33.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.368+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:33.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.377+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:43:33.396+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.483 seconds
[2025-07-18T10:44:04.394+0000] {processor.py:186} INFO - Started process (PID=550) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:44:04.394+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:44:04.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:04.397+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:44:04.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:04.709+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:04.716+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:44:04.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:04.799+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:04.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:04.808+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:44:04.826+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.438 seconds
[2025-07-18T10:44:35.012+0000] {processor.py:186} INFO - Started process (PID=686) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:44:35.013+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:44:35.015+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:35.015+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:44:35.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:35.221+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:35.231+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:44:35.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:35.328+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:35.339+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:35.339+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:44:35.360+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.354 seconds
[2025-07-18T10:45:06.012+0000] {processor.py:186} INFO - Started process (PID=822) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:45:06.013+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:45:06.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:06.016+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:45:06.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:06.241+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:06.251+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:45:06.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:06.366+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:06.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:06.378+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:45:06.399+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.395 seconds
[2025-07-18T10:45:36.538+0000] {processor.py:186} INFO - Started process (PID=958) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:45:36.539+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:45:36.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.540+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:45:36.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.741+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:36.751+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:45:36.862+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.861+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:36.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.873+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:45:36.894+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.362 seconds
[2025-07-18T10:46:07.232+0000] {processor.py:186} INFO - Started process (PID=1094) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:46:07.233+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:46:07.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.236+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:46:07.469+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.469+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:07.476+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:46:07.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.570+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:07.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.581+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:46:07.600+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.376 seconds
[2025-07-18T10:46:38.153+0000] {processor.py:186} INFO - Started process (PID=1230) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:46:38.154+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:46:38.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:38.157+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:46:38.367+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:38.366+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:38.376+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:46:38.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:38.496+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:38.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:38.507+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:46:38.528+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.382 seconds
[2025-07-18T10:48:06.187+0000] {processor.py:186} INFO - Started process (PID=276) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:48:06.189+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:48:06.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.191+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:48:06.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.516+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:06.522+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:48:06.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.610+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:06.622+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.622+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:48:06.640+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.460 seconds
[2025-07-18T10:48:37.378+0000] {processor.py:186} INFO - Started process (PID=412) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:48:37.380+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:48:37.382+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.382+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:48:37.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.725+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:37.733+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:48:37.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.827+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:37.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.835+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:48:37.855+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.485 seconds
[2025-07-18T10:49:08.452+0000] {processor.py:186} INFO - Started process (PID=548) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:49:08.453+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:49:08.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.456+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:49:08.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.852+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:08.859+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:49:08.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.958+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:08.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:49:08.987+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.543 seconds
[2025-07-18T10:49:39.512+0000] {processor.py:186} INFO - Started process (PID=686) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:49:39.513+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:49:39.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.515+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:49:39.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.712+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:39.723+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:49:39.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.829+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:39.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.843+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:49:39.864+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.358 seconds
[2025-07-18T10:50:10.219+0000] {processor.py:186} INFO - Started process (PID=820) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:50:10.220+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:50:10.222+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.222+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:50:10.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.439+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:10.448+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:50:10.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.557+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:10.571+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.571+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:50:10.593+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.379 seconds
[2025-07-18T10:50:41.014+0000] {processor.py:186} INFO - Started process (PID=956) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:50:41.015+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:50:41.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.017+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:50:41.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.233+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:41.242+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:50:41.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.342+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:41.353+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.353+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:50:41.373+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.365 seconds
[2025-07-18T10:51:11.751+0000] {processor.py:186} INFO - Started process (PID=1092) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:51:11.752+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:51:11.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:11.754+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:51:11.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:11.952+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:11.961+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:51:12.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.057+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:12.069+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.068+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:51:12.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.343 seconds
[2025-07-18T10:51:42.166+0000] {processor.py:186} INFO - Started process (PID=1229) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:51:42.167+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:51:42.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.169+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:51:42.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.360+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:42.368+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:51:42.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.458+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:42.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.467+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:51:42.485+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.325 seconds
[2025-07-18T10:52:12.607+0000] {processor.py:186} INFO - Started process (PID=1365) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:52:12.608+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:52:12.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.610+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:52:12.812+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.812+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:12.821+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:52:12.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.922+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:12.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.933+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:52:12.953+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.351 seconds
[2025-07-18T10:52:43.289+0000] {processor.py:186} INFO - Started process (PID=1501) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:52:43.290+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:52:43.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.292+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:52:43.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.494+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:43.501+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:52:43.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.599+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:43.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.611+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:52:43.633+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.349 seconds
[2025-07-18T10:53:13.827+0000] {processor.py:186} INFO - Started process (PID=1637) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:53:13.828+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:53:13.831+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.830+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:53:14.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:14.025+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:14.035+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:53:14.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:14.137+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:14.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:14.149+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:53:14.170+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.348 seconds
[2025-07-18T10:53:44.629+0000] {processor.py:186} INFO - Started process (PID=1775) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:53:44.630+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:53:44.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.632+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:53:44.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.817+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:44.826+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:53:44.915+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.914+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:44.925+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.925+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:53:44.943+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.320 seconds
[2025-07-18T10:54:15.860+0000] {processor.py:186} INFO - Started process (PID=1911) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:54:15.861+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:54:15.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:15.863+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:54:16.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:16.062+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:16.071+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:54:16.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:16.174+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:16.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:16.184+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:54:16.203+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.349 seconds
[2025-07-18T10:54:46.320+0000] {processor.py:186} INFO - Started process (PID=2047) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:54:46.321+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:54:46.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:46.322+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:54:46.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:46.523+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:46.533+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:54:46.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:46.635+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:46.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:46.645+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:54:46.663+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.350 seconds
[2025-07-18T10:55:17.369+0000] {processor.py:186} INFO - Started process (PID=2183) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:55:17.370+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:55:17.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:17.372+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:55:17.601+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:17.601+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:17.609+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:55:17.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:17.699+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:17.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:17.709+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:55:17.726+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.363 seconds
[2025-07-18T10:57:30.836+0000] {processor.py:186} INFO - Started process (PID=276) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:57:30.837+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:57:30.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.840+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:57:31.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.224+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:31.232+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:57:31.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.334+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:31.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.344+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:57:31.365+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.536 seconds
[2025-07-18T10:58:02.305+0000] {processor.py:186} INFO - Started process (PID=412) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:58:02.306+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:58:02.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.308+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:58:02.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.636+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:02.644+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:58:02.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.726+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:02.734+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.734+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:58:02.751+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.452 seconds
[2025-07-18T10:58:33.824+0000] {processor.py:186} INFO - Started process (PID=548) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:58:33.825+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:58:33.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:33.827+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:58:34.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.121+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:34.128+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:58:34.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.214+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:34.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.222+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:58:34.239+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.420 seconds
[2025-07-18T10:59:04.529+0000] {processor.py:186} INFO - Started process (PID=684) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:59:04.531+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:59:04.534+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.534+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:59:04.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.760+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:04.769+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:59:04.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.866+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:04.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.876+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:59:04.895+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.373 seconds
[2025-07-18T10:59:35.005+0000] {processor.py:186} INFO - Started process (PID=820) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:59:35.006+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T10:59:35.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.008+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:59:35.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.198+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:35.206+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T10:59:35.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.294+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:35.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.306+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T10:59:35.337+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.337 seconds
[2025-07-18T11:00:05.491+0000] {processor.py:186} INFO - Started process (PID=956) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:00:05.492+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:00:05.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.493+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:00:05.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.683+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:05.690+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:00:05.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.788+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:05.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.800+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:00:05.823+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.337 seconds
[2025-07-18T11:00:36.504+0000] {processor.py:186} INFO - Started process (PID=1092) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:00:36.507+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:00:36.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.509+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:00:36.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.716+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:36.723+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:00:36.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.814+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:36.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.824+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:00:36.844+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.346 seconds
[2025-07-18T11:01:07.100+0000] {processor.py:186} INFO - Started process (PID=1233) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:01:07.101+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:01:07.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.103+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:01:07.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.306+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:07.316+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:01:07.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.418+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:07.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.428+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:01:07.448+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.354 seconds
[2025-07-18T11:01:38.049+0000] {processor.py:186} INFO - Started process (PID=1369) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:01:38.050+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:01:38.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.053+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:01:38.243+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.243+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:38.252+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:01:38.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.361+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:38.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.372+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:01:38.394+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.351 seconds
[2025-07-18T11:02:09.691+0000] {processor.py:186} INFO - Started process (PID=1502) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:02:09.692+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:02:09.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.694+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:02:09.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.898+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:09.905+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:02:09.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.988+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:09.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.998+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:02:10.015+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.329 seconds
[2025-07-18T11:02:40.411+0000] {processor.py:186} INFO - Started process (PID=1638) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:02:40.412+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:02:40.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.414+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:02:40.622+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.622+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:40.629+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:02:40.730+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.730+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:40.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.741+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:02:40.761+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.355 seconds
[2025-07-18T11:03:11.120+0000] {processor.py:186} INFO - Started process (PID=1779) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:03:11.121+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:03:11.123+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.123+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:03:11.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.314+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:11.321+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:03:11.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.416+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:11.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.427+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:03:11.447+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.333 seconds
[2025-07-18T11:03:42.073+0000] {processor.py:186} INFO - Started process (PID=1910) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:03:42.074+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:03:42.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.077+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:03:42.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.281+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:42.291+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:03:42.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.387+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:42.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.398+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:03:42.417+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.350 seconds
[2025-07-18T11:04:12.959+0000] {processor.py:186} INFO - Started process (PID=2046) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:04:12.960+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:04:12.964+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:12.963+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:04:13.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.174+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:13.183+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:04:13.286+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.286+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:13.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.296+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:04:13.317+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.365 seconds
[2025-07-18T11:04:43.548+0000] {processor.py:186} INFO - Started process (PID=2182) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:04:43.549+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:04:43.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.551+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:04:43.764+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.764+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:43.771+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:04:43.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.879+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:43.891+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.890+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:04:43.910+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.369 seconds
[2025-07-18T11:05:14.335+0000] {processor.py:186} INFO - Started process (PID=2318) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:05:14.336+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:05:14.338+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:14.338+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:05:14.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:14.547+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:14.556+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:05:14.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:14.667+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:14.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:14.679+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:05:14.699+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.371 seconds
[2025-07-18T11:06:53.597+0000] {processor.py:186} INFO - Started process (PID=288) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:06:53.598+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:06:53.600+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:53.600+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:06:54.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.008+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:54.016+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:06:54.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.106+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:54.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.117+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:06:54.138+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.547 seconds
[2025-07-18T11:07:25.013+0000] {processor.py:186} INFO - Started process (PID=429) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:07:25.015+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:07:25.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.017+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:07:25.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.427+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:25.433+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:07:25.556+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.555+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:25.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.574+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:07:25.592+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.588 seconds
[2025-07-18T11:07:56.053+0000] {processor.py:186} INFO - Started process (PID=572) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:07:56.054+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:07:56.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.056+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:07:56.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.249+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:56.259+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:07:56.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.365+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:56.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.379+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:07:56.400+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.353 seconds
[2025-07-18T11:08:26.948+0000] {processor.py:186} INFO - Started process (PID=711) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:08:26.949+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:08:26.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.952+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:08:27.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.136+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:27.145+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:08:27.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.244+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:27.255+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.254+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:08:27.277+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.334 seconds
[2025-07-18T11:08:57.829+0000] {processor.py:186} INFO - Started process (PID=854) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:08:57.830+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:08:57.833+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.832+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:08:58.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.039+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:58.048+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:08:58.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.150+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:58.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.164+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:08:58.201+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.377 seconds
[2025-07-18T11:09:28.818+0000] {processor.py:186} INFO - Started process (PID=993) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:09:28.820+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:09:28.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.822+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:09:29.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.038+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:29.049+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:09:29.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.144+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:29.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.156+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:09:29.177+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.365 seconds
[2025-07-18T11:09:59.883+0000] {processor.py:186} INFO - Started process (PID=1136) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:09:59.884+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:09:59.887+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:59.886+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:10:00.093+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.092+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:00.101+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:10:00.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.205+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:00.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.217+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:10:00.239+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.361 seconds
[2025-07-18T11:10:30.469+0000] {processor.py:186} INFO - Started process (PID=1277) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:10:30.470+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:10:30.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:30.472+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:10:30.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:30.700+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:30.710+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:10:30.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:30.848+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:30.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:30.861+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:10:30.886+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.423 seconds
[2025-07-18T11:11:01.119+0000] {processor.py:186} INFO - Started process (PID=1418) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:11:01.120+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:11:01.123+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.122+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:11:01.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.319+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:01.329+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:11:01.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.427+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:01.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.443+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:11:01.469+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.355 seconds
[2025-07-18T11:11:31.929+0000] {processor.py:186} INFO - Started process (PID=1559) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:11:31.930+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:11:31.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.932+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:11:32.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.113+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:32.121+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:11:32.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.238+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:32.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.248+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:11:32.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.344 seconds
[2025-07-18T11:12:02.730+0000] {processor.py:186} INFO - Started process (PID=1700) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:12:02.731+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:12:02.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:02.733+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:12:02.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:02.906+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:02.914+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:12:03.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.005+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:03.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.018+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:12:03.042+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.318 seconds
[2025-07-18T11:12:33.216+0000] {processor.py:186} INFO - Started process (PID=1841) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:12:33.217+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:12:33.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.219+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:12:33.432+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.431+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:33.440+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:12:33.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.536+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:33.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.552+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:12:33.575+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.366 seconds
[2025-07-18T11:13:05.018+0000] {processor.py:186} INFO - Started process (PID=1980) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:13:05.019+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:13:05.021+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.021+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:13:05.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.207+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:05.218+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:13:05.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.322+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:05.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.332+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:13:05.348+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.336 seconds
[2025-07-18T11:13:35.866+0000] {processor.py:186} INFO - Started process (PID=2123) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:13:35.867+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:13:35.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.869+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:13:36.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.051+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:36.061+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:13:36.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.153+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:36.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.164+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:13:36.182+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.322 seconds
[2025-07-18T11:14:07.060+0000] {processor.py:186} INFO - Started process (PID=2264) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:14:07.061+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:14:07.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:07.064+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:14:07.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:07.319+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:07.329+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:14:07.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:07.456+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:07.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:07.471+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:14:07.498+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.447 seconds
[2025-07-18T11:14:37.608+0000] {processor.py:186} INFO - Started process (PID=2405) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:14:37.609+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:14:37.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.610+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:14:37.831+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.831+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:37.840+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:14:37.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.952+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:37.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.964+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:14:37.987+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.385 seconds
[2025-07-18T11:15:08.262+0000] {processor.py:186} INFO - Started process (PID=2546) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:15:08.263+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:15:08.265+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.264+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:15:08.464+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.464+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:08.473+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:15:08.571+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.571+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:08.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.585+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:15:08.606+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.351 seconds
[2025-07-18T11:15:38.831+0000] {processor.py:186} INFO - Started process (PID=2685) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:15:38.832+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:15:38.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.834+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:15:39.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.063+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:39.072+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:15:39.183+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.183+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:39.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.196+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:15:39.218+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.392 seconds
[2025-07-18T11:16:09.788+0000] {processor.py:186} INFO - Started process (PID=2826) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:16:09.789+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:16:09.792+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:09.792+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:16:10.033+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.033+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:10.041+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:16:10.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.153+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:10.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.166+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:16:10.186+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.405 seconds
[2025-07-18T11:16:40.285+0000] {processor.py:186} INFO - Started process (PID=2967) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:16:40.286+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:16:40.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:40.288+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:16:40.519+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:40.519+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:40.528+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:16:40.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:40.637+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:40.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:40.648+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:16:40.668+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.391 seconds
[2025-07-18T11:17:11.895+0000] {processor.py:186} INFO - Started process (PID=3110) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:17:11.896+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:17:11.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:11.898+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:17:12.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.115+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:12.125+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:17:12.222+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.222+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:12.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.233+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:17:12.253+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.365 seconds
[2025-07-18T11:17:42.580+0000] {processor.py:186} INFO - Started process (PID=3251) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:17:42.581+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:17:42.583+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.583+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:17:42.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.793+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:42.802+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:17:42.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.904+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:42.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.915+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:17:42.935+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.364 seconds
[2025-07-18T11:18:13.424+0000] {processor.py:186} INFO - Started process (PID=3392) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:18:13.425+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:18:13.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:13.427+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:18:13.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:13.635+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:13.645+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:18:13.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:13.751+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:13.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:13.763+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:18:13.783+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.366 seconds
[2025-07-18T11:18:43.965+0000] {processor.py:186} INFO - Started process (PID=3533) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:18:43.965+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:18:43.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.967+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:18:44.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.155+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:44.164+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:18:44.258+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.258+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:44.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.269+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:18:44.288+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.330 seconds
[2025-07-18T11:19:15.504+0000] {processor.py:186} INFO - Started process (PID=3679) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:19:15.505+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:19:15.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.507+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:19:15.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.783+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:15.792+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:19:15.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.919+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:15.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.934+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:19:15.957+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.460 seconds
[2025-07-18T11:19:46.040+0000] {processor.py:186} INFO - Started process (PID=3820) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:19:46.041+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:19:46.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:46.043+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:19:46.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:46.237+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:46.246+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:19:46.343+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:46.342+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:46.354+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:46.353+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:19:46.372+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.338 seconds
[2025-07-18T11:20:16.928+0000] {processor.py:186} INFO - Started process (PID=3961) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:20:16.929+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:20:16.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:16.932+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:20:17.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.163+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:17.173+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:20:17.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.283+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:17.294+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.294+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:20:17.317+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.399 seconds
