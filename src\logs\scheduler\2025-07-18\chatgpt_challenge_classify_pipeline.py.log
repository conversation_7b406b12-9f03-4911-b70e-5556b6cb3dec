[2025-07-18T10:16:50.657+0000] {processor.py:186} INFO - Started process (PID=276) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:16:50.658+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:16:50.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.660+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:16:50.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.708+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:50.714+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:16:50.741+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.091 seconds
[2025-07-18T10:17:20.899+0000] {processor.py:186} INFO - Started process (PID=409) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:17:20.900+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:17:20.903+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.902+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:17:20.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.935+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.939+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:17:20.958+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.064 seconds
[2025-07-18T10:17:51.992+0000] {processor.py:186} INFO - Started process (PID=540) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:17:51.993+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:17:51.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.993+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:17:52.029+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:52.025+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:52.030+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:17:52.048+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.061 seconds
[2025-07-18T10:18:22.852+0000] {processor.py:186} INFO - Started process (PID=671) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:18:22.853+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:18:22.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:22.854+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:18:22.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:22.886+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:22.891+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:18:22.907+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.060 seconds
[2025-07-18T10:18:53.810+0000] {processor.py:186} INFO - Started process (PID=802) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:18:53.812+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:18:53.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:53.813+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:18:53.862+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:53.857+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:53.863+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:18:53.880+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.076 seconds
[2025-07-18T10:19:24.766+0000] {processor.py:186} INFO - Started process (PID=933) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:19:24.767+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:19:24.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:24.768+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:19:24.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:24.797+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:24.801+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:19:24.815+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.055 seconds
[2025-07-18T10:19:55.761+0000] {processor.py:186} INFO - Started process (PID=1064) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:19:55.762+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:19:55.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:55.763+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:19:55.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:55.796+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:55.800+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:19:55.817+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.062 seconds
[2025-07-18T10:20:26.754+0000] {processor.py:186} INFO - Started process (PID=1195) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:20:26.755+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:20:26.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:26.756+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:20:26.791+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:26.789+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:26.792+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:20:26.813+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.065 seconds
[2025-07-18T10:20:57.708+0000] {processor.py:186} INFO - Started process (PID=1326) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:20:57.709+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:20:57.710+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.709+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:20:57.743+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.741+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:57.744+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:20:57.760+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.058 seconds
[2025-07-18T10:21:28.713+0000] {processor.py:186} INFO - Started process (PID=1457) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:21:28.714+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:21:28.715+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.715+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:21:28.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.748+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:28.753+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:21:28.772+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.065 seconds
[2025-07-18T10:21:58.875+0000] {processor.py:186} INFO - Started process (PID=1588) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:21:58.876+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:21:58.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:58.877+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:21:58.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:58.908+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:58.911+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:21:58.926+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.057 seconds
[2025-07-18T10:22:29.896+0000] {processor.py:186} INFO - Started process (PID=1719) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:22:29.897+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:22:29.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:29.898+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:22:29.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:29.931+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:29.935+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:22:29.951+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.062 seconds
[2025-07-18T10:23:00.869+0000] {processor.py:186} INFO - Started process (PID=1850) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:23:00.869+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:23:00.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:00.871+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:23:00.903+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:00.901+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:00.905+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:23:00.922+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.059 seconds
[2025-07-18T10:23:31.719+0000] {processor.py:186} INFO - Started process (PID=1981) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:23:31.720+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:23:31.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.721+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:23:31.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.752+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:31.756+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:23:31.771+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.058 seconds
[2025-07-18T10:24:01.871+0000] {processor.py:186} INFO - Started process (PID=2110) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:24:01.873+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:24:01.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.874+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:24:01.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.922+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:01.930+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:24:01.955+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.093 seconds
[2025-07-18T10:24:32.895+0000] {processor.py:186} INFO - Started process (PID=2241) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:24:32.896+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:24:32.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:32.897+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:24:32.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:32.928+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:32.932+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:24:32.947+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.058 seconds
[2025-07-18T10:25:06.381+0000] {processor.py:186} INFO - Started process (PID=2374) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:25:06.381+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:25:06.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.382+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:25:06.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.563+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:06.573+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:25:06.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.658+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:06.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.669+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:25:06.692+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.317 seconds
[2025-07-18T10:26:24.273+0000] {processor.py:186} INFO - Started process (PID=276) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:26:24.274+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:26:24.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.276+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:26:24.618+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.618+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:24.624+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:26:24.718+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.718+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:24.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.727+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:26:24.745+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.477 seconds
[2025-07-18T10:26:56.612+0000] {processor.py:186} INFO - Started process (PID=407) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:26:56.613+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:26:56.616+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.616+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:26:56.938+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.937+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:56.945+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:26:57.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.045+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:57.055+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.055+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:26:57.071+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.464 seconds
[2025-07-18T10:27:27.852+0000] {processor.py:186} INFO - Started process (PID=538) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:27:27.853+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:27:27.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.855+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:27:28.200+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.199+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:28.207+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:27:28.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:28.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.307+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:27:28.326+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.480 seconds
[2025-07-18T10:27:59.221+0000] {processor.py:186} INFO - Started process (PID=671) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:27:59.222+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:27:59.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.225+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:27:59.460+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.460+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:59.471+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:27:59.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.585+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:59.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.596+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:27:59.617+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.402 seconds
[2025-07-18T10:28:29.691+0000] {processor.py:186} INFO - Started process (PID=802) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:28:29.693+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:28:29.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:29.695+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:28:29.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:29.892+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:29.901+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:28:30.006+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.005+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:30.015+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.015+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:28:30.033+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.347 seconds
[2025-07-18T10:29:00.547+0000] {processor.py:186} INFO - Started process (PID=933) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:29:00.548+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:29:00.550+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.550+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:29:00.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.746+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:00.756+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:29:00.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.858+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:00.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.869+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:29:00.891+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.349 seconds
[2025-07-18T10:29:31.182+0000] {processor.py:186} INFO - Started process (PID=1064) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:29:31.183+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:29:31.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.185+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:29:31.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.368+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:31.376+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:29:31.467+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.466+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:31.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.476+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:29:31.494+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.317 seconds
[2025-07-18T10:30:01.910+0000] {processor.py:186} INFO - Started process (PID=1195) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:30:01.911+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:30:01.913+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.913+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:30:02.101+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.100+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:02.112+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:30:02.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.220+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:02.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.229+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:30:02.252+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.347 seconds
[2025-07-18T10:30:32.411+0000] {processor.py:186} INFO - Started process (PID=1324) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:30:32.412+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:30:32.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.413+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:30:32.601+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.601+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:32.610+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:30:32.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.706+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:32.717+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.717+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:30:32.737+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.332 seconds
[2025-07-18T10:31:02.998+0000] {processor.py:186} INFO - Started process (PID=1457) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:31:02.999+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:31:03.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.001+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:31:03.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.194+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:03.203+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:31:03.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.301+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:03.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.310+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:31:03.329+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.336 seconds
[2025-07-18T10:31:33.787+0000] {processor.py:186} INFO - Started process (PID=1588) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:31:33.788+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:31:33.791+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.791+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:31:33.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.973+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:33.980+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:31:34.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.077+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:34.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.088+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:31:34.105+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.326 seconds
[2025-07-18T10:32:04.246+0000] {processor.py:186} INFO - Started process (PID=1717) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:32:04.247+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:32:04.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.250+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:32:04.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.462+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:04.471+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:32:04.584+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.583+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:04.595+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.595+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:32:04.620+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.381 seconds
[2025-07-18T10:32:34.920+0000] {processor.py:186} INFO - Started process (PID=1848) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:32:34.921+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:32:34.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.923+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:32:35.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.106+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:35.115+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:32:35.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.203+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:35.212+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.212+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:32:35.231+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.316 seconds
[2025-07-18T10:33:05.372+0000] {processor.py:186} INFO - Started process (PID=1979) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:33:05.372+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:33:05.375+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.375+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:33:05.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.558+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:05.567+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:33:05.656+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.656+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:05.666+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.666+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:33:05.685+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.319 seconds
[2025-07-18T10:33:35.988+0000] {processor.py:186} INFO - Started process (PID=2110) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:33:35.989+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:33:35.993+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.993+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:33:36.190+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.190+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:36.198+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:33:36.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.295+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:36.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.306+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:33:36.328+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.350 seconds
[2025-07-18T10:34:06.661+0000] {processor.py:186} INFO - Started process (PID=2241) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:34:06.661+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:34:06.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.663+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:34:06.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.844+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:06.854+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:34:06.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.957+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:06.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.969+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:34:06.988+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.333 seconds
[2025-07-18T10:34:37.680+0000] {processor.py:186} INFO - Started process (PID=2374) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:34:37.681+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:34:37.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.683+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:34:37.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.870+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:37.877+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:34:37.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.961+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:37.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:34:37.986+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.312 seconds
[2025-07-18T10:35:08.217+0000] {processor.py:186} INFO - Started process (PID=2505) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:35:08.218+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:35:08.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.220+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:35:08.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.406+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:08.415+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:35:08.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.505+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:08.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.515+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:35:08.534+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.323 seconds
[2025-07-18T10:35:38.959+0000] {processor.py:186} INFO - Started process (PID=2634) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:35:38.960+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:35:38.963+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.962+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:35:39.182+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.182+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:39.192+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:35:39.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.296+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:39.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.307+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:35:39.327+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.375 seconds
[2025-07-18T10:36:10.034+0000] {processor.py:186} INFO - Started process (PID=2765) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:36:10.035+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:36:10.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.037+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:36:10.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.257+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:10.267+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:36:10.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.373+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:10.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.384+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:36:10.406+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.377 seconds
[2025-07-18T10:36:40.674+0000] {processor.py:186} INFO - Started process (PID=2896) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:36:40.675+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:36:40.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.677+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:36:40.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.876+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:40.883+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:36:40.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.984+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:40.996+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.996+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:36:41.016+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.349 seconds
[2025-07-18T10:37:11.205+0000] {processor.py:186} INFO - Started process (PID=3027) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:37:11.206+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:37:11.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.208+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:37:11.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.415+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:11.426+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:37:11.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.545+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:11.560+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.560+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:37:11.581+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.382 seconds
[2025-07-18T10:37:42.364+0000] {processor.py:186} INFO - Started process (PID=3158) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:37:42.365+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:37:42.367+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:42.366+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:37:42.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:42.558+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:42.569+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:37:42.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:42.670+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:42.681+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:42.681+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:37:42.700+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.342 seconds
[2025-07-18T10:38:12.921+0000] {processor.py:186} INFO - Started process (PID=3289) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:38:12.922+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:38:12.925+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.924+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:38:13.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.157+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:13.166+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:38:13.284+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.283+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:13.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.295+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:38:13.316+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.402 seconds
[2025-07-18T10:38:43.725+0000] {processor.py:186} INFO - Started process (PID=3422) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:38:43.726+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:38:43.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.727+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:38:43.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.901+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:43.910+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:38:44.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.001+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:44.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.012+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:38:44.032+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.313 seconds
[2025-07-18T10:39:14.643+0000] {processor.py:186} INFO - Started process (PID=3553) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:39:14.644+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:39:14.647+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:14.646+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:39:14.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:14.859+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:14.868+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:39:14.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:14.975+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:14.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:14.985+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:39:15.004+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.369 seconds
[2025-07-18T10:39:45.277+0000] {processor.py:186} INFO - Started process (PID=3684) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:39:45.278+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:39:45.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.280+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:39:45.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.465+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:45.473+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:39:45.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.563+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:45.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.573+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:39:45.593+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.322 seconds
[2025-07-18T10:40:16.030+0000] {processor.py:186} INFO - Started process (PID=3813) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:40:16.031+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:40:16.033+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.033+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:40:16.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.247+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:16.256+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:40:16.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.365+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:16.377+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.377+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:40:16.401+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.378 seconds
[2025-07-18T10:40:47.058+0000] {processor.py:186} INFO - Started process (PID=3944) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:40:47.058+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:40:47.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.060+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:40:47.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.250+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:47.260+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:40:47.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.356+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:47.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.367+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:40:47.386+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.336 seconds
[2025-07-18T10:41:17.551+0000] {processor.py:186} INFO - Started process (PID=4075) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:41:17.552+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:41:17.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.554+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:41:17.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.784+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:17.793+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:41:17.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.908+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:17.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.921+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:41:17.941+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.396 seconds
[2025-07-18T10:41:48.379+0000] {processor.py:186} INFO - Started process (PID=4213) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:41:48.380+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:41:48.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:48.382+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:41:48.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:48.563+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:48.571+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:41:48.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:48.663+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:48.674+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:48.674+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:41:48.693+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.319 seconds
[2025-07-18T10:42:18.970+0000] {processor.py:186} INFO - Started process (PID=4347) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:42:18.971+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:42:18.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.974+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:42:19.186+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.186+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:19.195+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:42:19.305+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.304+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:19.317+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.317+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:42:19.339+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.375 seconds
[2025-07-18T10:43:00.965+0000] {processor.py:186} INFO - Started process (PID=281) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:43:00.966+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:43:00.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.968+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:43:01.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.307+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:01.313+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:43:01.404+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.404+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:01.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.415+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:43:01.434+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.476 seconds
[2025-07-18T10:43:33.434+0000] {processor.py:186} INFO - Started process (PID=419) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:43:33.435+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:43:33.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.436+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:43:33.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.751+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:33.758+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:43:33.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.846+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:33.855+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.855+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:43:33.872+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.443 seconds
[2025-07-18T10:44:04.403+0000] {processor.py:186} INFO - Started process (PID=553) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:44:04.403+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:44:04.405+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:04.405+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:44:04.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:04.700+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:04.708+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:44:04.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:04.794+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:04.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:04.802+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:44:04.821+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.424 seconds
[2025-07-18T10:44:35.021+0000] {processor.py:186} INFO - Started process (PID=689) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:44:35.022+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:44:35.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:35.024+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:44:35.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:35.221+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:35.231+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:44:35.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:35.331+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:35.343+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:35.343+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:44:35.365+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.347 seconds
[2025-07-18T10:45:06.026+0000] {processor.py:186} INFO - Started process (PID=825) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:45:06.027+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:45:06.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:06.029+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:45:06.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:06.247+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:06.256+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:45:06.377+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:06.377+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:06.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:06.393+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:45:06.420+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.403 seconds
[2025-07-18T10:45:36.546+0000] {processor.py:186} INFO - Started process (PID=961) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:45:36.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:45:36.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.548+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:45:36.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.760+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:36.769+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:45:36.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.882+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:36.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.892+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:45:36.912+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.372 seconds
[2025-07-18T10:46:07.259+0000] {processor.py:186} INFO - Started process (PID=1097) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:46:07.260+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:46:07.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.263+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:46:07.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.495+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:07.503+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:46:07.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.606+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:07.617+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.617+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:46:07.641+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.391 seconds
[2025-07-18T10:46:38.572+0000] {processor.py:186} INFO - Started process (PID=1235) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:46:38.573+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:46:38.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:38.575+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:46:38.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:38.790+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:38.799+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:46:38.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:38.909+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:38.921+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:38.920+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:46:38.944+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.378 seconds
[2025-07-18T10:48:06.483+0000] {processor.py:186} INFO - Started process (PID=281) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:48:06.484+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:48:06.486+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.486+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:48:06.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.814+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:06.822+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:48:06.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.926+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:06.942+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.942+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:48:06.961+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.483 seconds
[2025-07-18T10:48:37.561+0000] {processor.py:186} INFO - Started process (PID=417) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:48:37.562+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:48:37.565+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.565+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:48:37.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.899+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:37.906+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:48:37.993+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.993+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:38.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.001+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:48:38.021+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.467 seconds
[2025-07-18T10:49:08.989+0000] {processor.py:186} INFO - Started process (PID=553) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:49:08.991+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:49:08.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.993+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:49:09.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:09.374+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:09.382+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:49:09.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:09.504+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:09.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:09.515+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:49:09.536+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.554 seconds
[2025-07-18T10:49:39.917+0000] {processor.py:186} INFO - Started process (PID=691) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:49:39.918+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:49:39.921+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.920+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:49:40.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.119+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:40.130+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:49:40.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.237+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:40.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.248+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:49:40.269+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.357 seconds
[2025-07-18T10:50:10.786+0000] {processor.py:186} INFO - Started process (PID=837) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:50:10.787+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:50:10.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.789+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:50:11.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.051+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:11.059+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:50:11.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.157+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:11.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.168+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:50:11.189+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.409 seconds
[2025-07-18T10:50:41.577+0000] {processor.py:186} INFO - Started process (PID=973) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:50:41.578+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:50:41.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.580+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:50:41.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.790+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:41.800+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:50:41.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.900+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:41.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.911+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:50:41.934+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.363 seconds
[2025-07-18T10:51:12.276+0000] {processor.py:186} INFO - Started process (PID=1109) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:51:12.277+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:51:12.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.279+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:51:12.488+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.487+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:12.494+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:51:12.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.598+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:12.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.608+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:51:12.629+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.359 seconds
[2025-07-18T10:51:43.577+0000] {processor.py:186} INFO - Started process (PID=1246) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:51:43.578+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:51:43.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:43.580+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:51:43.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:43.773+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:43.784+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:51:43.903+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:43.902+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:43.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:43.915+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:51:43.939+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.368 seconds
[2025-07-18T10:52:14.047+0000] {processor.py:186} INFO - Started process (PID=1382) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:52:14.048+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:52:14.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:14.050+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:52:14.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:14.247+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:14.257+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:52:14.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:14.357+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:14.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:14.369+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:52:14.389+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.350 seconds
[2025-07-18T10:52:44.773+0000] {processor.py:186} INFO - Started process (PID=1518) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:52:44.774+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:52:44.776+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:44.776+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:52:44.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:44.972+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:44.980+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:52:45.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:45.087+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:45.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:45.099+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:52:45.123+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.356 seconds
[2025-07-18T10:53:15.317+0000] {processor.py:186} INFO - Started process (PID=1654) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:53:15.317+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:53:15.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:15.319+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:53:15.517+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:15.516+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:15.525+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:53:15.629+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:15.628+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:15.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:15.642+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:53:15.663+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.351 seconds
[2025-07-18T10:53:46.033+0000] {processor.py:186} INFO - Started process (PID=1790) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:53:46.034+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:53:46.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:46.036+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:53:46.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:46.230+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:46.241+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:53:46.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:46.345+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:46.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:46.357+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:53:46.377+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.349 seconds
[2025-07-18T10:54:17.291+0000] {processor.py:186} INFO - Started process (PID=1926) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:54:17.292+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:54:17.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:17.294+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:54:17.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:17.506+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:17.515+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:54:17.647+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:17.647+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:17.660+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:17.660+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:54:17.685+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.400 seconds
[2025-07-18T10:54:47.764+0000] {processor.py:186} INFO - Started process (PID=2062) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:54:47.765+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:54:47.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:47.768+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:54:47.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:47.956+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:47.966+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:54:48.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:48.081+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:48.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:48.093+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:54:48.114+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.355 seconds
[2025-07-18T10:55:18.589+0000] {processor.py:186} INFO - Started process (PID=2198) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:55:18.589+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:55:18.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:18.591+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:55:18.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:18.785+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:18.792+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:55:18.886+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:18.885+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:18.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:18.901+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:55:18.920+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.337 seconds
[2025-07-18T10:57:31.057+0000] {processor.py:186} INFO - Started process (PID=281) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:57:31.058+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:57:31.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.060+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:57:31.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.443+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:31.452+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:57:31.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.550+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:31.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.562+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:57:31.583+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.532 seconds
[2025-07-18T10:58:02.679+0000] {processor.py:186} INFO - Started process (PID=417) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:58:02.680+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:58:02.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.682+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:58:02.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.994+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:03.002+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:58:03.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.091+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:03.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.100+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:58:03.119+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.445 seconds
[2025-07-18T10:58:34.288+0000] {processor.py:186} INFO - Started process (PID=555) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:58:34.289+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:58:34.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.291+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:58:34.583+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.583+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:34.589+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:58:34.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.668+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:34.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.676+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:58:34.692+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.409 seconds
[2025-07-18T10:59:04.945+0000] {processor.py:186} INFO - Started process (PID=691) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:59:04.946+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:59:04.948+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.948+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:59:05.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.127+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:05.138+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:59:05.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.225+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:05.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.235+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:59:05.253+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.314 seconds
[2025-07-18T10:59:35.407+0000] {processor.py:186} INFO - Started process (PID=827) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:59:35.408+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T10:59:35.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.410+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:59:35.593+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.593+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:35.601+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T10:59:35.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.686+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:35.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.695+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T10:59:35.712+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.310 seconds
[2025-07-18T11:00:05.877+0000] {processor.py:186} INFO - Started process (PID=963) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:00:05.878+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:00:05.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.880+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:00:06.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.064+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:06.072+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:00:06.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.168+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:06.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.178+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:00:06.196+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.325 seconds
[2025-07-18T11:00:36.629+0000] {processor.py:186} INFO - Started process (PID=1102) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:00:36.630+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:00:36.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.632+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:00:36.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.838+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:36.848+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:00:36.948+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.948+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:36.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.957+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:00:36.975+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.353 seconds
[2025-07-18T11:01:07.391+0000] {processor.py:186} INFO - Started process (PID=1238) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:01:07.392+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:01:07.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.395+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:01:07.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.606+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:07.616+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:01:07.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.712+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:07.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.724+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:01:07.743+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.358 seconds
[2025-07-18T11:01:38.418+0000] {processor.py:186} INFO - Started process (PID=1376) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:01:38.419+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:01:38.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.421+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:01:38.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.612+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:38.623+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:01:38.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.726+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:38.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.740+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:01:38.761+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.348 seconds
[2025-07-18T11:02:10.148+0000] {processor.py:186} INFO - Started process (PID=1512) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:02:10.149+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:02:10.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.151+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:02:10.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.340+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:10.348+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:02:10.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.465+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:10.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.484+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:02:10.508+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.366 seconds
[2025-07-18T11:02:40.818+0000] {processor.py:186} INFO - Started process (PID=1648) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:02:40.819+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:02:40.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.822+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:02:41.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.040+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:41.049+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:02:41.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.149+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:41.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.161+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:02:41.182+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.371 seconds
[2025-07-18T11:03:11.500+0000] {processor.py:186} INFO - Started process (PID=1784) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:03:11.502+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:03:11.504+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.504+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:03:11.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.713+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:11.723+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:03:11.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.825+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:11.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.837+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:03:11.858+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.364 seconds
[2025-07-18T11:03:42.540+0000] {processor.py:186} INFO - Started process (PID=1920) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:03:42.541+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:03:42.544+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.543+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:03:42.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.754+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:42.764+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:03:42.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.883+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:42.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.895+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:03:42.917+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.382 seconds
[2025-07-18T11:04:13.371+0000] {processor.py:186} INFO - Started process (PID=2051) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:04:13.372+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:04:13.375+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.374+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:04:13.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.599+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:13.608+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:04:13.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.740+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:13.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.755+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:04:13.782+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.416 seconds
[2025-07-18T11:04:43.963+0000] {processor.py:186} INFO - Started process (PID=2187) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:04:43.964+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:04:43.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.966+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:04:44.171+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.171+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:44.181+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:04:44.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.283+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:44.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.296+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:04:44.317+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.360 seconds
[2025-07-18T11:05:14.758+0000] {processor.py:186} INFO - Started process (PID=2323) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:05:14.759+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:05:14.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:14.761+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:05:14.999+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:14.998+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:15.008+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:05:15.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.116+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:15.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.128+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:05:15.152+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.401 seconds
[2025-07-18T11:06:53.777+0000] {processor.py:186} INFO - Started process (PID=293) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:06:53.778+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:06:53.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:53.781+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:06:54.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.150+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:54.157+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:06:54.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.247+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:54.258+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.257+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:06:54.276+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.504 seconds
[2025-07-18T11:07:25.113+0000] {processor.py:186} INFO - Started process (PID=434) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:07:25.114+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:07:25.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.116+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:07:25.502+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.502+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:25.512+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:07:25.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.627+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:25.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.636+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:07:25.650+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.544 seconds
[2025-07-18T11:07:56.069+0000] {processor.py:186} INFO - Started process (PID=575) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:07:56.070+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:07:56.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.072+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:07:56.266+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.266+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:56.274+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:07:56.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.386+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:56.399+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.399+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:07:56.421+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.358 seconds
[2025-07-18T11:08:26.985+0000] {processor.py:186} INFO - Started process (PID=716) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:08:26.986+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:08:26.989+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.988+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:08:27.182+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.182+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:27.191+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:08:27.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.289+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:27.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.300+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:08:27.319+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.339 seconds
[2025-07-18T11:08:57.850+0000] {processor.py:186} INFO - Started process (PID=857) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:08:57.851+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:08:57.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.853+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:08:58.067+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.067+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:58.075+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:08:58.182+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.182+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:58.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.196+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:08:58.216+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.372 seconds
[2025-07-18T11:09:28.851+0000] {processor.py:186} INFO - Started process (PID=998) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:09:28.852+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:09:28.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.854+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:09:29.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.050+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:29.059+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:09:29.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.155+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:29.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.166+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:09:29.208+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.364 seconds
[2025-07-18T11:09:59.891+0000] {processor.py:186} INFO - Started process (PID=1139) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:09:59.892+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:09:59.896+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:59.895+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:10:00.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.094+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:00.103+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:10:00.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.202+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:00.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.213+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:10:00.233+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.349 seconds
[2025-07-18T11:10:30.478+0000] {processor.py:186} INFO - Started process (PID=1280) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:10:30.479+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:10:30.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:30.482+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:10:30.704+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:30.703+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:30.712+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:10:30.839+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:30.839+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:30.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:30.854+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:10:30.877+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.407 seconds
[2025-07-18T11:11:01.128+0000] {processor.py:186} INFO - Started process (PID=1421) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:11:01.129+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:11:01.131+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.131+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:11:01.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.323+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:01.333+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:11:01.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.439+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:01.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.456+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:11:01.480+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.358 seconds
[2025-07-18T11:11:32.327+0000] {processor.py:186} INFO - Started process (PID=1564) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:11:32.328+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:11:32.331+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.331+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:11:32.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.546+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:32.556+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:11:32.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.654+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:32.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.664+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:11:32.683+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.362 seconds
[2025-07-18T11:12:03.114+0000] {processor.py:186} INFO - Started process (PID=1705) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:12:03.115+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:12:03.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.117+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:12:03.331+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.331+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:03.343+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:12:03.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.441+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:03.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.454+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:12:03.474+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.368 seconds
[2025-07-18T11:12:33.626+0000] {processor.py:186} INFO - Started process (PID=1846) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:12:33.627+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:12:33.630+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.630+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:12:33.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.821+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:33.830+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:12:33.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.934+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:33.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.946+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:12:33.966+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.346 seconds
[2025-07-18T11:13:05.385+0000] {processor.py:186} INFO - Started process (PID=1987) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:13:05.386+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:13:05.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.388+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:13:05.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.581+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:05.590+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:13:05.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.680+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:05.690+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.689+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:13:05.706+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.326 seconds
[2025-07-18T11:13:36.236+0000] {processor.py:186} INFO - Started process (PID=2128) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:13:36.237+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:13:36.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.239+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:13:36.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.432+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:36.441+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:13:36.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.540+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:36.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.551+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:13:36.571+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.341 seconds
[2025-07-18T11:14:07.067+0000] {processor.py:186} INFO - Started process (PID=2267) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:14:07.068+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:14:07.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:07.070+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:14:07.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:07.319+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:07.330+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:14:07.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:07.461+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:07.477+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:07.477+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:14:07.502+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.442 seconds
[2025-07-18T11:14:37.618+0000] {processor.py:186} INFO - Started process (PID=2408) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:14:37.619+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:14:37.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.622+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:14:37.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.848+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:37.858+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:14:37.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.973+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:37.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.984+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:14:38.007+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.396 seconds
[2025-07-18T11:15:08.271+0000] {processor.py:186} INFO - Started process (PID=2549) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:15:08.272+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:15:08.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.275+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:15:08.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.489+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:08.500+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:15:08.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.615+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:08.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.628+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:15:08.645+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.381 seconds
[2025-07-18T11:15:39.247+0000] {processor.py:186} INFO - Started process (PID=2692) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:15:39.248+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:15:39.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.250+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:15:39.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.451+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:39.462+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:15:39.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.563+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:39.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.573+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:15:39.592+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.351 seconds
[2025-07-18T11:16:10.214+0000] {processor.py:186} INFO - Started process (PID=2833) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:16:10.215+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:16:10.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.217+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:16:10.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.415+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:10.425+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:16:10.534+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.533+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:10.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.546+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:16:10.566+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.357 seconds
[2025-07-18T11:16:40.709+0000] {processor.py:186} INFO - Started process (PID=2974) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:16:40.710+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:16:40.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:40.712+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:16:40.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:40.908+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:40.915+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:16:41.013+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:41.013+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:41.023+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:41.023+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:16:41.041+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.338 seconds
[2025-07-18T11:17:12.310+0000] {processor.py:186} INFO - Started process (PID=3115) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:17:12.311+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:17:12.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.312+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:17:12.566+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.566+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:12.579+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:17:12.704+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.703+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:12.717+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.717+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:17:12.740+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.437 seconds
[2025-07-18T11:17:42.994+0000] {processor.py:186} INFO - Started process (PID=3256) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:17:42.995+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:17:42.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.998+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:17:43.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.230+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:43.239+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:17:43.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.347+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:43.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.360+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:17:43.383+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.396 seconds
[2025-07-18T11:18:13.839+0000] {processor.py:186} INFO - Started process (PID=3397) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:18:13.840+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:18:13.842+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:13.842+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:18:14.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.065+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:14.075+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:18:14.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.227+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:14.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.242+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:18:14.265+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.432 seconds
[2025-07-18T11:18:44.346+0000] {processor.py:186} INFO - Started process (PID=3538) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:18:44.347+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:18:44.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.349+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:18:44.556+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.555+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:44.565+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:18:44.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.674+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:44.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.687+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:18:44.709+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.368 seconds
[2025-07-18T11:19:15.527+0000] {processor.py:186} INFO - Started process (PID=3682) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:19:15.528+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:19:15.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.532+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:19:15.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.798+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:15.808+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:19:15.936+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.936+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:15.950+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.950+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:19:15.975+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.455 seconds
[2025-07-18T11:19:46.413+0000] {processor.py:186} INFO - Started process (PID=3825) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:19:46.414+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:19:46.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:46.416+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:19:46.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:46.628+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:46.638+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:19:46.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:46.731+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:46.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:46.741+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:19:46.759+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.353 seconds
[2025-07-18T11:20:17.378+0000] {processor.py:186} INFO - Started process (PID=3966) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:20:17.379+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:20:17.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.381+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:20:17.602+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.602+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:17.612+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:20:17.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.721+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:17.734+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.734+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:20:17.756+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.385 seconds
