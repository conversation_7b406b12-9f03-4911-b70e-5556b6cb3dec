#!/usr/bin/env python3
"""
Скрипт для экспорта данных о затратах из Redis в CSV файл и Google Sheets
Формат таблицы соответствует скриншоту с колонками:
Название, <PERSON><PERSON><PERSON> (input), <PERSON><PERSON><PERSON> (output), <PERSON><PERSON><PERSON> (Всего), Стоимость (input) USD, Стоимость (output) USD, Стоимость (Всего) USD
"""

import redis
import json
import csv
import os
import gspread
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional
import argparse
from google.oauth2.service_account import Credentials


def connect_to_redis():
    """Подключение к Redis"""
    try:
        redis_host = os.getenv('REDIS_HOST', 'redis')
        r = redis.Redis(host=redis_host, port=6379, db=0, decode_responses=True)
        r.ping()
        print("✅ Подключение к Redis успешно")
        return r
    except Exception as e:
        print(f"❌ Ошибка подключения к Redis: {e}")
        return None


def get_cost_data_from_redis(r: redis.Redis, days: int = 30) -> List[Dict[str, Any]]:
    """
    Получает данные о затратах из Redis и группирует по DAG
    
    Args:
        r: Клиент Redis
        days: Количество дней для выборки
        
    Returns:
        Список словарей с агрегированными данными по DAG
    """
    cost_data = {}
    cutoff_date = datetime.now() - timedelta(days=days)
    
    print(f"📊 Получение данных за последние {days} дней...")
    
    # Получаем все ключи cost_tracking
    cost_keys = []
    for key in r.scan_iter("cost_tracking:*"):
        cost_keys.append(key)
    
    print(f"🔍 Найдено {len(cost_keys)} записей в Redis")
    
    for key in cost_keys:
        try:
            data = json.loads(r.get(key))
            
            # Проверяем дату записи
            timestamp_str = data.get('timestamp', '')
            if timestamp_str:
                try:
                    record_date = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00').replace('+00:00', ''))
                    if record_date < cutoff_date:
                        continue
                except:
                    pass
            
            # Группируем по DAG ID
            dag_id = data.get('dag_id', 'unknown')
            
            if dag_id not in cost_data:
                cost_data[dag_id] = {
                    'name': dag_id,
                    'tokens_input': 0,
                    'tokens_output': 0,
                    'tokens_total': 0,
                    'cost_input': 0.0,
                    'cost_output': 0.0,
                    'cost_total': 0.0,
                    'operations_count': 0
                }
            
            # Агрегируем данные
            cost_data[dag_id]['tokens_input'] += data.get('tokens_input', 0)
            cost_data[dag_id]['tokens_output'] += data.get('tokens_output', 0)
            cost_data[dag_id]['tokens_total'] += data.get('total_tokens', data.get('tokens_input', 0) + data.get('tokens_output', 0))
            cost_data[dag_id]['cost_input'] += data.get('cost_input', 0.0)
            cost_data[dag_id]['cost_output'] += data.get('cost_output', 0.0)
            cost_data[dag_id]['cost_total'] += data.get('total_cost', 0.0)
            cost_data[dag_id]['operations_count'] += 1
            
        except Exception as e:
            print(f"⚠️ Ошибка обработки ключа {key}: {e}")
            continue
    
    # Преобразуем в список и сортируем по общей стоимости
    result = list(cost_data.values())
    result.sort(key=lambda x: x['cost_total'], reverse=True)
    
    print(f"✅ Обработано {len(result)} DAG с данными")
    return result


def export_to_csv(cost_data: List[Dict[str, Any]], filename: str = None) -> str:
    """Экспортирует данные в CSV файл"""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"cost_data_export_{timestamp}.csv"
    
    fieldnames = [
        'Название',
        'Tokens (input)',
        'Tokens (output)', 
        'Tokens (Всего)',
        'Стоимость (input) USD',
        'Стоимость (output) USD',
        'Стоимость (Всего) USD'
    ]
    
    print(f"💾 Экспорт в CSV: {filename}")
    
    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for row in cost_data:
            csv_row = {
                'Название': row['name'],
                'Tokens (input)': row['tokens_input'],
                'Tokens (output)': row['tokens_output'],
                'Tokens (Всего)': row['tokens_total'],
                'Стоимость (input) USD': round(row['cost_input'], 6),
                'Стоимость (output) USD': round(row['cost_output'], 6),
                'Стоимость (Всего) USD': round(row['cost_total'], 6)
            }
            writer.writerow(csv_row)
        
        # Добавляем итоговую строку
        total_row = {
            'Название': 'ИТОГО ЗА ПОЛЬЗОВАТЕЛЯ:',
            'Tokens (input)': sum(row['tokens_input'] for row in cost_data),
            'Tokens (output)': sum(row['tokens_output'] for row in cost_data),
            'Tokens (Всего)': sum(row['tokens_total'] for row in cost_data),
            'Стоимость (input) USD': round(sum(row['cost_input'] for row in cost_data), 2),
            'Стоимость (output) USD': round(sum(row['cost_output'] for row in cost_data), 2),
            'Стоимость (Всего) USD': round(sum(row['cost_total'] for row in cost_data), 2)
        }
        writer.writerow(total_row)
    
    print(f"✅ CSV экспорт завершен: {len(cost_data)} записей + итоговая строка")
    return filename


def upload_to_gsheets(
    cost_data: List[Dict[str, Any]], 
    creds_path: str,
    spreadsheet_id: Optional[str] = None,
    sheet_name: str = "Cost Tracking"
) -> bool:
    """
    Загружает данные в Google Sheets с форматированием как на скриншоте
    
    Args:
        cost_data: Данные о затратах
        creds_path: Путь к файлу с учетными данными
        spreadsheet_id: ID существующей таблицы (если None, создается новая)
        sheet_name: Имя листа
        
    Returns:
        True если успешно, False в противном случае
    """
    try:
        print(f"📤 Загрузка в Google Sheets...")
        
        # Проверяем файл с учетными данными
        if not os.path.exists(creds_path):
            print(f"❌ Файл с учетными данными не найден: {creds_path}")
            return False
        
        # Настройка аутентификации
        scopes = [
            'https://www.googleapis.com/auth/spreadsheets',
            'https://www.googleapis.com/auth/drive'
        ]
        
        credentials = Credentials.from_service_account_file(creds_path, scopes=scopes)
        gc = gspread.authorize(credentials)
        
        # Открываем или создаем таблицу
        if spreadsheet_id:
            try:
                spreadsheet = gc.open_by_key(spreadsheet_id)
                print(f"📋 Открыта существующая таблица")
            except Exception as e:
                print(f"❌ Не удалось открыть таблицу с ID {spreadsheet_id}: {e}")
                return False
        else:
            # Создаем новую таблицу
            spreadsheet_name = f"Biome AI Cost Tracking {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            spreadsheet = gc.create(spreadsheet_name)
            print(f"📋 Создана новая таблица: {spreadsheet.url}")
        
        # Получаем или создаем лист
        try:
            worksheet = spreadsheet.worksheet(sheet_name)
            # Очищаем существующий лист
            worksheet.clear()
        except:
            worksheet = spreadsheet.add_worksheet(title=sheet_name, rows=len(cost_data) + 10, cols=10)
        
        # Подготавливаем заголовки как на скриншоте
        headers = [
            'Название',
            'Tokens (input)',
            'Tokens (output)', 
            'Tokens (Всего)',
            'Стоимость (input) USD',
            'Стоимость (output) USD',
            'Стоимость (Всего) USD'
        ]
        
        # Добавляем заголовки
        worksheet.insert_row(headers, 1)
        
        # Подготавливаем данные для массовой загрузки
        rows_data = []
        for item in cost_data:
            row = [
                item['name'],
                item['tokens_input'],
                item['tokens_output'],
                item['tokens_total'],
                round(item['cost_input'], 6),
                round(item['cost_output'], 6),
                round(item['cost_total'], 6)
            ]
            rows_data.append(row)
        
        # Добавляем итоговую строку
        total_row = [
            'ИТОГО ЗА ПОЛЬЗОВАТЕЛЯ:',
            sum(item['tokens_input'] for item in cost_data),
            sum(item['tokens_output'] for item in cost_data),
            sum(item['tokens_total'] for item in cost_data),
            round(sum(item['cost_input'] for item in cost_data), 2),
            round(sum(item['cost_output'] for item in cost_data), 2),
            round(sum(item['cost_total'] for item in cost_data), 2)
        ]
        rows_data.append(total_row)
        
        # Массовая загрузка данных
        if rows_data:
            worksheet.insert_rows(rows_data, 2)
        
        print(f"✅ Данные успешно загружены в Google Sheets")
        print(f"🔗 Ссылка на таблицу: {spreadsheet.url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка загрузки в Google Sheets: {e}")
        return False


def print_summary(cost_data: List[Dict[str, Any]]):
    """Выводит сводную статистику"""
    if not cost_data:
        print("📊 Нет данных для анализа")
        return
    
    total_cost = sum(item['cost_total'] for item in cost_data)
    total_tokens_input = sum(item['tokens_input'] for item in cost_data)
    total_tokens_output = sum(item['tokens_output'] for item in cost_data)
    total_operations = sum(item['operations_count'] for item in cost_data)
    
    print(f"\n📈 СВОДНАЯ СТАТИСТИКА:")
    print("=" * 60)
    print(f"💰 Общая стоимость: ${total_cost:.6f}")
    print(f"🔢 Всего операций: {total_operations}")
    print(f"📥 Входные токены: {total_tokens_input:,}")
    print(f"📤 Выходные токены: {total_tokens_output:,}")
    print(f"📊 Всего токенов: {total_tokens_input + total_tokens_output:,}")
    print(f"📋 DAG с данными: {len(cost_data)}")


def main():
    """Основная функция"""
    parser = argparse.ArgumentParser(description='Экспорт данных о затратах в CSV и Google Sheets')
    parser.add_argument('--days', type=int, default=30, help='Количество дней для выборки')
    parser.add_argument('--csv-only', action='store_true', help='Только CSV экспорт, без Google Sheets')
    parser.add_argument('--gsheets-only', action='store_true', help='Только Google Sheets, без CSV')
    parser.add_argument('--output', type=str, help='Имя CSV файла')
    parser.add_argument('--creds', type=str, default='src/secrets/service_account.json', help='Путь к файлу с учетными данными Google')
    parser.add_argument('--spreadsheet-id', type=str, help='ID существующей Google Sheets таблицы')
    parser.add_argument('--sheet-name', type=str, default='Cost Tracking', help='Имя листа в Google Sheets')
    
    args = parser.parse_args()
    
    print("🚀 ЭКСПОРТ ДАННЫХ О ЗАТРАТАХ")
    print("=" * 50)
    
    # Подключаемся к Redis
    r = connect_to_redis()
    if not r:
        return
    
    # Получаем данные
    cost_data = get_cost_data_from_redis(r, days=args.days)
    
    if not cost_data:
        print("❌ Нет данных для экспорта")
        return
    
    # Показываем статистику
    print_summary(cost_data)
    
    success = True
    
    # CSV экспорт
    if not args.gsheets_only:
        try:
            csv_file = export_to_csv(cost_data, args.output)
            print(f"✅ CSV файл создан: {csv_file}")
        except Exception as e:
            print(f"❌ Ошибка создания CSV: {e}")
            success = False
    
    # Google Sheets загрузка
    if not args.csv_only:
        try:
            gsheets_success = upload_to_gsheets(
                cost_data, 
                args.creds, 
                args.spreadsheet_id, 
                args.sheet_name
            )
            if not gsheets_success:
                success = False
        except Exception as e:
            print(f"❌ Ошибка загрузки в Google Sheets: {e}")
            success = False
    
    if success:
        print(f"\n🎉 ГОТОВО! Экспорт завершен успешно")
    else:
        print(f"\n⚠️ Экспорт завершен с ошибками")


if __name__ == "__main__":
    main()
