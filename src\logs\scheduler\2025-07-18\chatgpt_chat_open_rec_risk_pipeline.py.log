[2025-07-18T10:16:50.413+0000] {processor.py:186} INFO - Started process (PID=261) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:16:50.414+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:16:50.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.415+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:16:50.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.460+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:50.465+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:16:50.486+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.081 seconds
[2025-07-18T10:17:20.683+0000] {processor.py:186} INFO - Started process (PID=394) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:17:20.684+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:17:20.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.687+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:17:20.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.717+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.721+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:17:20.738+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.061 seconds
[2025-07-18T10:17:51.435+0000] {processor.py:186} INFO - Started process (PID=523) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:17:51.436+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:17:51.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.437+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:17:51.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.604+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:51.608+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:17:51.623+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.193 seconds
[2025-07-18T10:18:21.733+0000] {processor.py:186} INFO - Started process (PID=654) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:18:21.734+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:18:21.735+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.735+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:18:21.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.766+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.769+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:18:21.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.057 seconds
[2025-07-18T10:18:52.678+0000] {processor.py:186} INFO - Started process (PID=787) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:18:52.679+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:18:52.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.680+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:18:52.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.717+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:52.721+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:18:52.737+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.065 seconds
[2025-07-18T10:19:23.655+0000] {processor.py:186} INFO - Started process (PID=918) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:19:23.656+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:19:23.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.657+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:19:23.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.685+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:23.688+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:19:23.703+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.053 seconds
[2025-07-18T10:19:54.642+0000] {processor.py:186} INFO - Started process (PID=1049) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:19:54.643+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:19:54.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.644+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:19:54.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.676+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:54.680+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:19:54.697+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.060 seconds
[2025-07-18T10:20:25.639+0000] {processor.py:186} INFO - Started process (PID=1180) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:20:25.641+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:20:25.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.642+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:20:25.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.673+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:25.677+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:20:25.692+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.059 seconds
[2025-07-18T10:20:56.594+0000] {processor.py:186} INFO - Started process (PID=1311) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:20:56.595+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:20:56.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.596+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:20:56.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.631+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:56.635+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:20:56.650+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.062 seconds
[2025-07-18T10:21:27.583+0000] {processor.py:186} INFO - Started process (PID=1442) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:21:27.584+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:21:27.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.585+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:21:27.619+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.616+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:27.620+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:21:27.638+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.060 seconds
[2025-07-18T10:21:57.747+0000] {processor.py:186} INFO - Started process (PID=1573) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:21:57.748+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:21:57.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.749+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:21:57.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.780+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:57.784+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:21:57.799+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.057 seconds
[2025-07-18T10:22:28.777+0000] {processor.py:186} INFO - Started process (PID=1704) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:22:28.778+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:22:28.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.779+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:22:28.812+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.810+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:28.813+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:22:28.828+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.057 seconds
[2025-07-18T10:22:59.751+0000] {processor.py:186} INFO - Started process (PID=1835) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:22:59.752+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:22:59.753+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.753+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:22:59.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.785+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:59.789+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:22:59.804+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.059 seconds
[2025-07-18T10:23:30.610+0000] {processor.py:186} INFO - Started process (PID=1966) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:23:30.611+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:23:30.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.612+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:23:30.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.642+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:30.646+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:23:30.661+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.057 seconds
[2025-07-18T10:24:01.627+0000] {processor.py:186} INFO - Started process (PID=2097) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:24:01.628+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:24:01.630+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.630+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:24:01.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.665+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:01.669+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:24:01.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.066 seconds
[2025-07-18T10:24:31.849+0000] {processor.py:186} INFO - Started process (PID=2228) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:24:31.850+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:24:31.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.851+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:24:31.885+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.882+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.887+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:24:31.904+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.060 seconds
[2025-07-18T10:25:05.683+0000] {processor.py:186} INFO - Started process (PID=2359) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:25:05.683+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:25:05.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.684+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:25:05.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.870+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:05.879+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:25:05.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.969+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:05.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.978+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:25:06.000+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.323 seconds
[2025-07-18T10:26:23.689+0000] {processor.py:186} INFO - Started process (PID=261) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:26:23.690+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:26:23.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.692+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:26:24.075+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.075+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:24.080+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:26:24.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.175+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:24.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.185+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:26:24.202+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.519 seconds
[2025-07-18T10:26:55.842+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:26:55.844+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:26:55.846+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.846+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:26:56.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.186+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:56.194+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:26:56.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.300+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:56.309+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.309+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:26:56.330+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.493 seconds
[2025-07-18T10:27:26.881+0000] {processor.py:186} INFO - Started process (PID=523) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:27:26.882+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:27:26.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.884+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:27:27.212+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.212+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:27.218+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:27:27.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.312+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:27.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.322+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:27:27.339+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.464 seconds
[2025-07-18T10:27:57.423+0000] {processor.py:186} INFO - Started process (PID=654) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:27:57.424+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:27:57.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.426+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:27:57.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.651+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:57.662+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:27:57.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.765+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:57.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.774+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:27:57.792+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.376 seconds
[2025-07-18T10:28:28.888+0000] {processor.py:186} INFO - Started process (PID=787) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:28:28.889+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:28:28.891+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.890+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:28:29.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:29.096+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:29.106+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:28:29.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:29.201+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:29.212+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:29.211+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:28:29.230+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.348 seconds
[2025-07-18T10:28:59.745+0000] {processor.py:186} INFO - Started process (PID=918) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:28:59.746+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:28:59.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.749+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:28:59.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.983+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:59.992+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:29:00.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.105+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:00.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.116+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:29:00.137+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.398 seconds
[2025-07-18T10:29:30.423+0000] {processor.py:186} INFO - Started process (PID=1049) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:29:30.424+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:29:30.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:30.426+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:29:30.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:30.628+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:30.637+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:29:30.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:30.736+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:30.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:30.746+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:29:30.766+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.349 seconds
[2025-07-18T10:30:01.133+0000] {processor.py:186} INFO - Started process (PID=1180) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:30:01.133+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:30:01.136+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.135+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:30:01.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.325+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:01.334+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:30:01.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.440+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:01.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.452+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:30:01.474+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.346 seconds
[2025-07-18T10:30:31.667+0000] {processor.py:186} INFO - Started process (PID=1309) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:30:31.667+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:30:31.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.670+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:30:31.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.876+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:31.887+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:30:31.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.995+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:32.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.008+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:30:32.027+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.366 seconds
[2025-07-18T10:31:02.235+0000] {processor.py:186} INFO - Started process (PID=1440) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:31:02.236+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:31:02.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.238+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:31:02.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.437+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:02.453+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:31:02.556+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.556+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:02.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.566+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:31:02.583+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.353 seconds
[2025-07-18T10:31:32.698+0000] {processor.py:186} INFO - Started process (PID=1571) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:31:32.698+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:31:32.701+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.700+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:31:32.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.883+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:32.892+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:31:32.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.976+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:32.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.986+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:31:33.003+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.311 seconds
[2025-07-18T10:32:03.774+0000] {processor.py:186} INFO - Started process (PID=1704) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:32:03.775+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:32:03.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.777+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:32:04.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.001+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:04.009+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:32:04.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.124+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:04.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.135+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:32:04.157+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.391 seconds
[2025-07-18T10:32:34.213+0000] {processor.py:186} INFO - Started process (PID=1835) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:32:34.214+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:32:34.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.216+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:32:34.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.393+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:34.402+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:32:34.490+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.489+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:34.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.498+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:32:34.516+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.309 seconds
[2025-07-18T10:33:04.647+0000] {processor.py:186} INFO - Started process (PID=1966) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:33:04.648+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:33:04.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.649+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:33:04.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.825+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:04.833+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:33:04.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.925+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:04.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.935+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:33:04.952+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.311 seconds
[2025-07-18T10:33:35.237+0000] {processor.py:186} INFO - Started process (PID=2097) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:33:35.238+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:33:35.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.240+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:33:35.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.410+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:35.418+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:33:35.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.505+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:35.514+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.514+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:33:35.532+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.300 seconds
[2025-07-18T10:34:05.886+0000] {processor.py:186} INFO - Started process (PID=2228) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:34:05.887+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:34:05.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.889+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:34:06.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.102+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:06.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:34:06.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.204+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:06.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.214+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:34:06.231+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.351 seconds
[2025-07-18T10:34:36.945+0000] {processor.py:186} INFO - Started process (PID=2359) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:34:36.946+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:34:36.948+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.948+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:34:37.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.128+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:37.136+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:34:37.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.223+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:37.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.232+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:34:37.249+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.309 seconds
[2025-07-18T10:35:07.432+0000] {processor.py:186} INFO - Started process (PID=2490) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:35:07.433+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:35:07.435+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.435+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:35:07.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.632+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:07.639+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:35:07.735+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.735+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:07.745+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.744+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:35:07.767+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.341 seconds
[2025-07-18T10:35:38.523+0000] {processor.py:186} INFO - Started process (PID=2621) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:35:38.525+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:35:38.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.527+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:35:38.730+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.730+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:38.740+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:35:38.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.842+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:38.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.854+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:35:38.875+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.358 seconds
[2025-07-18T10:36:09.278+0000] {processor.py:186} INFO - Started process (PID=2752) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:36:09.279+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:36:09.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.281+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:36:09.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.483+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:09.492+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:36:09.588+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.588+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:09.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.598+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:36:09.617+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.345 seconds
[2025-07-18T10:36:39.890+0000] {processor.py:186} INFO - Started process (PID=2883) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:36:39.891+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:36:39.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.893+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:36:40.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.085+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:40.092+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:36:40.186+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.186+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:40.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.198+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:36:40.215+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.332 seconds
[2025-07-18T10:37:10.427+0000] {processor.py:186} INFO - Started process (PID=3012) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:37:10.428+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:37:10.431+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.431+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:37:10.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.637+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:10.648+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:37:10.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.753+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:10.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.766+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:37:10.785+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.365 seconds
[2025-07-18T10:37:41.613+0000] {processor.py:186} INFO - Started process (PID=3143) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:37:41.614+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:37:41.617+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.616+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:37:41.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.821+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:41.829+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:37:41.915+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.914+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:41.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.926+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:37:41.947+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.340 seconds
[2025-07-18T10:38:12.134+0000] {processor.py:186} INFO - Started process (PID=3274) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:38:12.134+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:38:12.136+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.136+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:38:12.339+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.339+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:12.347+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:38:12.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.446+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:12.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.458+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:38:12.477+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.350 seconds
[2025-07-18T10:38:43.007+0000] {processor.py:186} INFO - Started process (PID=3407) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:38:43.008+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:38:43.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.010+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:38:43.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.196+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:43.204+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:38:43.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.290+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:43.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.299+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:38:43.317+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.316 seconds
[2025-07-18T10:39:13.739+0000] {processor.py:186} INFO - Started process (PID=3538) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:39:13.739+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:39:13.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.741+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:39:13.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.926+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:13.937+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:39:14.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:14.045+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:14.059+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:14.059+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:39:14.090+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.357 seconds
[2025-07-18T10:39:44.495+0000] {processor.py:186} INFO - Started process (PID=3669) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:39:44.496+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:39:44.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.498+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:39:44.702+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.702+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:44.712+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:39:44.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.808+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:44.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.819+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:39:44.838+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.349 seconds
[2025-07-18T10:40:15.182+0000] {processor.py:186} INFO - Started process (PID=3800) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:40:15.183+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:40:15.186+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:15.185+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:40:15.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:15.416+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:15.425+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:40:15.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:15.528+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:15.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:15.541+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:40:15.559+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.384 seconds
[2025-07-18T10:40:46.280+0000] {processor.py:186} INFO - Started process (PID=3929) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:40:46.281+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:40:46.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.283+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:40:46.467+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.467+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:46.476+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:40:46.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.574+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:46.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.587+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:40:46.610+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.336 seconds
[2025-07-18T10:41:17.061+0000] {processor.py:186} INFO - Started process (PID=4060) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:41:17.063+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:41:17.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.065+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:41:17.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.279+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:17.289+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:41:17.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.395+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:17.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.407+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:41:17.428+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.373 seconds
[2025-07-18T10:41:47.667+0000] {processor.py:186} INFO - Started process (PID=4198) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:41:47.668+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:41:47.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.670+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:41:47.855+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.855+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:47.863+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:41:47.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.954+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:47.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.965+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:41:47.985+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.323 seconds
[2025-07-18T10:42:18.188+0000] {processor.py:186} INFO - Started process (PID=4334) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:42:18.189+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:42:18.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.191+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:42:18.380+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.380+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:18.389+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:42:18.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.475+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:18.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.485+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:42:18.502+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.320 seconds
[2025-07-18T10:43:00.297+0000] {processor.py:186} INFO - Started process (PID=266) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:43:00.298+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:43:00.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.300+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:43:00.614+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.614+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:00.621+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:43:00.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.711+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:00.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.722+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:43:00.739+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.447 seconds
[2025-07-18T10:43:32.409+0000] {processor.py:186} INFO - Started process (PID=402) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:43:32.410+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:43:32.412+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.411+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:43:32.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.726+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:32.733+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:43:32.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.849+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:32.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.858+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:43:32.876+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.472 seconds
[2025-07-18T10:44:03.460+0000] {processor.py:186} INFO - Started process (PID=538) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:44:03.460+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:44:03.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:03.462+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:44:03.764+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:03.764+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:03.771+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:44:03.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:03.852+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:03.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:03.860+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:44:03.877+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.422 seconds
[2025-07-18T10:44:34.242+0000] {processor.py:186} INFO - Started process (PID=674) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:44:34.243+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:44:34.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.245+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:44:34.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.449+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:34.455+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:44:34.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.555+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:34.569+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.568+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:44:34.589+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.352 seconds
[2025-07-18T10:45:05.154+0000] {processor.py:186} INFO - Started process (PID=810) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:45:05.155+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:45:05.158+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.157+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:45:05.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.359+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:05.369+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:45:05.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.471+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:05.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.483+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:45:05.506+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.360 seconds
[2025-07-18T10:45:35.760+0000] {processor.py:186} INFO - Started process (PID=946) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:45:35.761+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:45:35.764+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:35.763+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:45:35.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:35.970+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:35.978+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:45:36.076+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.076+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:36.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.086+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:45:36.105+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.351 seconds
[2025-07-18T10:46:06.768+0000] {processor.py:186} INFO - Started process (PID=1084) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:46:06.769+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:46:06.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:06.771+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:46:07.027+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.027+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:07.035+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:46:07.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.140+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:07.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.151+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:46:07.172+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.413 seconds
[2025-07-18T10:46:37.728+0000] {processor.py:186} INFO - Started process (PID=1220) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:46:37.729+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:46:37.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.732+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:46:37.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.937+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:37.945+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:46:38.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:38.063+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:38.076+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:38.076+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:46:38.102+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.381 seconds
[2025-07-18T10:47:08.287+0000] {processor.py:186} INFO - Started process (PID=1410) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:47:08.288+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:47:08.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:08.291+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:47:09.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:09.012+0000] {process_utils.py:266} INFO - Waiting up to 5 seconds for processes to exit...
[2025-07-18T10:48:05.692+0000] {processor.py:186} INFO - Started process (PID=266) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:48:05.693+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:48:05.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.695+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:48:06.013+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.012+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:06.020+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:48:06.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.108+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:06.118+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.118+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:48:06.140+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.453 seconds
[2025-07-18T10:48:36.862+0000] {processor.py:186} INFO - Started process (PID=402) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:48:36.863+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:48:36.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.865+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:48:37.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.205+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:37.213+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:48:37.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.306+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:37.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.316+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:48:37.333+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.478 seconds
[2025-07-18T10:49:07.755+0000] {processor.py:186} INFO - Started process (PID=538) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:49:07.757+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:49:07.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:07.760+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:49:08.200+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.200+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:08.210+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:49:08.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.358+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:08.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.369+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:49:08.388+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.640 seconds
[2025-07-18T10:49:38.752+0000] {processor.py:186} INFO - Started process (PID=674) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:49:38.753+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:49:38.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.755+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:49:38.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.960+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:38.969+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:49:39.075+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.074+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:39.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.085+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:49:39.105+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.359 seconds
[2025-07-18T10:50:09.817+0000] {processor.py:186} INFO - Started process (PID=812) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:50:09.818+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:50:09.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:09.820+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:50:10.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.019+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:10.027+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:50:10.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.126+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:10.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.137+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:50:10.157+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.347 seconds
[2025-07-18T10:50:40.590+0000] {processor.py:186} INFO - Started process (PID=948) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:50:40.592+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:50:40.595+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:40.595+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:50:40.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:40.801+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:40.810+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:50:40.917+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:40.917+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:40.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:40.928+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:50:40.953+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.370 seconds
[2025-07-18T10:51:11.369+0000] {processor.py:186} INFO - Started process (PID=1084) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:51:11.370+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:51:11.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:11.372+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:51:11.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:11.563+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:11.571+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:51:11.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:11.663+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:11.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:11.673+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:51:11.690+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.328 seconds
[2025-07-18T10:51:41.785+0000] {processor.py:186} INFO - Started process (PID=1221) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:51:41.785+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:51:41.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.787+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:51:41.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.980+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:41.988+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:51:42.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.079+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:42.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.089+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:51:42.107+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.328 seconds
[2025-07-18T10:52:12.219+0000] {processor.py:186} INFO - Started process (PID=1357) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:52:12.220+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:52:12.222+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.222+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:52:12.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.413+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:12.422+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:52:12.520+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.520+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:12.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.530+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:52:12.550+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.336 seconds
[2025-07-18T10:52:42.886+0000] {processor.py:186} INFO - Started process (PID=1493) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:52:42.887+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:52:42.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.889+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:52:43.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.093+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:43.104+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:52:43.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.197+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:43.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.210+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:52:43.230+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.350 seconds
[2025-07-18T10:53:13.427+0000] {processor.py:186} INFO - Started process (PID=1629) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:53:13.428+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:53:13.431+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.430+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:53:13.626+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.625+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:13.633+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:53:13.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.736+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:13.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.748+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:53:13.767+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.346 seconds
[2025-07-18T10:53:44.277+0000] {processor.py:186} INFO - Started process (PID=1765) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:53:44.278+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:53:44.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.280+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:53:44.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.459+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:44.468+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:53:44.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.561+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:44.572+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.572+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:53:44.588+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.316 seconds
[2025-07-18T10:54:15.059+0000] {processor.py:186} INFO - Started process (PID=1899) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:54:15.060+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:54:15.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:15.062+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:54:15.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:15.279+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:15.287+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:54:15.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:15.387+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:15.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:15.398+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:54:15.418+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.367 seconds
[2025-07-18T10:54:45.554+0000] {processor.py:186} INFO - Started process (PID=2035) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:54:45.555+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:54:45.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.557+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:54:45.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.746+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:45.754+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:54:45.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.861+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:45.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.871+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:54:45.891+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.344 seconds
[2025-07-18T10:55:15.992+0000] {processor.py:186} INFO - Started process (PID=2171) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:55:15.992+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:55:15.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.994+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:55:16.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:16.184+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:16.192+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:55:16.282+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:16.282+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:16.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:16.292+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:55:16.310+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.324 seconds
[2025-07-18T10:55:46.651+0000] {processor.py:186} INFO - Started process (PID=2371) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:55:46.652+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:55:46.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.654+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:55:47.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:47.039+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:47.054+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:55:47.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:47.552+0000] {process_utils.py:266} INFO - Waiting up to 5 seconds for processes to exit...
[2025-07-18T10:57:30.312+0000] {processor.py:186} INFO - Started process (PID=266) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:57:30.313+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:57:30.315+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.315+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:57:30.662+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.662+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:30.668+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:57:30.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.760+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:30.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.770+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:57:30.789+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.484 seconds
[2025-07-18T10:58:01.811+0000] {processor.py:186} INFO - Started process (PID=402) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:58:01.814+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:58:01.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.816+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:58:02.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.139+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:02.153+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:58:02.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.238+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:02.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.248+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:58:02.265+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.459 seconds
[2025-07-18T10:58:33.356+0000] {processor.py:186} INFO - Started process (PID=540) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:58:33.357+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:58:33.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:33.359+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:58:33.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:33.670+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:33.677+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:58:33.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:33.754+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:33.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:33.763+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:58:33.780+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.429 seconds
[2025-07-18T10:59:04.126+0000] {processor.py:186} INFO - Started process (PID=676) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:59:04.127+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:59:04.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.129+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:59:04.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.333+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:04.339+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:59:04.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.437+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:04.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.447+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:59:04.465+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.345 seconds
[2025-07-18T10:59:34.630+0000] {processor.py:186} INFO - Started process (PID=812) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:59:34.631+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T10:59:34.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.633+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:59:34.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.811+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:34.819+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T10:59:34.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.921+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:34.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.932+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T10:59:34.950+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.325 seconds
[2025-07-18T11:00:05.111+0000] {processor.py:186} INFO - Started process (PID=946) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:00:05.112+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:00:05.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.114+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:00:05.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.310+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:05.319+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:00:05.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.408+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:05.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.418+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:00:05.435+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.330 seconds
[2025-07-18T11:00:36.142+0000] {processor.py:186} INFO - Started process (PID=1082) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:00:36.143+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:00:36.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.145+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:00:36.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.331+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:36.340+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:00:36.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.430+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:36.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.447+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:00:36.466+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.330 seconds
[2025-07-18T11:01:06.664+0000] {processor.py:186} INFO - Started process (PID=1220) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:01:06.665+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:01:06.667+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.667+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:01:06.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.852+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:06.861+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:01:06.953+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.952+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:06.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.962+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:01:06.981+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.323 seconds
[2025-07-18T11:01:37.608+0000] {processor.py:186} INFO - Started process (PID=1354) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:01:37.609+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:01:37.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.611+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:01:37.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.805+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:37.814+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:01:37.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.908+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:37.919+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.919+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:01:37.940+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.337 seconds
[2025-07-18T11:02:09.339+0000] {processor.py:186} INFO - Started process (PID=1492) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:02:09.340+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:02:09.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.342+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:02:09.520+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.520+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:09.529+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:02:09.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.621+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:09.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.631+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:02:09.650+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.317 seconds
[2025-07-18T11:02:39.996+0000] {processor.py:186} INFO - Started process (PID=1628) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:02:39.996+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:02:39.999+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:39.999+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:02:40.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.203+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:40.212+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:02:40.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.324+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:40.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.340+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:02:40.362+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.372 seconds
[2025-07-18T11:03:10.611+0000] {processor.py:186} INFO - Started process (PID=1764) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:03:10.613+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:03:10.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.615+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:03:10.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.843+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:10.851+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:03:10.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.954+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:10.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.968+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:03:10.987+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.382 seconds
[2025-07-18T11:03:41.668+0000] {processor.py:186} INFO - Started process (PID=1900) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:03:41.669+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:03:41.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.671+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:03:41.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.879+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:41.891+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:03:41.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.997+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:42.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.009+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:03:42.027+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.365 seconds
[2025-07-18T11:04:12.523+0000] {processor.py:186} INFO - Started process (PID=2036) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:04:12.524+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:04:12.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:12.526+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:04:12.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:12.739+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:12.750+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:04:12.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:12.869+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:12.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:12.879+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:04:12.905+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.388 seconds
[2025-07-18T11:04:43.122+0000] {processor.py:186} INFO - Started process (PID=2172) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:04:43.123+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:04:43.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.125+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:04:43.338+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.338+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:43.347+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:04:43.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.453+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:43.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.463+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:04:43.485+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.371 seconds
[2025-07-18T11:05:13.880+0000] {processor.py:186} INFO - Started process (PID=2308) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:05:13.881+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:05:13.885+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.885+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:05:14.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:14.109+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:14.118+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:05:14.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:14.232+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:14.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:14.245+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:05:14.268+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.395 seconds
[2025-07-18T11:06:53.058+0000] {processor.py:186} INFO - Started process (PID=278) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:06:53.059+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:06:53.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:53.061+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:06:53.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:53.417+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:53.425+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:06:53.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:53.525+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:53.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:53.536+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:06:53.555+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.504 seconds
[2025-07-18T11:07:24.378+0000] {processor.py:186} INFO - Started process (PID=419) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:07:24.379+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:07:24.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:24.381+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:07:24.793+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:24.793+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:24.799+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:07:24.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:24.915+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:24.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:24.930+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:07:24.965+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.594 seconds
[2025-07-18T11:07:55.635+0000] {processor.py:186} INFO - Started process (PID=560) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:07:55.636+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:07:55.639+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.639+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:07:55.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.861+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:55.869+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:07:55.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.965+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:55.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.976+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:07:55.999+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.369 seconds
[2025-07-18T11:08:26.568+0000] {processor.py:186} INFO - Started process (PID=701) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:08:26.569+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:08:26.572+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.571+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:08:26.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.773+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:26.782+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:08:26.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.870+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:26.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.879+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:08:26.899+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.337 seconds
[2025-07-18T11:08:57.424+0000] {processor.py:186} INFO - Started process (PID=844) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:08:57.425+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:08:57.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.427+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:08:57.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.637+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:57.646+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:08:57.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.754+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:57.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.768+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:08:57.789+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.372 seconds
[2025-07-18T11:09:28.429+0000] {processor.py:186} INFO - Started process (PID=985) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:09:28.430+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:09:28.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.433+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:09:28.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.623+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:28.632+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:09:28.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.725+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:28.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.736+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:09:28.760+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.337 seconds
[2025-07-18T11:09:59.053+0000] {processor.py:186} INFO - Started process (PID=1126) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:09:59.055+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:09:59.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:59.058+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:09:59.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:59.269+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:59.276+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:09:59.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:59.385+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:59.395+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:59.395+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:09:59.416+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.372 seconds
[2025-07-18T11:10:29.613+0000] {processor.py:186} INFO - Started process (PID=1267) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:10:29.615+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:10:29.617+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.617+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:10:29.868+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.868+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:29.874+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:10:29.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.973+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:29.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.984+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:10:30.005+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.398 seconds
[2025-07-18T11:11:00.395+0000] {processor.py:186} INFO - Started process (PID=1406) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:11:00.396+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:11:00.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.398+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:11:00.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.577+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:00.584+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:11:00.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.673+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:00.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.682+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:11:00.700+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.311 seconds
[2025-07-18T11:11:31.547+0000] {processor.py:186} INFO - Started process (PID=1549) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:11:31.548+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:11:31.550+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.550+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:11:31.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.746+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:31.755+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:11:31.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.860+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:31.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.871+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:11:31.890+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.349 seconds
[2025-07-18T11:12:02.003+0000] {processor.py:186} INFO - Started process (PID=1688) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:12:02.004+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:12:02.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:02.007+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:12:02.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:02.195+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:02.202+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:12:02.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:02.295+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:02.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:02.306+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:12:02.325+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.329 seconds
[2025-07-18T11:12:32.830+0000] {processor.py:186} INFO - Started process (PID=1831) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:12:32.831+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:12:32.833+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.833+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:12:33.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.017+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:33.025+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:12:33.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.117+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:33.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.128+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:12:33.146+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.322 seconds
[2025-07-18T11:13:04.650+0000] {processor.py:186} INFO - Started process (PID=1972) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:13:04.651+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:13:04.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.654+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:13:04.833+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.833+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:04.842+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:13:04.948+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.948+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:04.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.957+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:13:04.974+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.331 seconds
[2025-07-18T11:13:35.483+0000] {processor.py:186} INFO - Started process (PID=2113) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:13:35.484+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:13:35.486+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.486+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:13:35.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.665+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:35.675+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:13:35.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.784+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:35.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.798+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:13:35.821+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.343 seconds
[2025-07-18T11:14:06.147+0000] {processor.py:186} INFO - Started process (PID=2254) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:14:06.148+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:14:06.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:06.150+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:14:06.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:06.400+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:06.408+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:14:06.519+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:06.519+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:06.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:06.532+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:14:06.555+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.415 seconds
[2025-07-18T11:14:36.797+0000] {processor.py:186} INFO - Started process (PID=2395) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:14:36.798+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:14:36.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:36.800+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:14:36.999+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:36.998+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:37.007+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:14:37.123+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.123+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:37.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.135+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:14:37.155+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.364 seconds
[2025-07-18T11:15:07.423+0000] {processor.py:186} INFO - Started process (PID=2536) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:15:07.424+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:15:07.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:07.427+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:15:07.626+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:07.625+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:07.634+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:15:07.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:07.749+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:07.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:07.762+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:15:07.783+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.365 seconds
[2025-07-18T11:15:38.423+0000] {processor.py:186} INFO - Started process (PID=2675) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:15:38.424+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:15:38.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.426+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:15:38.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.637+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:38.646+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:15:38.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.739+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:38.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.750+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:15:38.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.352 seconds
[2025-07-18T11:16:09.352+0000] {processor.py:186} INFO - Started process (PID=2818) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:16:09.353+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:16:09.355+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:09.355+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:16:09.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:09.559+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:09.570+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:16:09.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:09.687+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:09.701+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:09.701+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:16:09.725+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.380 seconds
[2025-07-18T11:16:39.866+0000] {processor.py:186} INFO - Started process (PID=2959) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:16:39.867+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:16:39.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.869+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:16:40.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:40.083+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:40.090+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:16:40.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:40.191+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:40.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:40.202+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:16:40.221+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.361 seconds
[2025-07-18T11:17:11.450+0000] {processor.py:186} INFO - Started process (PID=3100) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:17:11.451+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:17:11.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:11.453+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:17:11.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:11.688+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:11.697+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:17:11.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:11.812+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:11.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:11.824+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:17:11.846+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.401 seconds
[2025-07-18T11:17:42.174+0000] {processor.py:186} INFO - Started process (PID=3241) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:17:42.175+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:17:42.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.177+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:17:42.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.378+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:42.388+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:17:42.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.488+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:42.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.500+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:17:42.520+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.352 seconds
[2025-07-18T11:18:12.969+0000] {processor.py:186} INFO - Started process (PID=3382) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:18:12.970+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:18:12.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.972+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:18:13.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:13.217+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:13.225+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:18:13.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:13.331+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:13.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:13.344+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:18:13.365+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.404 seconds
[2025-07-18T11:18:43.557+0000] {processor.py:186} INFO - Started process (PID=3523) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:18:43.558+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:18:43.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.561+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:18:43.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.775+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:43.783+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:18:43.881+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.880+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:43.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.893+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:18:43.910+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.360 seconds
[2025-07-18T11:19:14.871+0000] {processor.py:186} INFO - Started process (PID=3662) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:19:14.873+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:19:14.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:14.876+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:19:15.211+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.211+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:15.219+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:19:15.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.334+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:15.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.346+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:19:15.375+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.513 seconds
[2025-07-18T11:19:45.622+0000] {processor.py:186} INFO - Started process (PID=3810) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:19:45.623+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:19:45.625+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.625+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:19:45.831+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.831+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:45.840+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:19:45.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.947+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:45.963+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.963+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:19:45.984+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.369 seconds
[2025-07-18T11:20:16.489+0000] {processor.py:186} INFO - Started process (PID=3951) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:20:16.491+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:20:16.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:16.493+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:20:16.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:16.725+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:16.733+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:20:16.836+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:16.835+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:16.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:16.847+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:20:16.875+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.393 seconds
