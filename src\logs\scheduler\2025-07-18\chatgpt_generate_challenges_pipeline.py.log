[2025-07-18T10:16:50.291+0000] {processor.py:186} INFO - Started process (PID=253) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:16:50.292+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:16:50.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.295+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:16:50.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.338+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:50.343+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:16:50.363+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.080 seconds
[2025-07-18T10:17:20.592+0000] {processor.py:186} INFO - Started process (PID=384) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:17:20.593+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:17:20.595+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.595+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:17:20.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.625+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.629+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:17:20.646+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.059 seconds
[2025-07-18T10:17:51.182+0000] {processor.py:186} INFO - Started process (PID=513) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:17:51.183+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:17:51.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.184+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:17:51.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.377+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:51.380+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:17:51.394+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.219 seconds
[2025-07-18T10:18:21.630+0000] {processor.py:186} INFO - Started process (PID=646) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:18:21.631+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:18:21.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.632+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:18:21.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.663+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.666+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:18:21.681+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.056 seconds
[2025-07-18T10:18:52.499+0000] {processor.py:186} INFO - Started process (PID=775) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:18:52.499+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:18:52.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.500+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:18:52.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.529+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:52.532+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:18:52.547+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.054 seconds
[2025-07-18T10:19:23.470+0000] {processor.py:186} INFO - Started process (PID=908) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:19:23.470+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:19:23.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.471+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:19:23.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.501+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:23.506+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:19:23.522+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.058 seconds
[2025-07-18T10:19:54.450+0000] {processor.py:186} INFO - Started process (PID=1037) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:19:54.451+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:19:54.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.452+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:19:54.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.488+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:54.492+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:19:54.509+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.066 seconds
[2025-07-18T10:20:25.457+0000] {processor.py:186} INFO - Started process (PID=1168) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:20:25.458+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:20:25.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.459+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:20:25.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.490+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:25.493+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:20:25.510+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.058 seconds
[2025-07-18T10:20:56.397+0000] {processor.py:186} INFO - Started process (PID=1299) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:20:56.398+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:20:56.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.399+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:20:56.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.430+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:56.434+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:20:56.451+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.059 seconds
[2025-07-18T10:21:27.390+0000] {processor.py:186} INFO - Started process (PID=1430) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:21:27.391+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:21:27.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.392+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:21:27.431+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.428+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:27.432+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:21:27.448+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.063 seconds
[2025-07-18T10:21:57.563+0000] {processor.py:186} INFO - Started process (PID=1561) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:21:57.563+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:21:57.565+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.564+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:21:57.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.594+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:57.597+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:21:57.612+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.055 seconds
[2025-07-18T10:22:28.608+0000] {processor.py:186} INFO - Started process (PID=1692) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:22:28.609+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:22:28.610+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.610+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:22:28.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.638+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:28.642+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:22:28.658+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.056 seconds
[2025-07-18T10:22:59.565+0000] {processor.py:186} INFO - Started process (PID=1823) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:22:59.566+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:22:59.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.567+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:22:59.603+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.600+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:59.605+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:22:59.622+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.064 seconds
[2025-07-18T10:23:30.516+0000] {processor.py:186} INFO - Started process (PID=1956) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:23:30.517+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:23:30.518+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.518+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:23:30.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.548+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:30.552+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:23:30.568+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.057 seconds
[2025-07-18T10:24:01.404+0000] {processor.py:186} INFO - Started process (PID=2085) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:24:01.406+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:24:01.409+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.408+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:24:01.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.446+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:01.450+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:24:01.466+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.069 seconds
[2025-07-18T10:24:31.651+0000] {processor.py:186} INFO - Started process (PID=2216) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:24:31.652+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:24:31.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.653+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:24:31.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.686+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.689+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:24:31.708+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.063 seconds
[2025-07-18T10:25:04.987+0000] {processor.py:186} INFO - Started process (PID=2347) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:25:04.988+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:25:04.989+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.989+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:25:05.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.179+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:05.187+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:25:05.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.270+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:05.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.280+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:25:05.302+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.320 seconds
[2025-07-18T10:26:23.135+0000] {processor.py:186} INFO - Started process (PID=251) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:26:23.136+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:26:23.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.138+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:26:23.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.496+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:23.506+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:26:23.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.628+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:23.638+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.638+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:26:23.653+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.524 seconds
[2025-07-18T10:26:55.293+0000] {processor.py:186} INFO - Started process (PID=382) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:26:55.294+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:26:55.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.296+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:26:55.640+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.640+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:55.647+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:26:55.758+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.758+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:55.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.769+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:26:55.788+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.501 seconds
[2025-07-18T10:27:26.329+0000] {processor.py:186} INFO - Started process (PID=513) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:27:26.331+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:27:26.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.334+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:27:26.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.693+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:26.700+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:27:26.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.797+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:26.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.808+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:27:26.825+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.503 seconds
[2025-07-18T10:27:57.031+0000] {processor.py:186} INFO - Started process (PID=644) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:27:57.032+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:27:57.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.034+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:27:57.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.224+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:57.232+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:27:57.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.346+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:57.355+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.355+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:27:57.373+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.348 seconds
[2025-07-18T10:28:28.492+0000] {processor.py:186} INFO - Started process (PID=777) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:28:28.493+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:28:28.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.495+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:28:28.698+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.698+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:28.708+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:28:28.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.813+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:28.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.825+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:28:28.845+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.359 seconds
[2025-07-18T10:28:58.957+0000] {processor.py:186} INFO - Started process (PID=906) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:28:58.958+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:28:58.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.961+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:28:59.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.168+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:59.176+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:28:59.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.267+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:59.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.279+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:28:59.301+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.355 seconds
[2025-07-18T10:29:29.608+0000] {processor.py:186} INFO - Started process (PID=1037) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:29:29.609+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:29:29.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.612+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:29:29.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.826+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:29.836+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:29:29.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.951+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:29.963+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.963+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:29:29.982+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.380 seconds
[2025-07-18T10:30:00.393+0000] {processor.py:186} INFO - Started process (PID=1170) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:30:00.394+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:30:00.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:00.395+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:30:00.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:00.586+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:00.597+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:30:00.697+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:00.697+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:00.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:00.708+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:30:00.726+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.339 seconds
[2025-07-18T10:30:31.268+0000] {processor.py:186} INFO - Started process (PID=1301) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:30:31.269+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:30:31.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.272+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:30:31.477+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.477+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:31.486+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:30:31.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.577+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:31.588+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.588+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:30:31.607+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.346 seconds
[2025-07-18T10:31:01.848+0000] {processor.py:186} INFO - Started process (PID=1432) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:31:01.849+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:31:01.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:01.851+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:31:02.031+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.031+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:02.040+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:31:02.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.146+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:02.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.156+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:31:02.174+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.332 seconds
[2025-07-18T10:31:32.348+0000] {processor.py:186} INFO - Started process (PID=1563) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:31:32.349+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:31:32.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.350+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:31:32.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.525+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:32.534+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:31:32.617+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.616+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:32.626+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.626+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:31:32.643+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.301 seconds
[2025-07-18T10:32:03.371+0000] {processor.py:186} INFO - Started process (PID=1694) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:32:03.372+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:32:03.375+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.374+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:32:03.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.573+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:03.583+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:32:03.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.678+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:03.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.693+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:32:03.715+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.349 seconds
[2025-07-18T10:32:33.835+0000] {processor.py:186} INFO - Started process (PID=1825) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:32:33.836+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:32:33.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.839+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:32:34.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.017+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:34.025+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:32:34.115+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.115+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:34.125+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.125+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:32:34.143+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.316 seconds
[2025-07-18T10:33:04.267+0000] {processor.py:186} INFO - Started process (PID=1956) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:33:04.268+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:33:04.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.270+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:33:04.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.457+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:04.467+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:33:04.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.563+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:04.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.573+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:33:04.593+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.332 seconds
[2025-07-18T10:33:34.881+0000] {processor.py:186} INFO - Started process (PID=2087) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:33:34.882+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:33:34.885+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.884+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:33:35.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.064+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:35.073+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:33:35.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.165+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:35.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.174+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:33:35.190+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.314 seconds
[2025-07-18T10:34:05.508+0000] {processor.py:186} INFO - Started process (PID=2218) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:34:05.509+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:34:05.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.511+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:34:05.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.714+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:05.723+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:34:05.812+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.811+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:05.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.821+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:34:05.840+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.338 seconds
[2025-07-18T10:34:36.590+0000] {processor.py:186} INFO - Started process (PID=2349) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:34:36.591+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:34:36.593+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.593+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:34:36.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.776+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:36.785+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:34:36.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.879+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:36.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.889+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:34:36.909+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.324 seconds
[2025-07-18T10:35:07.037+0000] {processor.py:186} INFO - Started process (PID=2480) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:35:07.038+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:35:07.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.041+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:35:07.235+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.234+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:07.244+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:35:07.347+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.346+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:07.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.357+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:35:07.381+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.349 seconds
[2025-07-18T10:35:38.134+0000] {processor.py:186} INFO - Started process (PID=2611) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:35:38.135+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:35:38.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.136+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:35:38.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.329+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:38.338+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:35:38.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.440+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:38.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.452+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:35:38.476+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.348 seconds
[2025-07-18T10:36:08.883+0000] {processor.py:186} INFO - Started process (PID=2742) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:36:08.884+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:36:08.886+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.886+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:36:09.084+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.083+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:09.092+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:36:09.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.192+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:09.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.203+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:36:09.224+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.348 seconds
[2025-07-18T10:36:39.479+0000] {processor.py:186} INFO - Started process (PID=2873) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:36:39.480+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:36:39.482+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.482+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:36:39.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.682+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:39.692+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:36:39.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.811+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:39.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.825+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:36:39.845+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.372 seconds
[2025-07-18T10:37:09.988+0000] {processor.py:186} INFO - Started process (PID=3002) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:37:09.989+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:37:09.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:09.991+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:37:10.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.219+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:10.232+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:37:10.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.344+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:10.355+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.355+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:37:10.374+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.391 seconds
[2025-07-18T10:37:41.234+0000] {processor.py:186} INFO - Started process (PID=3135) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:37:41.236+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:37:41.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.238+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:37:41.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.427+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:41.436+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:37:41.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.526+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:41.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.536+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:37:41.554+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.325 seconds
[2025-07-18T10:38:11.720+0000] {processor.py:186} INFO - Started process (PID=3266) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:38:11.720+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:38:11.723+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:11.722+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:38:11.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:11.940+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:11.948+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:38:12.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.044+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:12.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.054+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:38:12.074+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.360 seconds
[2025-07-18T10:38:42.648+0000] {processor.py:186} INFO - Started process (PID=3397) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:38:42.649+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:38:42.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.651+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:38:42.831+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.831+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:42.839+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:38:42.938+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.938+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:42.949+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.949+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:38:42.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.325 seconds
[2025-07-18T10:39:13.351+0000] {processor.py:186} INFO - Started process (PID=3528) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:39:13.351+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:39:13.354+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.353+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:39:13.554+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.554+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:13.563+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:39:13.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.657+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:13.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.667+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:39:13.687+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.343 seconds
[2025-07-18T10:39:44.054+0000] {processor.py:186} INFO - Started process (PID=3659) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:39:44.056+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:39:44.059+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.059+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:39:44.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.303+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:44.312+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:39:44.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.409+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:44.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.421+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:39:44.443+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.395 seconds
[2025-07-18T10:40:14.755+0000] {processor.py:186} INFO - Started process (PID=3790) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:40:14.756+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:40:14.759+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.758+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:40:14.963+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.963+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:14.973+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:40:15.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:15.085+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:15.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:15.096+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:40:15.121+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.372 seconds
[2025-07-18T10:40:45.917+0000] {processor.py:186} INFO - Started process (PID=3921) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:40:45.918+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:40:45.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:45.919+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:40:46.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.102+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:46.111+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:40:46.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.200+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:46.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.212+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:40:46.234+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.323 seconds
[2025-07-18T10:41:16.650+0000] {processor.py:186} INFO - Started process (PID=4050) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:41:16.651+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:41:16.652+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:16.651+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:41:16.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:16.875+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:16.884+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:41:16.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:16.987+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:16.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:16.998+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:41:17.020+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.378 seconds
[2025-07-18T10:41:47.274+0000] {processor.py:186} INFO - Started process (PID=4186) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:41:47.275+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:41:47.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.277+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:41:47.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.471+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:47.480+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:41:47.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.579+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:47.590+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.590+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:41:47.611+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.343 seconds
[2025-07-18T10:42:17.795+0000] {processor.py:186} INFO - Started process (PID=4324) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:42:17.796+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:42:17.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:17.798+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:42:18.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.004+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:18.014+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:42:18.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.111+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:18.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.122+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:42:18.140+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.352 seconds
[2025-07-18T10:42:59.757+0000] {processor.py:186} INFO - Started process (PID=256) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:42:59.758+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:42:59.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.760+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:43:00.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.126+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:00.134+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:43:00.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.229+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:00.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.239+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:43:00.256+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.504 seconds
[2025-07-18T10:43:31.914+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:43:31.915+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:43:31.917+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:31.917+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:43:32.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.242+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:32.250+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:43:32.343+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.342+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:32.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.351+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:43:32.366+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.458 seconds
[2025-07-18T10:44:02.978+0000] {processor.py:186} INFO - Started process (PID=530) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:44:02.979+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:44:02.981+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:02.981+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:44:03.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:03.281+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:03.288+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:44:03.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:03.377+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:03.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:03.385+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:44:03.400+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.428 seconds
[2025-07-18T10:44:33.857+0000] {processor.py:186} INFO - Started process (PID=664) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:44:33.858+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:44:33.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:33.860+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:44:34.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.072+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:34.081+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:44:34.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.177+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:34.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.186+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:44:34.204+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.352 seconds
[2025-07-18T10:45:04.744+0000] {processor.py:186} INFO - Started process (PID=802) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:45:04.745+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:45:04.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:04.748+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:45:04.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:04.955+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:04.965+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:45:05.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.064+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:05.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.074+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:45:05.095+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.356 seconds
[2025-07-18T10:45:35.355+0000] {processor.py:186} INFO - Started process (PID=938) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:45:35.356+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:45:35.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:35.359+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:45:35.556+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:35.556+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:35.566+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:45:35.666+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:35.665+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:35.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:35.678+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:45:35.698+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.349 seconds
[2025-07-18T10:46:06.309+0000] {processor.py:186} INFO - Started process (PID=1074) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:46:06.310+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:46:06.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:06.311+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:46:06.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:06.546+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:06.556+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:46:06.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:06.683+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:06.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:06.695+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:46:06.717+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.414 seconds
[2025-07-18T10:46:36.861+0000] {processor.py:186} INFO - Started process (PID=1208) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:46:36.862+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:46:36.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:36.865+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:46:37.077+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.077+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:37.087+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:46:37.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.198+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:37.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.209+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:46:37.233+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.380 seconds
[2025-07-18T10:47:07.733+0000] {processor.py:186} INFO - Started process (PID=1408) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:47:07.734+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:47:07.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.736+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:47:07.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.976+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:07.988+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:47:08.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:08.162+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:08.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:08.179+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:47:08.214+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.489 seconds
[2025-07-18T10:48:05.181+0000] {processor.py:186} INFO - Started process (PID=256) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:48:05.182+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:48:05.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.184+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:48:05.514+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.514+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:05.520+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:48:05.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.620+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:05.630+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.630+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:48:05.647+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.472 seconds
[2025-07-18T10:48:36.319+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:48:36.320+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:48:36.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.322+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:48:36.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.663+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:36.671+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:48:36.776+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.775+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:36.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.784+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:48:36.804+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.492 seconds
[2025-07-18T10:49:07.142+0000] {processor.py:186} INFO - Started process (PID=528) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:49:07.143+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:49:07.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:07.145+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:49:07.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:07.530+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:07.538+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:49:07.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:07.657+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:07.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:07.669+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:49:07.692+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.555 seconds
[2025-07-18T10:49:38.347+0000] {processor.py:186} INFO - Started process (PID=664) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:49:38.348+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:49:38.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.351+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:49:38.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.566+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:38.575+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:49:38.681+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.681+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:38.692+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.691+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:49:38.710+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.370 seconds
[2025-07-18T10:50:09.054+0000] {processor.py:186} INFO - Started process (PID=800) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:50:09.055+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:50:09.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:09.057+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:50:09.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:09.253+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:09.262+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:50:09.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:09.360+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:09.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:09.369+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:50:09.390+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.342 seconds
[2025-07-18T10:50:39.790+0000] {processor.py:186} INFO - Started process (PID=936) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:50:39.791+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:50:39.793+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.793+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:50:39.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.994+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:40.001+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:50:40.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:40.109+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:40.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:40.120+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:50:40.141+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.357 seconds
[2025-07-18T10:51:10.508+0000] {processor.py:186} INFO - Started process (PID=1072) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:51:10.509+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:51:10.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.513+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:51:10.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.763+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:10.771+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:51:10.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.877+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:10.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.890+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:51:10.912+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.410 seconds
[2025-07-18T10:51:41.011+0000] {processor.py:186} INFO - Started process (PID=1209) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:51:41.012+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:51:41.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.014+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:51:41.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.215+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:41.224+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:51:41.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.323+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:41.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.334+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:51:41.354+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.349 seconds
[2025-07-18T10:52:11.422+0000] {processor.py:186} INFO - Started process (PID=1342) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:52:11.423+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:52:11.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:11.425+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:52:11.626+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:11.626+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:11.634+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:52:11.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:11.736+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:11.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:11.746+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:52:11.766+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.351 seconds
[2025-07-18T10:52:42.093+0000] {processor.py:186} INFO - Started process (PID=1478) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:52:42.094+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:52:42.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.096+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:52:42.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.302+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:42.311+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:52:42.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.410+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:42.420+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.420+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:52:42.443+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.355 seconds
[2025-07-18T10:53:12.633+0000] {processor.py:186} INFO - Started process (PID=1614) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:53:12.634+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:53:12.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.637+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:53:12.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.828+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:12.837+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:53:12.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:12.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.944+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:53:12.962+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.334 seconds
[2025-07-18T10:53:43.210+0000] {processor.py:186} INFO - Started process (PID=1748) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:53:43.211+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:53:43.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.214+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:53:43.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.396+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:43.403+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:53:43.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.498+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:43.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.509+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:53:43.528+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.323 seconds
[2025-07-18T10:54:14.605+0000] {processor.py:186} INFO - Started process (PID=1891) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:54:14.606+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:54:14.610+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.610+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:54:14.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.844+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:14.854+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:54:14.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.960+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:14.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.971+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:54:14.991+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.393 seconds
[2025-07-18T10:54:45.161+0000] {processor.py:186} INFO - Started process (PID=2027) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:54:45.162+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:54:45.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.164+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:54:45.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.362+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:45.370+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:54:45.465+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.465+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:45.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.475+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:54:45.493+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.338 seconds
[2025-07-18T10:55:15.635+0000] {processor.py:186} INFO - Started process (PID=2163) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:55:15.636+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:55:15.639+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.638+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:55:15.812+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.812+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:15.820+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:55:15.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.909+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:15.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.918+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:55:15.934+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.305 seconds
[2025-07-18T10:55:46.216+0000] {processor.py:186} INFO - Started process (PID=2297) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:55:46.217+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:55:46.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.219+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:55:46.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.441+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:46.450+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:55:46.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.564+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:46.576+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.576+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:55:46.602+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.393 seconds
[2025-07-18T10:57:29.822+0000] {processor.py:186} INFO - Started process (PID=256) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:57:29.823+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:57:29.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.825+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:57:30.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.142+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:30.151+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:57:30.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.238+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:30.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.249+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:57:30.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.451 seconds
[2025-07-18T10:58:01.290+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:58:01.291+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:58:01.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.293+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:58:01.639+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.638+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:01.645+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:58:01.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.731+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:01.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.739+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:58:01.754+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.469 seconds
[2025-07-18T10:58:32.851+0000] {processor.py:186} INFO - Started process (PID=530) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:58:32.852+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:58:32.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:32.854+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:58:33.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:33.207+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:33.213+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:58:33.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:33.296+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:33.305+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:33.305+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:58:33.323+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.477 seconds
[2025-07-18T10:59:03.751+0000] {processor.py:186} INFO - Started process (PID=671) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:59:03.752+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:59:03.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:03.754+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:59:03.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:03.943+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:03.952+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:59:04.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.044+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:04.056+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.055+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:59:04.076+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.331 seconds
[2025-07-18T10:59:34.257+0000] {processor.py:186} INFO - Started process (PID=807) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:59:34.258+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T10:59:34.260+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.260+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:59:34.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.449+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:34.458+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T10:59:34.556+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.556+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:34.566+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.566+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T10:59:34.583+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.331 seconds
[2025-07-18T11:00:04.747+0000] {processor.py:186} INFO - Started process (PID=941) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:00:04.748+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:00:04.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.750+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:00:04.945+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.944+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:04.955+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:00:05.069+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.068+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:05.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.079+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:00:05.094+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.353 seconds
[2025-07-18T11:00:35.793+0000] {processor.py:186} INFO - Started process (PID=1074) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:00:35.793+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:00:35.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:35.795+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:00:35.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:35.979+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:35.987+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:00:36.076+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.076+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:36.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.086+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:00:36.104+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.317 seconds
[2025-07-18T11:01:06.243+0000] {processor.py:186} INFO - Started process (PID=1210) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:01:06.244+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:01:06.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.247+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:01:06.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.457+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:06.467+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:01:06.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.579+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:06.593+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.593+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:01:06.614+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.379 seconds
[2025-07-18T11:01:37.229+0000] {processor.py:186} INFO - Started process (PID=1344) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:01:37.230+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:01:37.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.232+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:01:37.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.423+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:37.432+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:01:37.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.531+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:37.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.542+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:01:37.563+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.339 seconds
[2025-07-18T11:02:08.611+0000] {processor.py:186} INFO - Started process (PID=1480) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:02:08.612+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:02:08.614+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:08.614+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:02:08.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:08.813+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:08.823+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:02:08.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:08.920+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:08.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:08.931+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:02:08.952+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.346 seconds
[2025-07-18T11:02:39.246+0000] {processor.py:186} INFO - Started process (PID=1616) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:02:39.247+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:02:39.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:39.249+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:02:39.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:39.452+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:39.461+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:02:39.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:39.567+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:39.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:39.578+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:02:39.594+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.354 seconds
[2025-07-18T11:03:09.774+0000] {processor.py:186} INFO - Started process (PID=1752) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:03:09.775+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:03:09.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.776+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:03:09.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.977+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:09.985+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:03:10.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.091+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:10.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.103+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:03:10.124+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.356 seconds
[2025-07-18T11:03:40.845+0000] {processor.py:186} INFO - Started process (PID=1888) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:03:40.846+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:03:40.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:40.848+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:03:41.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.066+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:41.075+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:03:41.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.198+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:41.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.210+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:03:41.229+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.390 seconds
[2025-07-18T11:04:11.598+0000] {processor.py:186} INFO - Started process (PID=2024) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:04:11.599+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:04:11.602+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.601+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:04:11.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.848+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:11.857+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:04:11.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.981+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:11.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.994+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:04:12.022+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.431 seconds
[2025-07-18T11:04:42.293+0000] {processor.py:186} INFO - Started process (PID=2160) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:04:42.294+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:04:42.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:42.296+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:04:42.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:42.489+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:42.496+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:04:42.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:42.606+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:42.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:42.619+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:04:42.641+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.354 seconds
[2025-07-18T11:05:13.020+0000] {processor.py:186} INFO - Started process (PID=2296) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:05:13.021+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:05:13.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.023+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:05:13.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.245+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:13.254+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:05:13.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.383+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:13.395+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.395+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:05:13.416+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.404 seconds
[2025-07-18T11:06:52.505+0000] {processor.py:186} INFO - Started process (PID=268) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:06:52.506+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:06:52.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.508+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:06:52.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.878+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:52.884+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:06:52.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.984+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:52.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.995+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:06:53.014+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.518 seconds
[2025-07-18T11:07:23.866+0000] {processor.py:186} INFO - Started process (PID=409) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:07:23.868+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:07:23.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.870+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:07:24.199+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:24.199+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:24.206+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:07:24.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:24.293+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:24.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:24.302+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:07:24.319+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.458 seconds
[2025-07-18T11:07:55.238+0000] {processor.py:186} INFO - Started process (PID=550) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:07:55.239+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:07:55.241+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.241+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:07:55.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.446+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:55.454+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:07:55.550+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.549+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:55.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.560+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:07:55.580+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.349 seconds
[2025-07-18T11:08:26.179+0000] {processor.py:186} INFO - Started process (PID=691) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:08:26.181+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:08:26.183+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.183+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:08:26.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.392+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:26.399+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:08:26.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.497+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:26.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.507+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:08:26.528+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.355 seconds
[2025-07-18T11:08:56.656+0000] {processor.py:186} INFO - Started process (PID=834) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:08:56.657+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:08:56.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.659+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:08:56.868+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.868+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:56.876+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:08:56.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.975+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:56.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.985+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:08:57.004+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.355 seconds
[2025-07-18T11:09:28.052+0000] {processor.py:186} INFO - Started process (PID=975) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:09:28.053+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:09:28.055+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.055+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:09:28.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.252+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:28.262+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:09:28.355+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.355+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:28.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.366+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:09:28.386+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.340 seconds
[2025-07-18T11:09:58.574+0000] {processor.py:186} INFO - Started process (PID=1116) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:09:58.575+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:09:58.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.577+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:09:58.833+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.832+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:58.841+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:09:58.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.960+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:58.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.972+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:09:58.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.424 seconds
[2025-07-18T11:10:29.218+0000] {processor.py:186} INFO - Started process (PID=1257) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:10:29.218+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:10:29.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.220+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:10:29.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.423+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:29.432+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:10:29.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.529+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:29.539+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.539+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:10:29.558+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.346 seconds
[2025-07-18T11:11:00.006+0000] {processor.py:186} INFO - Started process (PID=1396) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:11:00.007+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:11:00.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.010+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:11:00.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.223+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:00.230+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:11:00.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.321+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:00.330+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.330+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:11:00.347+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.348 seconds
[2025-07-18T11:11:30.830+0000] {processor.py:186} INFO - Started process (PID=1539) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:11:30.831+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:11:30.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.833+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:11:31.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.017+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:31.025+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:11:31.115+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.115+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:31.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:11:31.140+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.317 seconds
[2025-07-18T11:12:01.614+0000] {processor.py:186} INFO - Started process (PID=1680) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:12:01.615+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:12:01.617+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.617+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:12:01.799+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.798+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:01.808+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:12:01.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.894+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:01.905+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.905+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:12:01.923+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.316 seconds
[2025-07-18T11:12:32.448+0000] {processor.py:186} INFO - Started process (PID=1821) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:12:32.449+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:12:32.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.451+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:12:32.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.643+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:32.651+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:12:32.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.760+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:32.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.772+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:12:32.791+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.349 seconds
[2025-07-18T11:13:04.260+0000] {processor.py:186} INFO - Started process (PID=1962) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:13:04.261+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:13:04.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.263+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:13:04.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.455+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:04.466+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:13:04.569+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.569+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:04.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.579+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:13:04.598+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.345 seconds
[2025-07-18T11:13:34.730+0000] {processor.py:186} INFO - Started process (PID=2103) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:13:34.731+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:13:34.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:34.733+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:13:34.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:34.925+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:34.933+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:13:35.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.025+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:35.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.036+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:13:35.057+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.332 seconds
[2025-07-18T11:14:05.742+0000] {processor.py:186} INFO - Started process (PID=2244) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:14:05.742+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:14:05.745+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.744+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:14:05.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.946+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:05.955+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:14:06.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:06.060+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:06.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:06.070+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:14:06.089+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.353 seconds
[2025-07-18T11:14:36.256+0000] {processor.py:186} INFO - Started process (PID=2385) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:14:36.257+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:14:36.260+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:36.260+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:14:36.588+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:36.588+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:36.599+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:14:36.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:36.712+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:36.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:36.722+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:14:36.743+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.495 seconds
[2025-07-18T11:15:07.019+0000] {processor.py:186} INFO - Started process (PID=2526) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:15:07.020+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:15:07.022+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:07.022+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:15:07.229+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:07.229+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:07.239+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:15:07.338+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:07.338+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:07.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:07.348+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:15:07.370+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.357 seconds
[2025-07-18T11:15:37.914+0000] {processor.py:186} INFO - Started process (PID=2665) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:15:37.915+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:15:37.917+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.917+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:15:38.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.197+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:38.207+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:15:38.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.346+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:38.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.357+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:15:38.374+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.467 seconds
[2025-07-18T11:16:08.546+0000] {processor.py:186} INFO - Started process (PID=2806) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:16:08.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:16:08.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.549+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:16:08.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.751+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:08.762+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:16:08.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.868+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:08.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.880+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:16:08.899+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.359 seconds
[2025-07-18T11:16:39.106+0000] {processor.py:186} INFO - Started process (PID=2947) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:16:39.107+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:16:39.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.109+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:16:39.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.301+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:39.311+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:16:39.425+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.425+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:39.436+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.436+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:16:39.456+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.356 seconds
[2025-07-18T11:17:10.574+0000] {processor.py:186} INFO - Started process (PID=3088) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:17:10.575+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:17:10.577+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.577+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:17:10.812+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.812+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:10.821+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:17:10.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.943+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:10.954+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.953+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:17:10.973+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.406 seconds
[2025-07-18T11:17:41.368+0000] {processor.py:186} INFO - Started process (PID=3229) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:17:41.369+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:17:41.371+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:41.371+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:17:41.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:41.580+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:41.591+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:17:41.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:41.693+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:41.704+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:41.704+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:17:41.726+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.366 seconds
[2025-07-18T11:18:12.088+0000] {processor.py:186} INFO - Started process (PID=3370) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:18:12.089+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:18:12.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.091+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:18:12.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.320+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:12.331+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:18:12.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.440+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:12.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.453+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:18:12.476+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.395 seconds
[2025-07-18T11:18:42.770+0000] {processor.py:186} INFO - Started process (PID=3511) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:18:42.771+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:18:42.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.773+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:18:42.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.978+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:42.987+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:18:43.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.089+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:43.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.102+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:18:43.124+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.360 seconds
[2025-07-18T11:19:13.842+0000] {processor.py:186} INFO - Started process (PID=3652) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:19:13.843+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:19:13.846+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:13.846+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:19:14.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:14.108+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:14.116+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:19:14.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:14.236+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:14.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:14.249+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:19:14.272+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.437 seconds
[2025-07-18T11:19:44.758+0000] {processor.py:186} INFO - Started process (PID=3798) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:19:44.759+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:19:44.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.762+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:19:44.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.970+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:44.980+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:19:45.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.085+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:45.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.099+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:19:45.120+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.369 seconds
[2025-07-18T11:20:15.578+0000] {processor.py:186} INFO - Started process (PID=3939) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:20:15.579+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:20:15.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.581+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:20:15.780+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.780+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:15.789+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:20:15.925+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.924+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:15.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.942+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:20:15.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.395 seconds
