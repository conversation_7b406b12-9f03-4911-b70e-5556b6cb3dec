[2025-07-18T10:16:49.713+0000] {processor.py:186} INFO - Started process (PID=205) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:16:49.714+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:16:49.717+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.717+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:16:49.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.771+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:49.779+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:16:49.802+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.095 seconds
[2025-07-18T10:17:20.012+0000] {processor.py:186} INFO - Started process (PID=344) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:17:20.013+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:17:20.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.016+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:17:20.049+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.046+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.050+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:17:20.068+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.061 seconds
[2025-07-18T10:17:50.410+0000] {processor.py:186} INFO - Started process (PID=473) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:17:50.411+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:17:50.413+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.413+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:17:50.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.444+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:50.448+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:17:50.470+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.065 seconds
[2025-07-18T10:18:21.087+0000] {processor.py:186} INFO - Started process (PID=604) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:18:21.087+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:18:21.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.088+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:18:21.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.116+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.120+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:18:21.135+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.053 seconds
[2025-07-18T10:18:52.054+0000] {processor.py:186} INFO - Started process (PID=737) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:18:52.056+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:18:52.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.057+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:18:52.093+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.090+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:52.094+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:18:52.109+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.060 seconds
[2025-07-18T10:19:22.995+0000] {processor.py:186} INFO - Started process (PID=868) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:19:22.996+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:19:22.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:22.997+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:19:23.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.034+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:23.039+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:19:23.056+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.066 seconds
[2025-07-18T10:19:53.967+0000] {processor.py:186} INFO - Started process (PID=999) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:19:53.968+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:19:53.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:53.969+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:19:54.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.001+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:54.006+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:19:54.022+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.060 seconds
[2025-07-18T10:20:24.993+0000] {processor.py:186} INFO - Started process (PID=1130) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:20:24.994+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:20:24.996+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:24.996+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:20:25.033+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.030+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:25.034+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:20:25.050+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.062 seconds
[2025-07-18T10:20:55.949+0000] {processor.py:186} INFO - Started process (PID=1261) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:20:55.950+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:20:55.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:55.951+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:20:55.989+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:55.985+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:55.990+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:20:56.005+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.062 seconds
[2025-07-18T10:21:26.921+0000] {processor.py:186} INFO - Started process (PID=1392) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:21:26.922+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:21:26.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:26.923+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:21:26.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:26.955+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:26.959+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:21:26.978+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.062 seconds
[2025-07-18T10:21:57.082+0000] {processor.py:186} INFO - Started process (PID=1523) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:21:57.083+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:21:57.084+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.084+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:21:57.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.114+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:57.118+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:21:57.133+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.056 seconds
[2025-07-18T10:22:28.124+0000] {processor.py:186} INFO - Started process (PID=1654) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:22:28.125+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:22:28.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.126+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:22:28.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.156+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:28.160+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:22:28.176+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.058 seconds
[2025-07-18T10:22:59.114+0000] {processor.py:186} INFO - Started process (PID=1785) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:22:59.115+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:22:59.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.116+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:22:59.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.147+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:59.152+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:22:59.168+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.060 seconds
[2025-07-18T10:23:30.064+0000] {processor.py:186} INFO - Started process (PID=1916) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:23:30.064+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:23:30.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.066+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:23:30.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.096+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:30.102+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:23:30.117+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.059 seconds
[2025-07-18T10:24:00.906+0000] {processor.py:186} INFO - Started process (PID=2047) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:24:00.907+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:24:00.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:00.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:24:00.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:00.941+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:00.945+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:24:00.963+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.063 seconds
[2025-07-18T10:24:31.137+0000] {processor.py:186} INFO - Started process (PID=2178) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:24:31.138+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:24:31.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.139+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:24:31.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.171+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.175+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:24:31.194+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.062 seconds
[2025-07-18T10:25:03.218+0000] {processor.py:186} INFO - Started process (PID=2309) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:25:03.219+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:25:03.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.221+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:25:03.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.393+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:03.401+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:25:03.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.487+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:03.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.496+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:25:03.514+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.302 seconds
[2025-07-18T10:26:20.527+0000] {processor.py:186} INFO - Started process (PID=213) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:26:20.528+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:26:20.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:20.530+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:26:20.868+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:20.868+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:20.875+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:26:20.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:20.962+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:20.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:20.971+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:26:20.990+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.467 seconds
[2025-07-18T10:26:51.753+0000] {processor.py:186} INFO - Started process (PID=342) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:26:51.757+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:26:51.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:51.767+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:26:53.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:53.046+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:53.088+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:26:53.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:53.321+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:53.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:53.348+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:26:53.374+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 1.637 seconds
[2025-07-18T10:27:23.519+0000] {processor.py:186} INFO - Started process (PID=475) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:27:23.521+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:27:23.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:23.523+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:27:23.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:23.881+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:23.887+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:27:23.989+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:23.988+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:24.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:24.001+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:27:24.021+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.510 seconds
[2025-07-18T10:27:54.203+0000] {processor.py:186} INFO - Started process (PID=606) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:27:54.204+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:27:54.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:54.206+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:27:54.404+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:54.404+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:54.415+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:27:54.518+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:54.518+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:54.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:54.529+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:27:54.547+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.349 seconds
[2025-07-18T10:28:24.843+0000] {processor.py:186} INFO - Started process (PID=737) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:28:24.844+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:28:24.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:24.846+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:28:25.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:25.042+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:25.053+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:28:25.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:25.174+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:25.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:25.184+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:28:25.203+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.367 seconds
[2025-07-18T10:28:55.287+0000] {processor.py:186} INFO - Started process (PID=868) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:28:55.289+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:28:55.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:55.291+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:28:55.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:55.498+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:55.507+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:28:55.603+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:55.602+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:55.614+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:55.614+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:28:55.634+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.352 seconds
[2025-07-18T10:29:25.895+0000] {processor.py:186} INFO - Started process (PID=999) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:29:25.896+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:29:25.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:25.898+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:29:26.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:26.110+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:26.122+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:29:26.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:26.252+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:26.266+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:26.265+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:29:26.292+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.403 seconds
[2025-07-18T10:29:56.418+0000] {processor.py:186} INFO - Started process (PID=1130) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:29:56.419+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:29:56.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:56.420+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:29:56.627+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:56.627+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:56.637+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:29:56.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:56.732+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:56.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:56.742+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:29:56.761+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.350 seconds
[2025-07-18T10:30:27.081+0000] {processor.py:186} INFO - Started process (PID=1261) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:30:27.082+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:30:27.084+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:27.083+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:30:27.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:27.278+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:27.287+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:30:27.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:27.384+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:27.395+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:27.395+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:30:27.414+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.339 seconds
[2025-07-18T10:30:57.615+0000] {processor.py:186} INFO - Started process (PID=1392) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:30:57.616+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:30:57.619+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:57.619+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:30:57.823+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:57.823+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:57.831+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:30:57.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:57.926+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:57.936+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:57.936+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:30:57.954+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.345 seconds
[2025-07-18T10:31:28.191+0000] {processor.py:186} INFO - Started process (PID=1523) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:31:28.192+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:31:28.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:28.194+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:31:28.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:28.368+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:28.377+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:31:28.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:28.460+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:28.470+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:28.469+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:31:28.487+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.301 seconds
[2025-07-18T10:31:58.787+0000] {processor.py:186} INFO - Started process (PID=1654) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:31:58.788+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:31:58.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:58.790+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:31:58.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:58.968+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:58.977+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:31:59.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:59.069+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:59.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:59.079+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:31:59.096+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.314 seconds
[2025-07-18T10:32:29.274+0000] {processor.py:186} INFO - Started process (PID=1785) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:32:29.275+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:32:29.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:29.277+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:32:29.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:29.458+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:29.467+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:32:29.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:29.548+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:29.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:29.558+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:32:29.573+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.305 seconds
[2025-07-18T10:33:00.026+0000] {processor.py:186} INFO - Started process (PID=1916) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:33:00.027+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:33:00.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:00.029+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:33:00.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:00.237+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:00.249+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:33:00.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:00.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:00.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:00.361+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:33:00.379+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.359 seconds
[2025-07-18T10:33:30.699+0000] {processor.py:186} INFO - Started process (PID=2047) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:33:30.700+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:33:30.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:30.702+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:33:30.903+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:30.903+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:30.913+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:33:31.002+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:31.002+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:31.013+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:31.013+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:33:31.030+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.337 seconds
[2025-07-18T10:34:01.268+0000] {processor.py:186} INFO - Started process (PID=2178) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:34:01.269+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:34:01.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:01.272+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:34:01.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:01.462+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:01.472+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:34:01.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:01.564+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:01.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:01.574+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:34:01.589+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.328 seconds
[2025-07-18T10:34:32.023+0000] {processor.py:186} INFO - Started process (PID=2309) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:34:32.024+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:34:32.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:32.026+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:34:32.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:32.239+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:32.251+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:34:32.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:32.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:32.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:32.358+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:34:32.372+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.355 seconds
[2025-07-18T10:35:02.851+0000] {processor.py:186} INFO - Started process (PID=2440) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:35:02.852+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:35:02.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:02.854+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:35:03.029+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:03.029+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:03.038+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:35:03.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:03.121+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:03.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:03.129+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:35:03.146+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.300 seconds
[2025-07-18T10:35:33.524+0000] {processor.py:186} INFO - Started process (PID=2571) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:35:33.525+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:35:33.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:33.528+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:35:33.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:33.739+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:33.749+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:35:33.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:33.862+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:33.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:33.876+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:35:33.903+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.386 seconds
[2025-07-18T10:36:04.257+0000] {processor.py:186} INFO - Started process (PID=2702) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:36:04.258+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:36:04.260+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:04.260+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:36:04.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:04.457+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:04.467+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:36:04.568+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:04.568+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:04.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:04.579+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:36:04.596+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.345 seconds
[2025-07-18T10:36:35.244+0000] {processor.py:186} INFO - Started process (PID=2833) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:36:35.245+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:36:35.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:35.247+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:36:35.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:35.437+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:35.446+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:36:35.542+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:35.542+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:35.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:35.551+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:36:35.569+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.330 seconds
[2025-07-18T10:37:06.076+0000] {processor.py:186} INFO - Started process (PID=2964) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:37:06.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:37:06.080+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:06.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:37:06.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:06.279+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:06.288+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:37:06.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:06.378+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:06.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:06.387+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:37:06.406+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.336 seconds
[2025-07-18T10:37:37.004+0000] {processor.py:186} INFO - Started process (PID=3095) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:37:37.005+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:37:37.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:37.007+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:37:37.200+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:37.199+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:37.208+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:37:37.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:37.292+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:37.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:37.302+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:37:37.321+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.323 seconds
[2025-07-18T10:38:07.830+0000] {processor.py:186} INFO - Started process (PID=3226) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:38:07.831+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:38:07.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:07.833+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:38:08.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:08.036+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:08.047+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:38:08.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:08.169+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:08.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:08.181+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:38:08.213+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.390 seconds
[2025-07-18T10:38:38.714+0000] {processor.py:186} INFO - Started process (PID=3357) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:38:38.715+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:38:38.718+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:38.718+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:38:38.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:38.920+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:38.928+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:38:39.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:39.025+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:39.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:39.036+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:38:39.055+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.348 seconds
[2025-07-18T10:39:09.396+0000] {processor.py:186} INFO - Started process (PID=3488) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:39:09.397+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:39:09.399+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:09.399+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:39:09.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:09.585+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:09.595+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:39:09.686+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:09.686+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:09.697+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:09.697+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:39:09.716+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.325 seconds
[2025-07-18T10:39:40.188+0000] {processor.py:186} INFO - Started process (PID=3619) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:39:40.189+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:39:40.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:40.192+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:39:40.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:40.400+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:40.409+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:39:40.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:40.507+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:40.518+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:40.518+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:39:40.538+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.357 seconds
[2025-07-18T10:40:10.929+0000] {processor.py:186} INFO - Started process (PID=3750) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:40:10.930+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:40:10.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:10.932+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:40:11.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:11.119+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:11.130+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:40:11.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:11.230+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:11.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:11.240+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:40:11.255+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.332 seconds
[2025-07-18T10:40:41.734+0000] {processor.py:186} INFO - Started process (PID=3881) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:40:41.735+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:40:41.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:41.738+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:40:41.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:41.909+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:41.918+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:40:42.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:42.001+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:42.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:42.010+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:40:42.026+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.298 seconds
[2025-07-18T10:41:12.615+0000] {processor.py:186} INFO - Started process (PID=4012) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:41:12.616+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:41:12.619+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:12.618+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:41:12.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:12.827+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:12.835+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:41:13.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:13.064+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:13.075+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:13.075+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:41:13.092+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.483 seconds
[2025-07-18T10:41:43.261+0000] {processor.py:186} INFO - Started process (PID=4148) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:41:43.262+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:41:43.264+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:43.263+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:41:43.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:43.472+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:43.482+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:41:43.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:43.728+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:43.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:43.739+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:41:43.759+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.505 seconds
[2025-07-18T10:42:14.289+0000] {processor.py:186} INFO - Started process (PID=4284) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:42:14.290+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:42:14.294+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:14.294+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:42:14.502+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:14.502+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:14.508+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:42:14.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:14.732+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:14.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:14.741+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:42:14.757+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.477 seconds
[2025-07-18T10:42:57.736+0000] {processor.py:186} INFO - Started process (PID=216) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:42:57.738+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:42:57.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:42:58.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.087+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:58.095+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:42:58.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.179+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:58.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.188+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:42:58.205+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.475 seconds
[2025-07-18T10:43:28.894+0000] {processor.py:186} INFO - Started process (PID=354) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:43:28.895+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:43:28.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:28.897+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:43:29.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:29.252+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:29.265+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:43:29.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:29.352+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:29.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:29.361+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:43:29.376+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.487 seconds
[2025-07-18T10:43:59.545+0000] {processor.py:186} INFO - Started process (PID=488) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:43:59.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:43:59.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.549+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:43:59.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.848+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:59.856+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:43:59.945+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.945+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:59.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.958+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:43:59.972+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.433 seconds
[2025-07-18T10:44:30.302+0000] {processor.py:186} INFO - Started process (PID=624) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:44:30.303+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:44:30.305+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.305+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:44:30.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.494+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:30.503+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:44:30.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.599+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:30.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.608+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:44:30.627+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.330 seconds
[2025-07-18T10:45:01.344+0000] {processor.py:186} INFO - Started process (PID=760) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:45:01.345+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:45:01.347+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.347+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:45:01.556+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.556+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:01.565+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:45:01.667+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.666+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:01.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.677+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:45:01.695+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.358 seconds
[2025-07-18T10:45:31.851+0000] {processor.py:186} INFO - Started process (PID=896) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:45:31.852+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:45:31.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:31.854+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:45:32.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:32.094+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:32.103+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:45:32.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:32.231+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:32.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:32.242+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:45:32.268+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.422 seconds
[2025-07-18T10:46:03.144+0000] {processor.py:186} INFO - Started process (PID=1034) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:46:03.145+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:46:03.147+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.146+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:46:03.402+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.402+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:03.411+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:46:03.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.581+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:03.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.592+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:46:03.612+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.474 seconds
[2025-07-18T10:46:34.026+0000] {processor.py:186} INFO - Started process (PID=1170) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:46:34.027+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:46:34.029+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:34.029+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:46:34.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:34.251+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:34.262+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:46:34.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:34.385+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:34.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:34.399+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:46:34.426+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.406 seconds
[2025-07-18T10:47:04.693+0000] {processor.py:186} INFO - Started process (PID=1306) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:47:04.694+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:47:04.697+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:04.696+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:47:04.888+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:04.888+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:04.896+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:47:04.993+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:04.992+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:05.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.002+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:47:05.029+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.342 seconds
[2025-07-18T10:48:03.103+0000] {processor.py:186} INFO - Started process (PID=216) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:48:03.104+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:48:03.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.106+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:48:03.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.463+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:03.471+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:48:03.560+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.560+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:03.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.570+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:48:03.594+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.497 seconds
[2025-07-18T10:48:34.212+0000] {processor.py:186} INFO - Started process (PID=352) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:48:34.213+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:48:34.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:34.215+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:48:34.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:34.562+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:34.570+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:48:34.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:34.668+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:34.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:34.679+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:48:34.697+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.491 seconds
[2025-07-18T10:49:04.998+0000] {processor.py:186} INFO - Started process (PID=488) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:49:04.999+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:49:05.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.003+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:49:05.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.326+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:05.333+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:49:05.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.426+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:05.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.436+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:49:05.457+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.465 seconds
[2025-07-18T10:49:36.426+0000] {processor.py:186} INFO - Started process (PID=626) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:49:36.427+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:49:36.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.429+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:49:36.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.621+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:36.630+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:49:36.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.727+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:36.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.737+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:49:36.757+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.337 seconds
[2025-07-18T10:50:06.958+0000] {processor.py:186} INFO - Started process (PID=762) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:50:06.959+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:50:06.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.962+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:50:07.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:07.162+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:07.172+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:50:07.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:07.275+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:07.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:07.287+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:50:07.309+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.356 seconds
[2025-07-18T10:50:37.378+0000] {processor.py:186} INFO - Started process (PID=896) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:50:37.379+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:50:37.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:37.381+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:50:37.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:37.581+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:37.590+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:50:37.692+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:37.691+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:37.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:37.702+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:50:37.724+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.351 seconds
[2025-07-18T10:51:08.109+0000] {processor.py:186} INFO - Started process (PID=1032) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:51:08.110+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:51:08.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.111+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:51:08.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.340+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:08.347+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:51:08.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.448+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:08.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.458+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:51:08.478+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.375 seconds
[2025-07-18T10:51:38.676+0000] {processor.py:186} INFO - Started process (PID=1169) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:51:38.677+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:51:38.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.679+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:51:38.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.892+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:38.901+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:51:39.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:39.006+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:39.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:39.018+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:51:39.041+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.371 seconds
[2025-07-18T10:52:09.132+0000] {processor.py:186} INFO - Started process (PID=1305) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:52:09.133+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:52:09.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.135+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:52:09.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.332+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:09.344+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:52:09.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.457+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:09.470+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.469+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:52:09.488+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.363 seconds
[2025-07-18T10:52:39.646+0000] {processor.py:186} INFO - Started process (PID=1441) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:52:39.647+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:52:39.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.651+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:52:39.881+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.881+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:39.890+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:52:40.023+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:40.022+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:40.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:40.036+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:52:40.058+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.419 seconds
[2025-07-18T10:53:10.262+0000] {processor.py:186} INFO - Started process (PID=1577) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:53:10.263+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:53:10.265+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.265+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:53:10.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.474+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:10.481+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:53:10.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.589+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:10.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.599+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:53:10.619+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.363 seconds
[2025-07-18T10:53:40.746+0000] {processor.py:186} INFO - Started process (PID=1713) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:53:40.747+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:53:40.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.749+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:53:40.925+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.925+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:40.932+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:53:41.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:41.030+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:41.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:41.041+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:53:41.061+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.321 seconds
[2025-07-18T10:54:11.557+0000] {processor.py:186} INFO - Started process (PID=1849) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:54:11.558+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:54:11.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.561+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:54:11.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.777+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:11.788+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:54:11.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.904+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:11.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.918+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:54:11.939+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.389 seconds
[2025-07-18T10:54:42.214+0000] {processor.py:186} INFO - Started process (PID=1985) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:54:42.215+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:54:42.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.217+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:54:42.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.416+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:42.426+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:54:42.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.524+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:42.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.538+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:54:42.560+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.352 seconds
[2025-07-18T10:55:12.827+0000] {processor.py:186} INFO - Started process (PID=2121) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:55:12.828+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:55:12.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.830+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:55:13.021+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.020+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:13.029+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:55:13.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.117+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:13.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.129+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:55:13.150+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.329 seconds
[2025-07-18T10:55:43.679+0000] {processor.py:186} INFO - Started process (PID=2257) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:55:43.680+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:55:43.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.682+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:55:43.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.874+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:43.883+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:55:43.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.970+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:43.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.980+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:55:43.998+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.326 seconds
[2025-07-18T10:57:27.866+0000] {processor.py:186} INFO - Started process (PID=216) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:57:27.867+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:57:27.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.869+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:57:28.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.192+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:28.199+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:57:28.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.290+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:28.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.299+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:57:28.319+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.458 seconds
[2025-07-18T10:57:59.131+0000] {processor.py:186} INFO - Started process (PID=354) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:57:59.132+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:57:59.134+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:59.134+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:57:59.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:59.468+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:59.475+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:57:59.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:59.570+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:59.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:59.580+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:57:59.599+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.474 seconds
[2025-07-18T10:58:29.690+0000] {processor.py:186} INFO - Started process (PID=490) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:58:29.691+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:58:29.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:29.693+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:58:30.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:30.009+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:30.016+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:58:30.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:30.107+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:30.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:30.116+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:58:30.135+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.450 seconds
[2025-07-18T10:59:00.523+0000] {processor.py:186} INFO - Started process (PID=626) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:59:00.525+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:59:00.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:00.527+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:59:00.704+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:00.703+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:00.712+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:59:00.799+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:00.799+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:00.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:00.809+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:59:00.829+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.313 seconds
[2025-07-18T10:59:31.401+0000] {processor.py:186} INFO - Started process (PID=762) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:59:31.402+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T10:59:31.404+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:31.403+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:59:31.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:31.578+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:31.585+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T10:59:31.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:31.669+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:31.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:31.678+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T10:59:31.695+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.299 seconds
[2025-07-18T11:00:02.027+0000] {processor.py:186} INFO - Started process (PID=898) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:00:02.028+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:00:02.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:02.030+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:00:02.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:02.234+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:02.244+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:00:02.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:02.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:02.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:02.359+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:00:02.380+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.359 seconds
[2025-07-18T11:00:32.723+0000] {processor.py:186} INFO - Started process (PID=1034) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:00:32.724+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:00:32.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:32.725+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:00:32.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:32.897+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:32.905+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:00:32.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:32.987+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:32.999+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:32.998+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:00:33.017+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.299 seconds
[2025-07-18T11:01:03.273+0000] {processor.py:186} INFO - Started process (PID=1170) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:01:03.274+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:01:03.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:03.277+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:01:03.517+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:03.517+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:03.527+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:01:03.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:03.624+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:03.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:03.633+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:01:03.652+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.388 seconds
[2025-07-18T11:01:33.749+0000] {processor.py:186} INFO - Started process (PID=1306) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:01:33.750+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:01:33.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.752+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:01:33.925+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.925+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:33.933+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:01:34.021+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:34.020+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:34.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:34.030+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:01:34.050+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.307 seconds
[2025-07-18T11:02:04.713+0000] {processor.py:186} INFO - Started process (PID=1440) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:02:04.715+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:02:04.718+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.718+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:02:04.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.917+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:04.925+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:02:05.022+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:05.022+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:05.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:05.031+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:02:05.048+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.341 seconds
[2025-07-18T11:02:35.964+0000] {processor.py:186} INFO - Started process (PID=1578) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:02:35.966+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:02:35.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.968+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:02:36.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:36.168+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:36.178+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:02:36.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:36.284+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:36.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:36.296+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:02:36.319+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.360 seconds
[2025-07-18T11:03:06.500+0000] {processor.py:186} INFO - Started process (PID=1714) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:03:06.501+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:03:06.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:06.503+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:03:06.711+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:06.710+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:06.719+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:03:06.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:06.819+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:06.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:06.829+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:03:06.852+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.360 seconds
[2025-07-18T11:03:37.142+0000] {processor.py:186} INFO - Started process (PID=1850) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:03:37.143+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:03:37.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:37.145+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:03:37.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:37.343+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:37.352+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:03:37.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:37.451+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:37.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:37.463+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:03:37.481+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.346 seconds
[2025-07-18T11:04:07.921+0000] {processor.py:186} INFO - Started process (PID=1984) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:04:07.922+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:04:07.925+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:07.925+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:04:08.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:08.130+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:08.140+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:04:08.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:08.242+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:08.255+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:08.255+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:04:08.278+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.364 seconds
[2025-07-18T11:04:38.549+0000] {processor.py:186} INFO - Started process (PID=2120) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:04:38.550+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:04:38.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.551+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:04:38.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.751+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:38.760+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:04:38.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.848+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:38.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.859+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:04:38.883+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.340 seconds
[2025-07-18T11:05:09.187+0000] {processor.py:186} INFO - Started process (PID=2256) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:05:09.189+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:05:09.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:09.191+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:05:09.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:09.418+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:09.431+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:05:09.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:09.527+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:09.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:09.537+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:05:09.556+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.377 seconds
[2025-07-18T11:05:40.214+0000] {processor.py:186} INFO - Started process (PID=2392) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:05:40.215+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:05:40.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:40.218+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:05:40.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:40.454+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:40.461+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:05:40.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:40.574+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:40.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:40.587+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:05:40.608+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.402 seconds
[2025-07-18T11:06:50.474+0000] {processor.py:186} INFO - Started process (PID=222) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:06:50.475+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:06:50.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.478+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:06:50.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.802+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:50.810+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:06:50.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.910+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:50.919+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.919+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:06:50.936+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.467 seconds
[2025-07-18T11:07:21.630+0000] {processor.py:186} INFO - Started process (PID=363) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:07:21.631+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:07:21.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:21.635+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:07:22.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.025+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:22.033+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:07:22.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.129+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:22.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.138+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:07:22.159+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.537 seconds
[2025-07-18T11:07:53.107+0000] {processor.py:186} INFO - Started process (PID=510) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:07:53.109+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:07:53.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.111+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:07:53.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.439+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:53.445+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:07:53.539+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.539+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:53.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.549+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:07:53.566+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.464 seconds
[2025-07-18T11:08:24.139+0000] {processor.py:186} INFO - Started process (PID=653) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:08:24.140+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:08:24.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.142+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:08:24.354+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.354+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:24.364+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:08:24.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.461+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:24.473+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.473+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:08:24.493+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.360 seconds
[2025-07-18T11:08:54.991+0000] {processor.py:186} INFO - Started process (PID=793) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:08:54.992+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:08:54.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.995+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:08:55.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.196+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:55.205+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:08:55.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.307+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:55.317+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.317+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:08:55.337+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.352 seconds
[2025-07-18T11:09:25.524+0000] {processor.py:186} INFO - Started process (PID=935) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:09:25.525+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:09:25.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.527+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:09:25.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.750+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:25.762+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:09:25.907+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.906+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:25.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.917+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:09:25.936+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.419 seconds
[2025-07-18T11:09:56.074+0000] {processor.py:186} INFO - Started process (PID=1076) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:09:56.075+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:09:56.077+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:56.077+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:09:56.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:56.268+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:56.279+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:09:56.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:56.373+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:56.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:56.386+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:09:56.404+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.336 seconds
[2025-07-18T11:10:26.972+0000] {processor.py:186} INFO - Started process (PID=1217) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:10:26.973+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:10:26.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.975+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:10:27.173+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:27.173+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:27.182+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:10:27.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:27.277+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:27.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:27.288+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:10:27.308+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.341 seconds
[2025-07-18T11:10:57.984+0000] {processor.py:186} INFO - Started process (PID=1358) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:10:57.985+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:10:57.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.986+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:10:58.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:58.163+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:58.172+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:10:58.267+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:58.266+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:58.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:58.276+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:10:58.294+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.316 seconds
[2025-07-18T11:11:28.532+0000] {processor.py:186} INFO - Started process (PID=1499) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:11:28.533+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:11:28.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.536+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:11:28.753+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.752+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:28.762+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:11:28.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.859+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:28.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.869+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:11:28.889+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.363 seconds
[2025-07-18T11:11:59.320+0000] {processor.py:186} INFO - Started process (PID=1638) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:11:59.321+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:11:59.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.323+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:11:59.520+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.520+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:59.529+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:11:59.630+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.630+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:59.639+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.639+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:11:59.654+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.340 seconds
[2025-07-18T11:12:30.233+0000] {processor.py:186} INFO - Started process (PID=1779) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:12:30.233+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:12:30.235+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.235+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:12:30.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.437+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:30.444+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:12:30.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.546+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:30.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.558+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:12:30.579+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.352 seconds
[2025-07-18T11:13:01.617+0000] {processor.py:186} INFO - Started process (PID=1920) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:13:01.618+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:13:01.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:01.621+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:13:01.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:01.848+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:01.860+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:13:02.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.002+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:02.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.025+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:13:02.054+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.444 seconds
[2025-07-18T11:13:32.456+0000] {processor.py:186} INFO - Started process (PID=2063) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:13:32.457+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:13:32.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:32.459+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:13:32.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:32.648+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:32.657+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:13:32.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:32.747+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:32.758+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:32.758+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:13:32.776+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.326 seconds
[2025-07-18T11:14:03.212+0000] {processor.py:186} INFO - Started process (PID=2202) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:14:03.213+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:14:03.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.215+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:14:03.420+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.420+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:03.431+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:14:03.540+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.540+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:03.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.552+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:14:03.573+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.366 seconds
[2025-07-18T11:14:33.810+0000] {processor.py:186} INFO - Started process (PID=2343) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:14:33.811+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:14:33.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:33.813+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:14:34.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.019+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:34.031+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:14:34.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.144+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:34.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.156+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:14:34.177+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.373 seconds
[2025-07-18T11:15:04.841+0000] {processor.py:186} INFO - Started process (PID=2484) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:15:04.842+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:15:04.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.844+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:15:05.056+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.056+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:05.068+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:15:05.190+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.189+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:05.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.201+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:15:05.224+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.390 seconds
[2025-07-18T11:15:35.702+0000] {processor.py:186} INFO - Started process (PID=2627) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:15:35.703+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:15:35.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:35.705+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:15:35.938+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:35.938+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:35.951+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:15:36.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.072+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:36.084+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.084+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:15:36.109+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.414 seconds
[2025-07-18T11:16:06.324+0000] {processor.py:186} INFO - Started process (PID=2768) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:16:06.325+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:16:06.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.327+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:16:06.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.521+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:06.529+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:16:06.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.643+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:06.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.658+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:16:06.682+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.365 seconds
[2025-07-18T11:16:37.113+0000] {processor.py:186} INFO - Started process (PID=2909) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:16:37.114+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:16:37.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.116+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:16:37.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.313+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:37.321+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:16:37.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:37.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.424+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:16:37.442+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.334 seconds
[2025-07-18T11:17:08.142+0000] {processor.py:186} INFO - Started process (PID=3050) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:17:08.143+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:17:08.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:08.146+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:17:08.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:08.390+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:08.402+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:17:08.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:08.537+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:08.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:08.552+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:17:08.582+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.447 seconds
[2025-07-18T11:17:39.140+0000] {processor.py:186} INFO - Started process (PID=3191) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:17:39.142+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:17:39.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.144+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:17:39.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.369+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:39.380+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:17:39.486+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.485+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:39.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.498+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:17:39.520+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.387 seconds
[2025-07-18T11:18:09.787+0000] {processor.py:186} INFO - Started process (PID=3332) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:18:09.788+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:18:09.791+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:09.791+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:18:10.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:10.006+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:10.017+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:18:10.132+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:10.132+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:10.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:10.144+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:18:10.165+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.384 seconds
[2025-07-18T11:18:40.736+0000] {processor.py:186} INFO - Started process (PID=3473) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:18:40.737+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:18:40.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:40.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:18:40.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:40.951+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:40.960+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:18:41.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.060+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:41.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.070+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:18:41.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.359 seconds
[2025-07-18T11:19:11.507+0000] {processor.py:186} INFO - Started process (PID=3614) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:19:11.508+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:19:11.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.510+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:19:11.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.713+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:11.722+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:19:11.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.828+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:11.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.841+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:19:11.863+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.362 seconds
[2025-07-18T11:19:42.036+0000] {processor.py:186} INFO - Started process (PID=3760) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:19:42.037+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:19:42.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:42.039+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:19:42.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:42.231+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:42.240+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:19:42.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:42.349+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:42.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:42.360+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:19:42.380+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.351 seconds
[2025-07-18T11:20:12.507+0000] {processor.py:186} INFO - Started process (PID=3901) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:20:12.508+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:20:12.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.510+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:20:12.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.713+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:12.724+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:20:12.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.821+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:12.832+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.832+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:20:12.852+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.351 seconds
