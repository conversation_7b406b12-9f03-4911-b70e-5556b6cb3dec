# Система отслеживания затрат Biome AI

Система для отслеживания затрат на использование OpenAI API, включая детальную информацию о входящих и исходящих токенах, с возможностью экспорта в CSV и Google Sheets.

## 🚀 Возможности

- ✅ Отслеживание входящих токенов (tokens_input) и исходящих токенов (tokens_output)
- ✅ Сохранение данных о затратах в Redis
- ✅ API endpoint для получения статистики с информацией о токенах
- ✅ Экспорт данных в CSV файлы
- ✅ Автоматическая загрузка в Google Sheets с использованием gspread
- ✅ Поддержка Docker контейнеров
- ✅ Агрегация данных по DAG с итоговыми суммами

## 📁 Структура файлов

```
src/
├── dags/
│   └── cost_tracking.py              # Основной модуль отслеживания затрат
├── fastapi/
│   └── main.py                       # API endpoints (обновлен /cost-stats)
├── scripts/
│   ├── export_cost_to_gsheets.py     # Экспорт в CSV и Google Sheets (формат как на скриншоте)
│   └── export_cost_data_to_csv.py    # Детальный экспорт в CSV
├── secrets/
│   └── service_account.json          # Учетные данные Google Service Account
├── add_test_cost_data.py             # Скрипт для добавления тестовых данных
└── test_cost_tracking_system.py      # Тестовый скрипт системы
```

## 🔧 Настройка

### 1. Установка зависимостей

```bash
pip install redis gspread google-auth google-oauth2-tool
```

### 2. Настройка Google Sheets

1. Создайте проект в Google Cloud Console
2. Включите Google Sheets API и Google Drive API
3. Создайте Service Account и скачайте JSON файл с ключами
4. Поместите файл в `src/secrets/service_account.json`
5. В Docker контейнере файл должен быть доступен по пути `/opt/airflow/src/secrets/service_account.json`

### 3. Настройка Redis

Убедитесь, что Redis сервер запущен и доступен по адресу, указанному в переменной окружения `REDIS_HOST` (по умолчанию `redis`).

## 📊 Использование

### 1. Сохранение данных о затратах

```python
from cost_tracking import save_cost_data

# Сохранение данных о затратах
save_cost_data(
    dag_id="chatgpt_message_pipeline",
    task_id="analyze_message_task",
    input_tokens=1500,
    output_tokens=800,
    input_cost=0.0225,
    output_cost=0.048,
    total_cost=0.0705,
    user_id=12345,
    model="gpt-4o-mini"
)
```

### 2. Использование CostTracker с Google Sheets

```python
from cost_tracking import CostTracker

tracker = CostTracker()

# Данные для сохранения в Google Sheets
cost_data = {
    "operation_type": "chatgpt_message_pipeline",
    "token_usage": {
        "tokens_input": 1500,
        "tokens_output": 800,
        "total_tokens": 2300,
        "cost_input": 0.0225,
        "cost_output": 0.048,
        "total_cost": 0.0705,
        "model": "gpt-4o-mini"
    },
    "additional_metadata": {"dag_id": "chatgpt_message_pipeline"}
}

# Сохранение в Google Sheets
tracker.save_cost_to_gsheets(
    cost_data, 
    creds_path="src/secrets/service_account.json"
)
```

### 3. API для получения статистики

```bash
# Получить статистику по затратам
curl http://localhost:9000/cost-stats
```

Ответ включает:
- `total_tokens_input` - общее количество входящих токенов
- `total_tokens_output` - общее количество исходящих токенов  
- `total_tokens` - общее количество токенов
- `operations_by_dag` - статистика по каждому DAG с токенами

### 4. Экспорт в CSV и Google Sheets

#### Экспорт с агрегацией по DAG (формат как на скриншоте):

```bash
# Экспорт за последние 30 дней в CSV и Google Sheets
python src/scripts/export_cost_to_gsheets.py --days 30

# Только CSV экспорт
python src/scripts/export_cost_to_gsheets.py --csv-only --output my_costs.csv

# Только Google Sheets
python src/scripts/export_cost_to_gsheets.py --gsheets-only --spreadsheet-id "your_sheet_id"
```

#### Детальный экспорт всех операций:

```bash
# Детальный экспорт в CSV
python src/scripts/export_cost_data_to_csv.py --days 7 --output detailed_costs.csv

# Только статистика без экспорта
python src/scripts/export_cost_data_to_csv.py --summary
```

## 🧪 Тестирование

```bash
# Запуск тестов системы
python src/test_cost_tracking_system.py

# Добавление тестовых данных
python src/add_test_cost_data.py
```

## 📋 Формат данных в Google Sheets

Таблица создается с колонками как на скриншоте:

| Название | Tokens (input) | Tokens (output) | Tokens (Всего) | Стоимость (input) USD | Стоимость (output) USD | Стоимость (Всего) USD |
|----------|----------------|-----------------|----------------|-----------------------|------------------------|----------------------|
| ГЕНЕРАЦИЯ РИСКОВ И РЕКОМЕНДАЦИЙ | 3843 | 2934 | 6777.00 | 0.005875 | 0.02934 | 0.038475 |
| СООБЩЕНИЕ ПО РИСКУ | 2355 | 2600 | 4955.00 | 0.005875 | 0.026 | 0.031875 |
| ... | ... | ... | ... | ... | ... | ... |
| **ИТОГО ЗА ПОЛЬЗОВАТЕЛЯ:** | **216028.00** | **17923.00** | **233951.00** | **0.54** | **0.18** | **0.72** |

## 🐳 Docker

В Docker контейнере:

1. Убедитесь, что `src/secrets/service_account.json` монтируется в контейнер
2. Переменная окружения `REDIS_HOST` должна указывать на Redis контейнер
3. Все скрипты доступны в `/opt/airflow/src/`

```dockerfile
# Пример монтирования secrets
COPY src/secrets/service_account.json /opt/airflow/src/secrets/service_account.json
```

## 🔍 Структура данных в Redis

Данные сохраняются с ключами вида: `cost_tracking:{dag_id}:{run_id}:{uuid}`

Структура данных:
```json
{
    "dag_id": "chatgpt_message_pipeline",
    "task_id": "analyze_message_task",
    "run_id": "manual__2025-01-18T10:30:00+00:00",
    "timestamp": "2025-01-18T10:30:00.123456",
    "tokens_input": 1500,
    "tokens_output": 800,
    "total_tokens": 2300,
    "cost_input": 0.0225,
    "cost_output": 0.048,
    "total_cost": 0.0705,
    "model": "gpt-4o-mini",
    "user_id": 12345
}
```

## 🚨 Устранение неполадок

1. **Ошибка подключения к Redis**: Проверьте переменную `REDIS_HOST`
2. **Ошибка Google Sheets**: Убедитесь, что `service_account.json` существует и содержит корректные данные
3. **Отсутствие данных**: Запустите `python src/add_test_cost_data.py` для добавления тестовых данных
4. **API не отвечает**: Убедитесь, что FastAPI сервер запущен на порту 9000

## 📈 Мониторинг

- Данные в Redis хранятся 30 дней (TTL)
- API endpoint `/cost-stats` показывает актуальную статистику
- Логи сохраняются через стандартный Python logger

## 🔄 Обновления

Система включает все необходимые компоненты для отслеживания tokens_input и экспорта в Google Sheets. Все изменения обратно совместимы с существующим кодом.
