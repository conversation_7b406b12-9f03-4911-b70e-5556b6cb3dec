[2025-07-18T10:16:49.399+0000] {processor.py:186} INFO - Started process (PID=182) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:16:49.400+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:16:49.402+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.402+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:16:49.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.454+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:49.458+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:16:49.476+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.082 seconds
[2025-07-18T10:17:19.708+0000] {processor.py:186} INFO - Started process (PID=313) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:17:19.710+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:17:19.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:19.713+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:17:19.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:19.749+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:19.754+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:17:19.774+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.073 seconds
[2025-07-18T10:17:50.102+0000] {processor.py:186} INFO - Started process (PID=450) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:17:50.103+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:17:50.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.105+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:17:50.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.156+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:50.161+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:17:50.179+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.096 seconds
[2025-07-18T10:18:20.908+0000] {processor.py:186} INFO - Started process (PID=581) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:18:20.910+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:18:20.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:20.911+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:18:20.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:20.949+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:20.953+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:18:20.969+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.067 seconds
[2025-07-18T10:18:51.788+0000] {processor.py:186} INFO - Started process (PID=712) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:18:51.790+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:18:51.791+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:51.791+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:18:51.833+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:51.830+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:51.834+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:18:51.853+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.072 seconds
[2025-07-18T10:19:22.722+0000] {processor.py:186} INFO - Started process (PID=843) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:19:22.723+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:19:22.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:22.724+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:19:22.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:22.762+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:22.767+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:19:22.785+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.069 seconds
[2025-07-18T10:19:53.675+0000] {processor.py:186} INFO - Started process (PID=974) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:19:53.676+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:19:53.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:53.677+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:19:53.710+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:53.707+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:53.711+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:19:53.727+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.057 seconds
[2025-07-18T10:20:24.708+0000] {processor.py:186} INFO - Started process (PID=1105) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:20:24.709+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:20:24.710+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:24.710+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:20:24.743+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:24.740+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:24.744+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:20:24.760+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.058 seconds
[2025-07-18T10:20:55.692+0000] {processor.py:186} INFO - Started process (PID=1236) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:20:55.693+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:20:55.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:55.694+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:20:55.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:55.729+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:55.733+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:20:55.748+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.061 seconds
[2025-07-18T10:21:26.663+0000] {processor.py:186} INFO - Started process (PID=1367) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:21:26.664+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:21:26.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:26.665+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:21:26.699+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:26.695+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:26.700+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:21:26.716+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.059 seconds
[2025-07-18T10:21:56.812+0000] {processor.py:186} INFO - Started process (PID=1498) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:21:56.813+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:21:56.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:56.814+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:21:56.846+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:56.843+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:56.847+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:21:56.864+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.058 seconds
[2025-07-18T10:22:27.867+0000] {processor.py:186} INFO - Started process (PID=1629) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:22:27.868+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:22:27.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:27.869+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:22:27.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:27.906+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:27.910+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:22:27.928+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.066 seconds
[2025-07-18T10:22:58.858+0000] {processor.py:186} INFO - Started process (PID=1760) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:22:58.859+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:22:58.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:58.861+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:22:58.896+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:58.892+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:58.897+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:22:58.913+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.062 seconds
[2025-07-18T10:23:29.792+0000] {processor.py:186} INFO - Started process (PID=1891) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:23:29.793+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:23:29.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:29.794+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:23:29.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:29.824+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:29.829+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:23:29.848+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.061 seconds
[2025-07-18T10:24:00.640+0000] {processor.py:186} INFO - Started process (PID=2022) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:24:00.641+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:24:00.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:00.642+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:24:00.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:00.673+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:00.677+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:24:00.692+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.057 seconds
[2025-07-18T10:24:30.869+0000] {processor.py:186} INFO - Started process (PID=2153) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:24:30.870+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:24:30.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:30.871+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:24:30.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:30.902+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_image_object_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_image_object_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:30.906+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:24:30.922+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.059 seconds
[2025-07-18T10:25:01.854+0000] {processor.py:186} INFO - Started process (PID=2284) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:25:01.855+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:25:01.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:01.856+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:25:02.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:02.245+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:02.259+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:25:02.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:02.452+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:02.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:02.463+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:25:02.491+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.643 seconds
[2025-07-18T10:26:18.309+0000] {processor.py:186} INFO - Started process (PID=182) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:26:18.310+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:26:18.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:18.313+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:26:19.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:19.140+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:19.149+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:26:19.339+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:19.339+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:19.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:19.349+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:26:19.371+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 1.071 seconds
[2025-07-18T10:26:49.433+0000] {processor.py:186} INFO - Started process (PID=319) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:26:49.434+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:26:49.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:49.437+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:26:49.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:49.765+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:49.772+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:26:49.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:49.870+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:49.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:49.880+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:26:49.901+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.473 seconds
[2025-07-18T10:27:20.370+0000] {processor.py:186} INFO - Started process (PID=450) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:27:20.372+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:27:20.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:20.374+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:27:20.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:20.760+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:20.769+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:27:20.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:20.883+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:20.896+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:20.895+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:27:20.919+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.555 seconds
[2025-07-18T10:27:51.366+0000] {processor.py:186} INFO - Started process (PID=581) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:27:51.367+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:27:51.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:51.369+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:27:51.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:51.588+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:51.599+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:27:51.702+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:51.702+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:51.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:51.715+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:27:51.738+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.379 seconds
[2025-07-18T10:28:21.993+0000] {processor.py:186} INFO - Started process (PID=712) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:28:21.994+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:28:21.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:21.996+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:28:22.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:22.206+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:22.218+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:28:22.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:22.326+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:22.339+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:22.339+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:28:22.360+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.377 seconds
[2025-07-18T10:28:52.456+0000] {processor.py:186} INFO - Started process (PID=843) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:28:52.457+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:28:52.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:52.459+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:28:52.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:52.663+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:52.674+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:28:52.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:52.778+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:52.791+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:52.791+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:28:52.811+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.361 seconds
[2025-07-18T10:29:23.018+0000] {processor.py:186} INFO - Started process (PID=974) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:29:23.019+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:29:23.027+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:23.027+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:29:23.265+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:23.265+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:23.276+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:29:23.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:23.396+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:23.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:23.407+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:29:23.427+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.417 seconds
[2025-07-18T10:29:53.521+0000] {processor.py:186} INFO - Started process (PID=1105) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:29:53.523+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:29:53.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:53.526+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:29:53.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:53.800+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:53.808+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:29:53.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:53.916+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:53.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:53.928+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:29:53.949+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.436 seconds
[2025-07-18T10:30:24.274+0000] {processor.py:186} INFO - Started process (PID=1236) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:30:24.275+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:30:24.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:24.277+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:30:24.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:24.471+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:24.480+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:30:24.584+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:24.583+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:24.594+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:24.594+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:30:24.616+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.347 seconds
[2025-07-18T10:30:54.728+0000] {processor.py:186} INFO - Started process (PID=1367) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:30:54.729+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:30:54.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:54.731+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:30:54.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:54.970+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:54.981+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:30:55.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:55.089+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:55.101+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:55.101+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:30:55.122+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.402 seconds
[2025-07-18T10:31:25.758+0000] {processor.py:186} INFO - Started process (PID=1498) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:31:25.759+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:31:25.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:25.761+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:31:25.950+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:25.950+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:25.960+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:31:26.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:26.053+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:26.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:26.064+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:31:26.089+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.339 seconds
[2025-07-18T10:31:56.227+0000] {processor.py:186} INFO - Started process (PID=1629) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:31:56.228+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:31:56.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:56.230+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:31:56.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:56.495+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:56.506+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:31:56.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:56.636+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:56.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:56.648+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:31:56.668+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.448 seconds
[2025-07-18T10:32:26.844+0000] {processor.py:186} INFO - Started process (PID=1760) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:32:26.845+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:32:26.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:26.847+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:32:27.031+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:27.031+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:27.041+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:32:27.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:27.138+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:27.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:27.149+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:32:27.168+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.330 seconds
[2025-07-18T10:32:57.255+0000] {processor.py:186} INFO - Started process (PID=1891) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:32:57.256+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:32:57.259+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:57.258+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:32:57.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:57.457+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:57.468+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:32:57.566+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:57.565+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:57.576+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:57.576+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:32:57.597+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.349 seconds
[2025-07-18T10:33:27.846+0000] {processor.py:186} INFO - Started process (PID=2022) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:33:27.847+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:33:27.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:27.849+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:33:28.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:28.065+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:28.077+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:33:28.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:28.187+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:28.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:28.198+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:33:28.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.382 seconds
[2025-07-18T10:33:58.455+0000] {processor.py:186} INFO - Started process (PID=2153) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:33:58.456+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:33:58.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:58.458+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:33:58.647+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:58.647+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:58.657+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:33:58.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:58.756+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:58.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:58.768+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:33:58.788+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.339 seconds
[2025-07-18T10:34:29.131+0000] {processor.py:186} INFO - Started process (PID=2284) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:34:29.132+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:34:29.136+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:29.136+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:34:29.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:29.394+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:29.404+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:34:29.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:29.530+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:29.542+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:29.542+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:34:29.563+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.443 seconds
[2025-07-18T10:35:00.284+0000] {processor.py:186} INFO - Started process (PID=2415) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:35:00.285+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:35:00.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:00.287+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:35:00.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:00.501+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:00.509+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:35:00.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:00.610+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:00.622+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:00.622+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:35:00.644+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.367 seconds
[2025-07-18T10:35:30.874+0000] {processor.py:186} INFO - Started process (PID=2546) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:35:30.875+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:35:30.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:30.877+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:35:31.080+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:31.080+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:31.090+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:35:31.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:31.187+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:31.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:31.200+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:35:31.221+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.352 seconds
[2025-07-18T10:36:01.688+0000] {processor.py:186} INFO - Started process (PID=2677) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:36:01.689+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:36:01.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:01.691+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:36:01.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:01.878+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:01.888+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:36:01.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:01.985+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:01.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:01.996+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:36:02.018+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.336 seconds
[2025-07-18T10:36:32.726+0000] {processor.py:186} INFO - Started process (PID=2808) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:36:32.727+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:36:32.729+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:32.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:36:32.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:32.914+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:32.926+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:36:33.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:33.024+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:33.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:33.035+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:36:33.056+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.336 seconds
[2025-07-18T10:37:03.453+0000] {processor.py:186} INFO - Started process (PID=2939) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:37:03.454+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:37:03.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:03.456+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:37:03.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:03.644+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:03.653+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:37:03.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:03.748+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:03.759+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:03.759+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:37:03.781+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.333 seconds
[2025-07-18T10:37:34.108+0000] {processor.py:186} INFO - Started process (PID=3070) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:37:34.109+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:37:34.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:34.112+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:37:34.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:34.323+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:34.332+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:37:34.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:34.443+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:34.460+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:34.460+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:37:34.483+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.382 seconds
[2025-07-18T10:38:05.125+0000] {processor.py:186} INFO - Started process (PID=3201) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:38:05.126+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:38:05.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:05.128+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:38:05.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:05.346+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:05.356+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:38:05.465+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:05.464+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:05.477+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:05.476+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:38:05.498+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.380 seconds
[2025-07-18T10:38:35.850+0000] {processor.py:186} INFO - Started process (PID=3332) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:38:35.851+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:38:35.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:35.853+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:38:36.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:36.082+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:36.093+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:38:36.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:36.201+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:36.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:36.213+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:38:36.232+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.388 seconds
[2025-07-18T10:39:06.631+0000] {processor.py:186} INFO - Started process (PID=3463) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:39:06.632+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:39:06.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:06.634+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:39:06.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:06.821+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:06.833+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:39:06.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:06.933+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:06.945+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:06.945+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:39:06.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.341 seconds
[2025-07-18T10:39:37.166+0000] {processor.py:186} INFO - Started process (PID=3594) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:39:37.167+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:39:37.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:37.169+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:39:37.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:37.352+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:37.362+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:39:37.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:37.470+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:37.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:37.484+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:39:37.674+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.514 seconds
[2025-07-18T10:40:07.769+0000] {processor.py:186} INFO - Started process (PID=3725) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:40:07.770+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:40:07.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:07.772+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:40:07.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:07.969+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:07.977+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:40:08.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:08.071+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:08.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:08.240+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:40:08.259+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.495 seconds
[2025-07-18T10:40:38.668+0000] {processor.py:186} INFO - Started process (PID=3856) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:40:38.669+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:40:38.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:38.671+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:40:38.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:38.853+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:38.863+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:40:38.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:38.960+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:39.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:39.112+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:40:39.131+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.468 seconds
[2025-07-18T10:41:09.523+0000] {processor.py:186} INFO - Started process (PID=3987) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:41:09.524+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:41:09.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:09.526+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:41:09.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:09.705+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:09.717+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:41:09.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:09.816+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:09.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:09.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:41:09.988+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.470 seconds
[2025-07-18T10:41:40.304+0000] {processor.py:186} INFO - Started process (PID=4123) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:41:40.305+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:41:40.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:40.308+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:41:40.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:40.527+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:40.541+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:41:40.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:40.853+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:40.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:40.864+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:41:40.886+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.587 seconds
[2025-07-18T10:42:11.066+0000] {processor.py:186} INFO - Started process (PID=4259) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:42:11.067+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:42:11.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:11.069+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:42:11.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:11.248+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:11.257+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:42:11.534+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:11.533+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:11.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:11.543+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:42:11.562+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.501 seconds
[2025-07-18T10:42:56.128+0000] {processor.py:186} INFO - Started process (PID=185) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:42:56.129+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:42:56.131+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:56.131+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:42:56.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:56.578+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:56.588+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:42:56.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:56.771+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:56.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:56.781+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:42:56.802+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.680 seconds
[2025-07-18T10:43:26.893+0000] {processor.py:186} INFO - Started process (PID=321) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:43:26.894+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:43:26.896+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:26.896+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:43:27.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:27.207+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:27.215+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:43:27.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:27.297+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:27.305+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:27.305+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:43:27.323+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.437 seconds
[2025-07-18T10:43:57.459+0000] {processor.py:186} INFO - Started process (PID=457) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:43:57.460+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:43:57.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:57.463+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:43:57.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:57.816+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:57.822+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:43:57.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:57.904+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:57.913+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:57.913+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:43:57.932+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.479 seconds
[2025-07-18T10:44:28.492+0000] {processor.py:186} INFO - Started process (PID=599) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:44:28.493+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:44:28.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:28.496+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:44:28.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:28.707+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:28.718+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:44:28.823+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:28.823+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:28.833+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:28.833+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:44:28.852+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.367 seconds
[2025-07-18T10:44:59.485+0000] {processor.py:186} INFO - Started process (PID=735) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:44:59.486+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:44:59.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:59.488+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:44:59.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:59.700+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:59.712+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:44:59.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:59.815+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:59.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:59.827+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:44:59.847+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.369 seconds
[2025-07-18T10:45:30.017+0000] {processor.py:186} INFO - Started process (PID=873) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:45:30.018+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:45:30.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:30.019+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:45:30.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:30.210+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:30.219+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:45:30.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:30.328+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:30.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:30.342+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:45:30.362+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.351 seconds
[2025-07-18T10:46:01.296+0000] {processor.py:186} INFO - Started process (PID=1009) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:46:01.296+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:46:01.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:01.298+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:46:01.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:01.531+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:01.540+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:46:01.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:01.669+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:01.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:01.684+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:46:01.704+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.415 seconds
[2025-07-18T10:46:31.799+0000] {processor.py:186} INFO - Started process (PID=1145) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:46:31.801+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:46:31.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:31.803+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:46:32.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:32.036+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:32.048+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:46:32.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:32.179+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:32.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:32.190+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:46:32.214+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.420 seconds
[2025-07-18T10:47:02.443+0000] {processor.py:186} INFO - Started process (PID=1281) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:47:02.444+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:47:02.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:02.446+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:47:02.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:02.677+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:02.689+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:47:02.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:02.801+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:02.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:02.814+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:47:02.837+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.400 seconds
[2025-07-18T10:48:01.445+0000] {processor.py:186} INFO - Started process (PID=185) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:48:01.446+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:48:01.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:01.448+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:48:01.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:01.956+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:01.970+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:48:02.186+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:02.186+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:02.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:02.198+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:48:02.218+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.778 seconds
[2025-07-18T10:48:32.295+0000] {processor.py:186} INFO - Started process (PID=327) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:48:32.296+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:48:32.298+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:32.298+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:48:32.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:32.649+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:32.654+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:48:32.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:32.744+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:32.753+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:32.753+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:48:32.775+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.485 seconds
[2025-07-18T10:49:03.503+0000] {processor.py:186} INFO - Started process (PID=463) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:49:03.504+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:49:03.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:03.506+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:49:03.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:03.872+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:03.879+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:49:03.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:03.962+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:03.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:03.971+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:49:03.993+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.497 seconds
[2025-07-18T10:49:34.081+0000] {processor.py:186} INFO - Started process (PID=599) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:49:34.083+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:49:34.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:34.085+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:49:34.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:34.290+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:34.300+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:49:34.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:34.389+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:34.399+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:34.399+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:49:34.415+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.344 seconds
[2025-07-18T10:50:04.569+0000] {processor.py:186} INFO - Started process (PID=735) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:50:04.570+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:50:04.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:04.572+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:50:04.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:04.771+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:04.781+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:50:04.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:04.874+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:04.885+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:04.885+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:50:04.903+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.341 seconds
[2025-07-18T10:50:35.284+0000] {processor.py:186} INFO - Started process (PID=871) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:50:35.285+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:50:35.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:35.287+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:50:35.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:35.491+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:35.501+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:50:35.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:35.595+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:35.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:35.607+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:50:35.626+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.347 seconds
[2025-07-18T10:51:06.063+0000] {processor.py:186} INFO - Started process (PID=1007) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:51:06.064+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:51:06.067+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:06.066+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:51:06.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:06.262+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:06.272+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:51:06.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:06.369+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:06.382+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:06.382+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:51:06.404+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.347 seconds
[2025-07-18T10:51:36.542+0000] {processor.py:186} INFO - Started process (PID=1144) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:51:36.543+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:51:36.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:36.545+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:51:36.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:36.752+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:36.761+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:51:36.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:36.853+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:36.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:36.862+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:51:36.881+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.345 seconds
[2025-07-18T10:52:06.951+0000] {processor.py:186} INFO - Started process (PID=1280) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:52:06.952+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:52:06.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:06.954+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:52:07.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:07.179+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:07.194+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:52:07.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:07.327+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:07.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:07.339+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:52:07.362+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.416 seconds
[2025-07-18T10:52:37.435+0000] {processor.py:186} INFO - Started process (PID=1416) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:52:37.436+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:52:37.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:37.438+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:52:37.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:37.634+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:37.643+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:52:37.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:37.732+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:37.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:37.743+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:52:37.761+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.332 seconds
[2025-07-18T10:53:08.161+0000] {processor.py:186} INFO - Started process (PID=1552) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:53:08.162+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:53:08.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:08.164+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:53:08.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:08.365+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:08.375+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:53:08.486+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:08.486+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:08.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:08.496+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:53:08.516+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.361 seconds
[2025-07-18T10:53:38.728+0000] {processor.py:186} INFO - Started process (PID=1688) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:53:38.729+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:53:38.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:38.731+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:53:38.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:38.908+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:38.917+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:53:39.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:39.007+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:39.018+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:39.018+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:53:39.035+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.313 seconds
[2025-07-18T10:54:09.385+0000] {processor.py:186} INFO - Started process (PID=1824) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:54:09.386+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:54:09.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:09.388+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:54:09.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:09.579+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:09.587+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:54:09.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:09.679+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:09.690+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:09.689+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:54:09.708+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.327 seconds
[2025-07-18T10:54:40.010+0000] {processor.py:186} INFO - Started process (PID=1962) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:54:40.011+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:54:40.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:40.014+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:54:40.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:40.202+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:40.211+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:54:40.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:40.308+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:40.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:40.319+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:54:40.339+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.335 seconds
[2025-07-18T10:55:10.790+0000] {processor.py:186} INFO - Started process (PID=2096) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:55:10.791+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:55:10.793+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:10.792+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:55:10.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:10.966+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:10.975+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:55:11.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:11.063+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:11.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:11.072+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:55:11.090+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.306 seconds
[2025-07-18T10:55:41.641+0000] {processor.py:186} INFO - Started process (PID=2232) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:55:41.642+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:55:41.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:41.645+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:55:41.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:41.852+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:41.860+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:55:41.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:41.959+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:41.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:41.971+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:55:41.991+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.357 seconds
[2025-07-18T10:57:26.242+0000] {processor.py:186} INFO - Started process (PID=185) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:57:26.243+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:57:26.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:26.245+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:57:26.723+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:26.722+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:26.734+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:57:26.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:26.924+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:26.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:26.934+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:57:26.952+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.716 seconds
[2025-07-18T10:57:57.048+0000] {processor.py:186} INFO - Started process (PID=321) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:57:57.049+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:57:57.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:57.052+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:57:57.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:57.400+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:57.408+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:57:57.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:57.499+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:57.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:57.508+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:57:57.523+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.482 seconds
[2025-07-18T10:58:27.765+0000] {processor.py:186} INFO - Started process (PID=465) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:58:27.766+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:58:27.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:27.768+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:58:28.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:28.050+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:28.059+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:58:28.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:28.140+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:28.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:28.150+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:58:28.166+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.407 seconds
[2025-07-18T10:58:58.381+0000] {processor.py:186} INFO - Started process (PID=601) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:58:58.382+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:58:58.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:58.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:58:58.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:58.561+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:58.569+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:58:58.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:58.675+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:58.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:58.687+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:58:58.709+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.333 seconds
[2025-07-18T10:59:29.604+0000] {processor.py:186} INFO - Started process (PID=737) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:59:29.604+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T10:59:29.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:29.606+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:59:29.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:29.800+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:29.812+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T10:59:29.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:29.910+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:29.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:29.922+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T10:59:29.942+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.344 seconds
[2025-07-18T11:00:00.141+0000] {processor.py:186} INFO - Started process (PID=873) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:00:00.142+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:00:00.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:00.144+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:00:00.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:00.360+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:00.369+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:00:00.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:00.479+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:00.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:00.492+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:00:00.513+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.378 seconds
[2025-07-18T11:00:31.313+0000] {processor.py:186} INFO - Started process (PID=1009) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:00:31.314+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:00:31.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:31.315+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:00:31.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:31.508+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:31.517+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:00:31.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:31.612+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:31.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:31.624+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:00:31.643+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.336 seconds
[2025-07-18T11:01:02.002+0000] {processor.py:186} INFO - Started process (PID=1145) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:01:02.002+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:01:02.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.004+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:01:02.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.207+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:02.219+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:01:02.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.328+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:02.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.341+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:01:02.362+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.366 seconds
[2025-07-18T11:01:32.663+0000] {processor.py:186} INFO - Started process (PID=1281) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:01:32.664+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:01:32.666+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:32.665+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:01:32.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:32.847+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:32.857+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:01:32.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:32.957+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:32.967+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:32.967+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:01:32.985+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.328 seconds
[2025-07-18T11:02:03.878+0000] {processor.py:186} INFO - Started process (PID=1417) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:02:03.879+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:02:03.881+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:03.881+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:02:04.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.104+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:04.115+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:02:04.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.221+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:04.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.232+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:02:04.253+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.381 seconds
[2025-07-18T11:02:34.766+0000] {processor.py:186} INFO - Started process (PID=1553) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:02:34.766+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:02:34.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:34.768+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:02:34.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:34.978+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:34.988+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:02:35.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.099+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:35.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.112+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:02:35.136+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.376 seconds
[2025-07-18T11:03:05.251+0000] {processor.py:186} INFO - Started process (PID=1689) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:03:05.252+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:03:05.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:05.254+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:03:05.482+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:05.482+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:05.492+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:03:05.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:05.595+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:05.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:05.607+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:03:05.628+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.384 seconds
[2025-07-18T11:03:35.921+0000] {processor.py:186} INFO - Started process (PID=1825) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:03:35.922+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:03:35.925+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:35.924+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:03:36.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.141+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:36.153+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:03:36.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.262+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:36.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.275+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:03:36.297+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.383 seconds
[2025-07-18T11:04:06.610+0000] {processor.py:186} INFO - Started process (PID=1961) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:04:06.612+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:04:06.614+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:06.614+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:04:06.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:06.842+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:06.857+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:04:06.981+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:06.981+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:06.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:06.994+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:04:07.018+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.414 seconds
[2025-07-18T11:04:37.258+0000] {processor.py:186} INFO - Started process (PID=2097) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:04:37.259+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:04:37.261+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:37.261+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:04:37.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:37.508+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:37.518+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:04:37.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:37.649+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:37.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:37.663+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:04:37.685+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.434 seconds
[2025-07-18T11:05:07.752+0000] {processor.py:186} INFO - Started process (PID=2233) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:05:07.753+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:05:07.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:07.755+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:05:07.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:07.984+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:07.996+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:05:08.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:08.128+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:08.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:08.142+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:05:08.165+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.419 seconds
[2025-07-18T11:05:38.343+0000] {processor.py:186} INFO - Started process (PID=2369) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:05:38.344+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:05:38.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:38.346+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:05:38.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:38.566+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:38.576+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:05:38.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:38.681+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:38.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:38.694+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:05:38.715+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.379 seconds
[2025-07-18T11:06:48.961+0000] {processor.py:186} INFO - Started process (PID=199) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:06:48.962+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:06:48.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:48.964+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:06:49.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:49.292+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:49.302+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:06:49.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:49.484+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:49.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:49.493+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:06:49.511+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.555 seconds
[2025-07-18T11:07:19.997+0000] {processor.py:186} INFO - Started process (PID=338) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:07:19.998+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:07:20.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:20.001+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:07:20.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:20.419+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:20.426+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:07:20.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:20.522+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:20.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:20.531+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:07:20.552+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.562 seconds
[2025-07-18T11:07:51.560+0000] {processor.py:186} INFO - Started process (PID=481) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:07:51.561+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:07:51.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:51.567+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:07:51.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:51.960+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:51.968+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:07:52.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:52.095+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:52.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:52.110+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:07:52.133+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.582 seconds
[2025-07-18T11:08:22.877+0000] {processor.py:186} INFO - Started process (PID=628) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:08:22.878+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:08:22.881+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:22.880+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:08:23.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.100+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:23.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:08:23.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.219+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:23.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.231+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:08:23.251+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.381 seconds
[2025-07-18T11:08:53.785+0000] {processor.py:186} INFO - Started process (PID=769) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:08:53.786+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:08:53.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:53.787+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:08:53.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:53.998+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:54.008+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:08:54.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.110+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:54.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.121+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:08:54.142+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.363 seconds
[2025-07-18T11:09:24.286+0000] {processor.py:186} INFO - Started process (PID=910) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:09:24.287+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:09:24.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:24.289+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:09:24.521+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:24.521+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:24.533+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:09:24.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:24.642+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:24.656+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:24.656+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:09:24.679+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.401 seconds
[2025-07-18T11:09:54.908+0000] {processor.py:186} INFO - Started process (PID=1051) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:09:54.909+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:09:54.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:54.912+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:09:55.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.127+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:55.139+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:09:55.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.245+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:55.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.256+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:09:55.276+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.374 seconds
[2025-07-18T11:10:25.876+0000] {processor.py:186} INFO - Started process (PID=1192) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:10:25.877+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:10:25.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:25.879+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:10:26.084+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.084+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:26.094+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:10:26.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.197+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:26.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.210+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:10:26.230+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.360 seconds
[2025-07-18T11:10:56.854+0000] {processor.py:186} INFO - Started process (PID=1333) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:10:56.855+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:10:56.857+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:56.857+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:10:57.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.054+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:57.065+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:10:57.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.168+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:57.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.179+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:10:57.198+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.351 seconds
[2025-07-18T11:11:27.393+0000] {processor.py:186} INFO - Started process (PID=1474) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:11:27.394+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:11:27.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:27.396+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:11:27.597+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:27.597+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:27.607+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:11:27.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:27.711+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:27.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:27.722+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:11:27.742+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.354 seconds
[2025-07-18T11:11:58.492+0000] {processor.py:186} INFO - Started process (PID=1615) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:11:58.494+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:11:58.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:58.495+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:11:58.698+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:58.698+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:58.707+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:11:58.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:58.805+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:58.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:58.817+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:11:58.845+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.357 seconds
[2025-07-18T11:12:29.068+0000] {processor.py:186} INFO - Started process (PID=1756) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:12:29.070+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:12:29.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:29.073+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:12:29.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:29.281+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:29.292+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:12:29.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:29.388+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:29.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:29.399+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:12:29.421+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.362 seconds
[2025-07-18T11:13:00.411+0000] {processor.py:186} INFO - Started process (PID=1897) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:13:00.412+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:13:00.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:00.414+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:13:00.627+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:00.627+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:00.638+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:13:00.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:00.745+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:00.757+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:00.756+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:13:00.779+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.375 seconds
[2025-07-18T11:13:31.321+0000] {processor.py:186} INFO - Started process (PID=2038) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:13:31.322+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:13:31.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:31.324+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:13:31.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:31.530+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:31.540+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:13:31.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:31.642+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:31.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:31.653+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:13:31.672+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.358 seconds
[2025-07-18T11:14:01.959+0000] {processor.py:186} INFO - Started process (PID=2179) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:14:01.960+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:14:01.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:01.962+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:14:02.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:02.204+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:02.215+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:14:02.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:02.332+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:02.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:02.346+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:14:02.368+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.415 seconds
[2025-07-18T11:14:32.579+0000] {processor.py:186} INFO - Started process (PID=2320) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:14:32.580+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:14:32.583+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:32.582+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:14:32.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:32.796+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:32.806+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:14:32.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:32.912+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:32.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:32.923+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:14:32.945+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.373 seconds
[2025-07-18T11:15:03.605+0000] {processor.py:186} INFO - Started process (PID=2461) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:15:03.607+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:15:03.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:03.609+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:15:03.832+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:03.831+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:03.844+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:15:03.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:03.968+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:03.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:03.981+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:15:04.004+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.405 seconds
[2025-07-18T11:15:34.449+0000] {processor.py:186} INFO - Started process (PID=2602) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:15:34.451+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:15:34.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:34.461+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:15:34.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:34.716+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:34.729+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:15:34.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:34.852+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:34.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:34.866+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:15:34.889+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.447 seconds
[2025-07-18T11:16:05.109+0000] {processor.py:186} INFO - Started process (PID=2743) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:16:05.109+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:16:05.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.111+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:16:05.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.327+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:05.338+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:16:05.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.445+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:05.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.458+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:16:05.480+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.378 seconds
[2025-07-18T11:16:35.898+0000] {processor.py:186} INFO - Started process (PID=2884) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:16:35.899+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:16:35.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:35.901+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:16:36.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.112+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:36.123+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:16:36.228+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.228+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:36.243+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.243+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:16:36.264+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.372 seconds
[2025-07-18T11:17:06.717+0000] {processor.py:186} INFO - Started process (PID=3025) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:17:06.719+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:17:06.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:06.721+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:17:06.964+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:06.963+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:06.975+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:17:07.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:07.098+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:07.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:07.111+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:17:07.134+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.426 seconds
[2025-07-18T11:17:37.243+0000] {processor.py:186} INFO - Started process (PID=3166) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:17:37.244+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:17:37.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:37.246+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:17:37.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:37.456+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:37.467+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:17:37.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:37.578+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:37.590+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:37.590+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:17:37.610+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.374 seconds
[2025-07-18T11:18:07.876+0000] {processor.py:186} INFO - Started process (PID=3307) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:18:07.877+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:18:07.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:07.879+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:18:08.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:08.116+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:08.127+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:18:08.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:08.244+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:08.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:08.257+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:18:08.280+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.411 seconds
[2025-07-18T11:18:38.850+0000] {processor.py:186} INFO - Started process (PID=3448) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:18:38.851+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:18:38.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:38.853+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:18:39.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:39.073+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:39.084+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:18:39.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:39.193+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:39.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:39.206+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:18:39.226+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.382 seconds
[2025-07-18T11:19:10.164+0000] {processor.py:186} INFO - Started process (PID=3589) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:19:10.165+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:19:10.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:10.167+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:19:10.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:10.383+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:10.394+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:19:10.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:10.505+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:10.518+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:10.517+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:19:10.540+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.382 seconds
[2025-07-18T11:19:40.827+0000] {processor.py:186} INFO - Started process (PID=3735) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:19:40.828+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:19:40.831+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:40.831+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:19:41.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.038+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:41.050+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:19:41.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.164+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:41.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.178+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:19:41.198+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.377 seconds
[2025-07-18T11:20:11.288+0000] {processor.py:186} INFO - Started process (PID=3876) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:20:11.289+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:20:11.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:11.291+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:20:11.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:11.483+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:11.493+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:20:11.603+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:11.603+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:11.617+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:11.616+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:20:11.639+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.357 seconds
[2025-07-18T12:38:06.736+0000] {processor.py:186} INFO - Started process (PID=186) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:38:06.738+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T12:38:06.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:06.741+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:38:06.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:06.827+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:06.841+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:38:07.092+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.091+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_image_object_pipeline
[2025-07-18T12:38:07.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.113+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_image_object_pipeline
[2025-07-18T12:38:07.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.128+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_image_object_pipeline
[2025-07-18T12:38:07.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.150+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_image_object_pipeline
[2025-07-18T12:38:07.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.162+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_image_object_pipeline
[2025-07-18T12:38:07.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.178+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_image_object_pipeline
[2025-07-18T12:38:07.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.191+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_image_object_pipeline
[2025-07-18T12:38:07.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.192+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:07.209+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T12:38:07.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.209+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_image_object_pipeline
[2025-07-18T12:38:07.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.210+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T12:38:07.228+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.498 seconds
[2025-07-18T12:38:37.667+0000] {processor.py:186} INFO - Started process (PID=328) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:38:37.668+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T12:38:37.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:37.669+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:38:37.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:37.737+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:37.745+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:38:37.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:37.958+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:37.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:37.971+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T12:38:37.991+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.329 seconds
[2025-07-18T12:39:08.221+0000] {processor.py:186} INFO - Started process (PID=464) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:39:08.221+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T12:39:08.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:08.224+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:39:08.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:08.485+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:08.491+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:39:08.577+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:08.577+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:08.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:08.587+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T12:39:08.605+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.391 seconds
[2025-07-18T12:39:38.890+0000] {processor.py:186} INFO - Started process (PID=600) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:39:38.891+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T12:39:38.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:38.893+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:39:38.967+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:38.967+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:38.977+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:39:39.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:39.068+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:39.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:39.078+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T12:39:39.097+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.213 seconds
[2025-07-18T12:40:09.740+0000] {processor.py:186} INFO - Started process (PID=737) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:40:09.740+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T12:40:09.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:09.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:40:09.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:09.814+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:09.824+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:40:09.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:09.918+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:09.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:09.929+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T12:40:09.953+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.219 seconds
[2025-07-18T12:40:40.703+0000] {processor.py:186} INFO - Started process (PID=873) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:40:40.704+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T12:40:40.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:40.706+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:40:40.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:40.774+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:40.783+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:40:40.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:40.881+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:40.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:40.898+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T12:40:40.922+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.223 seconds
[2025-07-18T12:41:11.258+0000] {processor.py:186} INFO - Started process (PID=1009) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:41:11.259+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T12:41:11.261+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:11.261+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:41:11.343+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:11.343+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:41:11.353+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T12:41:11.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:11.456+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:41:11.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:11.468+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T12:41:11.491+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.238 seconds
