from cost_tracking import CostTracker

tracker = CostTracker()
test_data = {
    "operation_type": "chatgpt_message_pipeline",
    "token_usage": {
        "tokens_input": 123,
        "tokens_output": 45,
        "total_tokens": 168,
        "cost_input": 0.01,
        "cost_output": 0.02,
        "total_cost": 0.03
    },
    "additional_metadata": {"dag_id": "chatgpt_message_pipeline"}
}

tracker.save_cost_to_gsheets(test_data, creds_path="/opt/airflow/src/secrets/service_account.json")