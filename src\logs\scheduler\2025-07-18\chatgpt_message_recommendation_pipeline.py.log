[2025-07-18T10:16:49.959+0000] {processor.py:186} INFO - Started process (PID=231) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:16:49.960+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:16:49.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.962+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:16:49.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.994+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:49.999+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:16:50.017+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.064 seconds
[2025-07-18T10:17:20.298+0000] {processor.py:186} INFO - Started process (PID=364) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:17:20.299+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:17:20.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.301+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:17:20.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.331+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.335+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:17:20.351+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.059 seconds
[2025-07-18T10:17:50.676+0000] {processor.py:186} INFO - Started process (PID=493) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:17:50.677+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:17:50.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.678+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:17:50.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.717+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:50.722+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:17:50.925+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.257 seconds
[2025-07-18T10:18:21.342+0000] {processor.py:186} INFO - Started process (PID=626) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:18:21.342+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:18:21.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.343+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:18:21.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.375+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.379+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:18:21.396+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.060 seconds
[2025-07-18T10:18:52.319+0000] {processor.py:186} INFO - Started process (PID=757) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:18:52.320+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:18:52.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.321+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:18:52.354+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.351+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:52.355+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:18:52.370+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.056 seconds
[2025-07-18T10:19:23.205+0000] {processor.py:186} INFO - Started process (PID=886) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:19:23.206+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:19:23.207+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.207+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:19:23.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.241+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:23.245+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:19:23.261+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.063 seconds
[2025-07-18T10:19:54.225+0000] {processor.py:186} INFO - Started process (PID=1019) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:19:54.226+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:19:54.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.227+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:19:54.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.277+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:54.285+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:19:54.305+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.086 seconds
[2025-07-18T10:20:25.262+0000] {processor.py:186} INFO - Started process (PID=1150) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:20:25.263+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:20:25.264+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.264+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:20:25.298+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.295+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:25.300+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:20:25.318+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.062 seconds
[2025-07-18T10:20:56.217+0000] {processor.py:186} INFO - Started process (PID=1281) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:20:56.218+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:20:56.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.219+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:20:56.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.249+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:56.253+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:20:56.268+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.056 seconds
[2025-07-18T10:21:27.195+0000] {processor.py:186} INFO - Started process (PID=1412) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:21:27.196+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:21:27.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.197+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:21:27.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.230+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:27.234+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:21:27.251+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.062 seconds
[2025-07-18T10:21:57.366+0000] {processor.py:186} INFO - Started process (PID=1543) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:21:57.367+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:21:57.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.368+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:21:57.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.395+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:57.399+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:21:57.414+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.053 seconds
[2025-07-18T10:22:28.425+0000] {processor.py:186} INFO - Started process (PID=1674) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:22:28.426+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:22:28.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.427+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:22:28.464+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.461+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:28.465+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:22:28.480+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.060 seconds
[2025-07-18T10:22:59.379+0000] {processor.py:186} INFO - Started process (PID=1805) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:22:59.379+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:22:59.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.380+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:22:59.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.411+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:59.415+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:22:59.430+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.057 seconds
[2025-07-18T10:23:30.248+0000] {processor.py:186} INFO - Started process (PID=1934) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:23:30.249+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:23:30.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.250+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:23:30.282+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.279+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:30.283+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:23:30.299+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.056 seconds
[2025-07-18T10:24:01.198+0000] {processor.py:186} INFO - Started process (PID=2067) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:24:01.199+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:24:01.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.200+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:24:01.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.235+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:01.238+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:24:01.258+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.068 seconds
[2025-07-18T10:24:31.447+0000] {processor.py:186} INFO - Started process (PID=2198) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:24:31.448+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:24:31.450+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.450+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:24:31.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.481+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.485+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:24:31.502+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.062 seconds
[2025-07-18T10:25:04.271+0000] {processor.py:186} INFO - Started process (PID=2329) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:25:04.272+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:25:04.273+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.273+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:25:04.460+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.460+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:04.472+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:25:04.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.569+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:04.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.580+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:25:04.600+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.334 seconds
[2025-07-18T10:26:22.069+0000] {processor.py:186} INFO - Started process (PID=233) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:26:22.070+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:26:22.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:22.071+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:26:22.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:22.449+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:22.457+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:26:22.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:22.551+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:22.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:22.562+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:26:22.577+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.514 seconds
[2025-07-18T10:26:54.204+0000] {processor.py:186} INFO - Started process (PID=362) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:26:54.205+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:26:54.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.208+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:26:54.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.578+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:54.585+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:26:54.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.693+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:54.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.707+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:26:54.731+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.533 seconds
[2025-07-18T10:27:25.167+0000] {processor.py:186} INFO - Started process (PID=493) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:27:25.168+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:27:25.171+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:25.171+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:27:25.539+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:25.538+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:25.546+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:27:25.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:25.672+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:25.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:25.683+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:27:25.703+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.542 seconds
[2025-07-18T10:27:56.278+0000] {processor.py:186} INFO - Started process (PID=624) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:27:56.279+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:27:56.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.280+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:27:56.482+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.482+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:56.490+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:27:56.591+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.590+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:56.603+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.603+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:27:56.623+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.350 seconds
[2025-07-18T10:28:27.323+0000] {processor.py:186} INFO - Started process (PID=755) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:28:27.324+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:28:27.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:27.326+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:28:27.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:27.536+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:27.544+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:28:27.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:27.644+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:27.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:27.657+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:28:27.680+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.362 seconds
[2025-07-18T10:28:57.752+0000] {processor.py:186} INFO - Started process (PID=886) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:28:57.753+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:28:57.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:57.755+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:28:57.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:57.958+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:57.966+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:28:58.069+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.069+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:58.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.079+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:28:58.099+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.354 seconds
[2025-07-18T10:29:28.424+0000] {processor.py:186} INFO - Started process (PID=1017) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:29:28.425+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:29:28.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:28.428+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:29:28.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:28.635+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:28.644+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:29:28.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:28.743+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:28.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:28.754+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:29:28.774+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.356 seconds
[2025-07-18T10:29:58.876+0000] {processor.py:186} INFO - Started process (PID=1148) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:29:58.877+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:29:58.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:58.879+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:29:59.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.067+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:59.076+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:29:59.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.167+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:59.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.179+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:29:59.199+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.329 seconds
[2025-07-18T10:30:29.529+0000] {processor.py:186} INFO - Started process (PID=1279) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:30:29.530+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:30:29.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:29.532+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:30:29.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:29.721+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:29.730+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:30:29.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:29.828+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:29.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:29.840+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:30:29.861+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.338 seconds
[2025-07-18T10:31:00.072+0000] {processor.py:186} INFO - Started process (PID=1410) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:31:00.073+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:31:00.076+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:00.075+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:31:00.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:00.276+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:00.284+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:31:00.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:00.384+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:00.395+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:00.395+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:31:00.417+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.351 seconds
[2025-07-18T10:31:30.599+0000] {processor.py:186} INFO - Started process (PID=1541) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:31:30.600+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:31:30.603+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:30.602+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:31:30.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:30.797+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:30.807+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:31:30.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:30.906+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:30.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:30.918+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:31:30.939+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.345 seconds
[2025-07-18T10:32:01.203+0000] {processor.py:186} INFO - Started process (PID=1672) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:32:01.204+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:32:01.207+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:01.206+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:32:01.409+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:01.408+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:01.416+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:32:01.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:01.526+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:01.544+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:01.543+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:32:01.564+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.367 seconds
[2025-07-18T10:32:31.675+0000] {processor.py:186} INFO - Started process (PID=1803) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:32:31.675+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:32:31.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:31.677+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:32:31.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:31.848+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:31.858+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:32:31.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:31.955+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:31.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:31.966+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:32:31.985+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.316 seconds
[2025-07-18T10:33:02.478+0000] {processor.py:186} INFO - Started process (PID=1934) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:33:02.479+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:33:02.482+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:02.482+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:33:02.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:02.682+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:02.690+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:33:02.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:02.785+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:02.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:02.796+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:33:02.816+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.343 seconds
[2025-07-18T10:33:33.130+0000] {processor.py:186} INFO - Started process (PID=2065) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:33:33.131+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:33:33.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:33.133+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:33:33.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:33.313+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:33.322+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:33:33.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:33.418+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:33.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:33.428+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:33:33.447+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.323 seconds
[2025-07-18T10:34:03.695+0000] {processor.py:186} INFO - Started process (PID=2196) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:34:03.696+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:34:03.698+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:03.698+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:34:03.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:03.933+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:03.943+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:34:04.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:04.043+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:04.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:04.054+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:34:04.072+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.384 seconds
[2025-07-18T10:34:34.464+0000] {processor.py:186} INFO - Started process (PID=2327) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:34:34.465+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:34:34.467+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:34.467+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:34:34.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:34.643+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:34.653+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:34:34.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:34.749+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:34.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:34.761+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:34:34.777+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.320 seconds
[2025-07-18T10:35:05.242+0000] {processor.py:186} INFO - Started process (PID=2458) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:35:05.243+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:35:05.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:05.244+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:35:05.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:05.428+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:05.436+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:35:05.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:05.525+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:05.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:05.536+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:35:05.556+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.319 seconds
[2025-07-18T10:35:36.029+0000] {processor.py:186} INFO - Started process (PID=2589) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:35:36.030+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:35:36.033+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:36.032+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:35:36.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:36.231+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:36.240+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:35:36.338+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:36.338+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:36.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:36.349+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:35:36.369+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.346 seconds
[2025-07-18T10:36:06.696+0000] {processor.py:186} INFO - Started process (PID=2720) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:36:06.697+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:36:06.699+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:06.699+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:36:06.921+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:06.921+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:06.929+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:36:07.023+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:07.022+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:07.033+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:07.033+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:36:07.050+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.363 seconds
[2025-07-18T10:36:37.680+0000] {processor.py:186} INFO - Started process (PID=2851) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:36:37.682+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:36:37.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:37.684+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:36:37.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:37.904+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:37.913+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:36:38.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:38.012+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:38.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:38.024+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:36:38.044+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.373 seconds
[2025-07-18T10:37:08.510+0000] {processor.py:186} INFO - Started process (PID=2982) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:37:08.511+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:37:08.514+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:08.514+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:37:08.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:08.719+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:08.728+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:37:08.833+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:08.832+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:08.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:08.845+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:37:08.867+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.362 seconds
[2025-07-18T10:37:39.420+0000] {processor.py:186} INFO - Started process (PID=3113) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:37:39.421+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:37:39.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:39.422+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:37:39.601+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:39.600+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:39.609+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:37:39.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:39.703+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:39.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:39.714+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:37:39.733+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.319 seconds
[2025-07-18T10:38:09.906+0000] {processor.py:186} INFO - Started process (PID=3244) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:38:09.907+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:38:09.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:09.909+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:38:10.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:10.086+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:10.094+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:38:10.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:10.195+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:10.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:10.205+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:38:10.223+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.323 seconds
[2025-07-18T10:38:40.875+0000] {processor.py:186} INFO - Started process (PID=3377) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:38:40.876+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:38:40.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:40.878+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:38:41.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:41.068+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:41.078+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:38:41.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:41.176+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:41.186+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:41.186+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:38:41.206+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.336 seconds
[2025-07-18T10:39:11.513+0000] {processor.py:186} INFO - Started process (PID=3508) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:39:11.514+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:39:11.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:11.516+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:39:11.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:11.703+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:11.712+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:39:11.804+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:11.804+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:11.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:11.814+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:39:11.832+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.324 seconds
[2025-07-18T10:39:42.238+0000] {processor.py:186} INFO - Started process (PID=3639) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:39:42.239+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:39:42.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:42.241+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:39:42.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:42.438+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:42.448+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:39:42.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:42.545+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:42.554+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:42.554+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:39:42.574+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.342 seconds
[2025-07-18T10:40:12.928+0000] {processor.py:186} INFO - Started process (PID=3770) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:40:12.929+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:40:12.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:12.931+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:40:13.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:13.110+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:13.118+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:40:13.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:13.207+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:13.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:13.218+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:40:13.237+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.315 seconds
[2025-07-18T10:40:43.506+0000] {processor.py:186} INFO - Started process (PID=3901) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:40:43.507+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:40:43.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:43.509+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:40:43.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:43.703+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:43.710+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:40:43.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:43.801+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:43.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:43.810+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:40:43.827+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.327 seconds
[2025-07-18T10:41:14.224+0000] {processor.py:186} INFO - Started process (PID=4032) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:41:14.226+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:41:14.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:14.229+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:41:14.467+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:14.467+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:14.475+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:41:14.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:14.573+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:14.583+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:14.583+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:41:14.604+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.392 seconds
[2025-07-18T10:41:44.831+0000] {processor.py:186} INFO - Started process (PID=4168) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:41:44.832+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:41:44.836+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:44.836+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:41:45.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:45.074+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:45.083+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:41:45.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:45.174+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:45.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:45.184+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:41:45.203+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.379 seconds
[2025-07-18T10:42:15.376+0000] {processor.py:186} INFO - Started process (PID=4302) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:42:15.377+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:42:15.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:15.379+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:42:15.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:15.579+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:15.588+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:42:15.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:15.811+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:15.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:15.822+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:42:15.839+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.469 seconds
[2025-07-18T10:42:58.743+0000] {processor.py:186} INFO - Started process (PID=236) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:42:58.744+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:42:58.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.746+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:42:59.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.082+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:59.089+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:42:59.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.180+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:59.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.189+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:42:59.207+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.471 seconds
[2025-07-18T10:43:30.929+0000] {processor.py:186} INFO - Started process (PID=374) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:43:30.930+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:43:30.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:30.932+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:43:31.243+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:31.243+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:31.251+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:43:31.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:31.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:31.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:31.346+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:43:31.363+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.439 seconds
[2025-07-18T10:44:01.449+0000] {processor.py:186} INFO - Started process (PID=510) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:44:01.450+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:44:01.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:01.452+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:44:01.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:01.781+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:01.788+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:44:01.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:01.875+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:01.886+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:01.886+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:44:01.901+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.457 seconds
[2025-07-18T10:44:32.079+0000] {processor.py:186} INFO - Started process (PID=644) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:44:32.080+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:44:32.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:32.082+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:44:32.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:32.269+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:32.277+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:44:32.371+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:32.370+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:32.380+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:32.380+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:44:32.398+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.323 seconds
[2025-07-18T10:45:02.515+0000] {processor.py:186} INFO - Started process (PID=780) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:45:02.516+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:45:02.518+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.518+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:45:02.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.711+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:02.720+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:45:02.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.827+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:02.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.838+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:45:02.858+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.348 seconds
[2025-07-18T10:45:33.116+0000] {processor.py:186} INFO - Started process (PID=916) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:45:33.117+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:45:33.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:33.119+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:45:33.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:33.333+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:33.342+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:45:33.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:33.452+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:33.465+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:33.464+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:45:33.486+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.376 seconds
[2025-07-18T10:46:04.443+0000] {processor.py:186} INFO - Started process (PID=1054) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:46:04.444+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:46:04.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:04.446+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:46:04.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:04.648+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:04.658+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:46:04.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:04.760+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:04.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:04.771+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:46:04.791+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.355 seconds
[2025-07-18T10:46:35.349+0000] {processor.py:186} INFO - Started process (PID=1190) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:46:35.350+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:46:35.353+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:35.353+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:46:35.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:35.573+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:35.584+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:46:35.698+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:35.698+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:35.710+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:35.710+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:46:35.730+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.387 seconds
[2025-07-18T10:47:05.863+0000] {processor.py:186} INFO - Started process (PID=1326) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:47:05.864+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:47:05.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.866+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:47:06.056+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:06.056+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:06.065+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:47:06.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:06.166+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:06.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:06.177+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:47:06.196+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.338 seconds
[2025-07-18T10:48:04.153+0000] {processor.py:186} INFO - Started process (PID=236) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:48:04.154+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:48:04.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.156+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:48:04.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.491+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:04.498+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:48:04.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.589+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:04.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.598+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:48:04.619+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.473 seconds
[2025-07-18T10:48:35.268+0000] {processor.py:186} INFO - Started process (PID=372) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:48:35.269+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:48:35.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.272+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:48:35.625+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.625+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:35.633+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:48:35.734+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.734+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:35.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.744+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:48:35.762+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.499 seconds
[2025-07-18T10:49:06.071+0000] {processor.py:186} INFO - Started process (PID=510) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:49:06.072+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:49:06.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:06.074+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:49:06.409+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:06.409+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:06.416+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:49:06.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:06.523+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:06.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:06.536+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:49:06.554+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.488 seconds
[2025-07-18T10:49:37.505+0000] {processor.py:186} INFO - Started process (PID=646) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:49:37.506+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:49:37.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.508+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:49:37.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.712+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:37.722+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:49:37.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.824+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:37.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.835+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:49:37.857+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.357 seconds
[2025-07-18T10:50:08.187+0000] {processor.py:186} INFO - Started process (PID=782) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:50:08.188+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:50:08.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.192+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:50:08.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.448+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:08.457+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:50:08.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.561+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:08.572+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.572+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:50:08.594+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.414 seconds
[2025-07-18T10:50:38.991+0000] {processor.py:186} INFO - Started process (PID=918) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:50:38.992+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:50:38.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:38.994+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:50:39.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.192+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:39.202+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:50:39.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:39.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.310+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:50:39.328+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.343 seconds
[2025-07-18T10:51:09.693+0000] {processor.py:186} INFO - Started process (PID=1054) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:51:09.693+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:51:09.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:09.695+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:51:09.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:09.890+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:09.901+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:51:09.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:09.998+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:10.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.010+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:51:10.035+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.348 seconds
[2025-07-18T10:51:40.224+0000] {processor.py:186} INFO - Started process (PID=1191) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:51:40.225+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:51:40.228+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.227+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:51:40.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.434+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:40.443+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:51:40.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.536+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:40.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.548+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:51:40.568+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.349 seconds
[2025-07-18T10:52:10.672+0000] {processor.py:186} INFO - Started process (PID=1327) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:52:10.673+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:52:10.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.675+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:52:10.867+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.867+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:10.877+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:52:10.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.974+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:10.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.984+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:52:11.003+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.337 seconds
[2025-07-18T10:52:41.299+0000] {processor.py:186} INFO - Started process (PID=1463) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:52:41.300+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:52:41.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:41.303+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:52:41.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:41.505+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:41.515+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:52:41.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:41.612+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:41.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:41.624+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:52:41.645+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.351 seconds
[2025-07-18T10:53:11.849+0000] {processor.py:186} INFO - Started process (PID=1599) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:53:11.850+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:53:11.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:11.853+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:53:12.059+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.059+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:12.068+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:53:12.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.170+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:12.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.181+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:53:12.204+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.361 seconds
[2025-07-18T10:53:42.510+0000] {processor.py:186} INFO - Started process (PID=1733) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:53:42.511+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:53:42.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:42.513+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:53:42.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:42.700+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:42.709+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:53:42.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:42.796+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:42.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:42.806+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:53:42.823+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.319 seconds
[2025-07-18T10:54:13.390+0000] {processor.py:186} INFO - Started process (PID=1869) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:54:13.391+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:54:13.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:13.393+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:54:13.595+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:13.594+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:13.603+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:54:13.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:13.704+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:13.717+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:13.717+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:54:13.742+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.359 seconds
[2025-07-18T10:54:44.016+0000] {processor.py:186} INFO - Started process (PID=2005) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:54:44.017+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:54:44.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.020+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:54:44.222+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.222+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:44.231+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:54:44.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.333+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:44.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.345+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:54:44.363+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.354 seconds
[2025-07-18T10:55:14.566+0000] {processor.py:186} INFO - Started process (PID=2141) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:55:14.567+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:55:14.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:14.569+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:55:14.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:14.756+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:14.766+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:55:14.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:14.854+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:14.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:14.865+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:55:14.882+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.322 seconds
[2025-07-18T10:55:45.413+0000] {processor.py:186} INFO - Started process (PID=2277) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:55:45.413+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:55:45.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:45.415+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:55:45.610+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:45.610+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:45.619+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:55:45.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:45.720+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:45.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:45.732+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:55:45.751+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.344 seconds
[2025-07-18T10:57:28.853+0000] {processor.py:186} INFO - Started process (PID=236) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:57:28.854+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:57:28.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.856+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:57:29.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.177+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:29.184+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:57:29.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.270+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:29.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.278+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:57:29.299+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.451 seconds
[2025-07-18T10:58:00.275+0000] {processor.py:186} INFO - Started process (PID=372) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:58:00.276+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:58:00.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:58:00.603+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.603+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:00.611+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:58:00.704+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.703+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:00.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.713+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:58:00.728+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.462 seconds
[2025-07-18T10:58:31.300+0000] {processor.py:186} INFO - Started process (PID=508) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:58:31.301+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:58:31.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.304+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:58:31.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.650+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:31.656+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:58:31.757+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.757+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:31.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.767+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:58:31.782+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.488 seconds
[2025-07-18T10:59:02.260+0000] {processor.py:186} INFO - Started process (PID=646) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:59:02.261+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:59:02.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.263+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:59:02.444+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.444+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:02.453+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:59:02.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.545+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:02.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.555+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:59:02.572+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.317 seconds
[2025-07-18T10:59:33.163+0000] {processor.py:186} INFO - Started process (PID=782) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:59:33.164+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T10:59:33.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.166+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:59:33.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.352+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:33.362+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T10:59:33.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.454+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:33.465+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.464+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T10:59:33.485+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.327 seconds
[2025-07-18T11:00:03.880+0000] {processor.py:186} INFO - Started process (PID=918) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:00:03.881+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:00:03.883+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.882+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:00:04.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.108+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:04.115+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:00:04.222+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.221+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:04.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.232+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:00:04.253+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.379 seconds
[2025-07-18T11:00:34.576+0000] {processor.py:186} INFO - Started process (PID=1054) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:00:34.577+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:00:34.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.580+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:00:34.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.854+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:34.865+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:00:34.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.982+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:34.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.994+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:00:35.015+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.447 seconds
[2025-07-18T11:01:05.125+0000] {processor.py:186} INFO - Started process (PID=1190) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:01:05.126+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:01:05.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.128+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:01:05.317+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.317+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:05.326+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:01:05.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.418+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:05.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.428+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:01:05.448+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.328 seconds
[2025-07-18T11:01:35.505+0000] {processor.py:186} INFO - Started process (PID=1326) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:01:35.506+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:01:35.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.508+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:01:35.686+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.685+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:35.693+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:01:35.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.778+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:35.787+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.786+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:01:35.804+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.304 seconds
[2025-07-18T11:02:06.815+0000] {processor.py:186} INFO - Started process (PID=1462) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:02:06.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:02:06.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:06.818+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:02:07.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:07.005+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:07.013+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:02:07.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:07.099+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:07.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:07.110+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:02:07.127+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.318 seconds
[2025-07-18T11:02:37.800+0000] {processor.py:186} INFO - Started process (PID=1598) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:02:37.801+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:02:37.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:37.803+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:02:38.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:38.016+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:38.026+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:02:38.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:38.133+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:38.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:38.144+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:02:38.164+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.370 seconds
[2025-07-18T11:03:08.946+0000] {processor.py:186} INFO - Started process (PID=1734) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:03:08.947+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:03:08.949+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:08.949+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:03:09.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.140+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:09.149+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:03:09.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.243+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:09.256+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.256+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:03:09.277+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.337 seconds
[2025-07-18T11:03:39.595+0000] {processor.py:186} INFO - Started process (PID=1868) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:03:39.597+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:03:39.602+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:39.601+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:03:39.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:39.827+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:39.836+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:03:39.948+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:39.948+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:39.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:39.959+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:03:39.980+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.393 seconds
[2025-07-18T11:04:10.380+0000] {processor.py:186} INFO - Started process (PID=2004) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:04:10.381+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:04:10.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:10.383+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:04:10.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:10.580+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:10.590+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:04:10.689+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:10.689+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:10.702+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:10.702+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:04:10.719+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.347 seconds
[2025-07-18T11:04:40.983+0000] {processor.py:186} INFO - Started process (PID=2140) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:04:40.984+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:04:40.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:40.987+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:04:41.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:41.188+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:41.199+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:04:41.315+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:41.315+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:41.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:41.326+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:04:41.352+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.375 seconds
[2025-07-18T11:05:11.677+0000] {processor.py:186} INFO - Started process (PID=2276) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:05:11.678+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:05:11.681+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:11.680+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:05:11.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:11.923+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:11.934+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:05:12.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:12.052+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:12.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:12.061+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:05:12.082+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.412 seconds
[2025-07-18T11:06:51.463+0000] {processor.py:186} INFO - Started process (PID=242) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:06:51.464+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:06:51.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.466+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:06:51.780+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.780+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:51.787+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:06:51.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.883+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:51.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.895+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:06:51.913+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.455 seconds
[2025-07-18T11:07:22.790+0000] {processor.py:186} INFO - Started process (PID=389) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:07:22.792+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:07:22.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.795+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:07:23.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.151+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:23.158+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:07:23.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.249+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:23.260+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.259+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:07:23.279+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.498 seconds
[2025-07-18T11:07:54.131+0000] {processor.py:186} INFO - Started process (PID=530) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:07:54.132+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:07:54.134+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.134+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:07:54.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.472+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:54.479+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:07:54.572+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.572+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:54.582+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.582+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:07:54.602+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.477 seconds
[2025-07-18T11:08:25.324+0000] {processor.py:186} INFO - Started process (PID=673) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:08:25.325+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:08:25.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.327+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:08:25.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.512+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:25.522+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:08:25.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.635+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:25.647+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.646+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:08:25.666+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.348 seconds
[2025-07-18T11:08:55.806+0000] {processor.py:186} INFO - Started process (PID=812) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:08:55.807+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:08:55.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.809+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:08:56.031+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.030+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:56.039+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:08:56.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.140+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:56.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.151+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:08:56.177+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.377 seconds
[2025-07-18T11:09:26.813+0000] {processor.py:186} INFO - Started process (PID=955) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:09:26.814+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:09:26.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:26.816+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:09:27.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.038+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:27.049+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:09:27.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.165+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:27.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.178+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:09:27.203+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.395 seconds
[2025-07-18T11:09:57.685+0000] {processor.py:186} INFO - Started process (PID=1101) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:09:57.686+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:09:57.689+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.689+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:09:57.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.890+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:57.901+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:09:58.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.008+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:58.022+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.021+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:09:58.046+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.367 seconds
[2025-07-18T11:10:28.462+0000] {processor.py:186} INFO - Started process (PID=1242) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:10:28.463+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:10:28.465+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.465+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:10:28.655+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.654+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:28.666+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:10:28.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.766+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:28.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.778+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:10:28.797+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.341 seconds
[2025-07-18T11:10:59.164+0000] {processor.py:186} INFO - Started process (PID=1381) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:10:59.165+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:10:59.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.167+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:10:59.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.386+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:59.394+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:10:59.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.500+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:59.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.511+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:10:59.531+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.375 seconds
[2025-07-18T11:11:29.725+0000] {processor.py:186} INFO - Started process (PID=1519) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:11:29.726+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:11:29.729+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:29.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:11:29.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:29.929+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:29.939+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:11:30.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.032+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:30.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.042+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:11:30.058+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.339 seconds
[2025-07-18T11:12:00.398+0000] {processor.py:186} INFO - Started process (PID=1660) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:12:00.399+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:12:00.402+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:00.401+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:12:00.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:00.613+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:00.622+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:12:00.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:00.722+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:00.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:00.732+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:12:00.753+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.363 seconds
[2025-07-18T11:12:31.331+0000] {processor.py:186} INFO - Started process (PID=1801) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:12:31.331+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:12:31.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.333+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:12:31.534+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.533+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:31.542+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:12:31.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.634+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:31.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.643+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:12:31.662+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.337 seconds
[2025-07-18T11:13:03.048+0000] {processor.py:186} INFO - Started process (PID=1942) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:13:03.050+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:13:03.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:03.052+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:13:03.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:03.314+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:03.323+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:13:03.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:03.426+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:03.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:03.438+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:13:03.455+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.415 seconds
[2025-07-18T11:13:33.556+0000] {processor.py:186} INFO - Started process (PID=2083) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:13:33.557+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:13:33.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.559+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:13:33.759+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.759+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:33.767+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:13:33.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.856+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:33.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.865+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:13:33.884+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.334 seconds
[2025-07-18T11:14:04.504+0000] {processor.py:186} INFO - Started process (PID=2224) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:14:04.505+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:14:04.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.508+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:14:04.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.736+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:04.745+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:14:04.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.850+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:04.862+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.861+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:14:04.883+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.386 seconds
[2025-07-18T11:14:35.046+0000] {processor.py:186} INFO - Started process (PID=2365) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:14:35.047+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:14:35.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:35.049+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:14:35.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:35.246+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:35.254+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:14:35.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:35.344+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:35.354+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:35.354+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:14:35.373+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.333 seconds
[2025-07-18T11:15:05.741+0000] {processor.py:186} INFO - Started process (PID=2506) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:15:05.742+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:15:05.745+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.745+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:15:05.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.959+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:05.968+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:15:06.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.078+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:06.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.090+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:15:06.116+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.382 seconds
[2025-07-18T11:15:37.018+0000] {processor.py:186} INFO - Started process (PID=2647) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:15:37.019+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:15:37.021+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.021+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:15:37.243+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.243+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:37.252+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:15:37.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.373+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:37.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.388+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:15:37.412+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.399 seconds
[2025-07-18T11:16:07.632+0000] {processor.py:186} INFO - Started process (PID=2788) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:16:07.633+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:16:07.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.636+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:16:07.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.860+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:07.869+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:16:07.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.984+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:08.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.000+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:16:08.024+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.399 seconds
[2025-07-18T11:16:38.295+0000] {processor.py:186} INFO - Started process (PID=2929) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:16:38.296+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:16:38.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:38.298+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:16:38.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:38.499+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:38.508+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:16:38.618+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:38.617+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:38.630+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:38.630+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:16:38.648+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.359 seconds
[2025-07-18T11:17:09.624+0000] {processor.py:186} INFO - Started process (PID=3070) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:17:09.625+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:17:09.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:09.627+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:17:09.862+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:09.861+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:09.871+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:17:10.048+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.048+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:10.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.064+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:17:10.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.471 seconds
[2025-07-18T11:17:40.449+0000] {processor.py:186} INFO - Started process (PID=3211) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:17:40.449+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:17:40.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:40.451+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:17:40.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:40.683+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:40.694+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:17:40.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:40.844+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:40.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:40.856+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:17:40.879+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.436 seconds
[2025-07-18T11:18:11.173+0000] {processor.py:186} INFO - Started process (PID=3352) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:18:11.174+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:18:11.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.177+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:18:11.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.427+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:11.437+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:18:11.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.557+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:11.572+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.572+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:18:11.595+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.429 seconds
[2025-07-18T11:18:41.953+0000] {processor.py:186} INFO - Started process (PID=3493) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:18:41.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:18:41.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.957+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:18:42.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.161+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:42.168+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:18:42.267+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.267+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:42.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.279+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:18:42.298+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.351 seconds
[2025-07-18T11:19:12.859+0000] {processor.py:186} INFO - Started process (PID=3634) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:19:12.860+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:19:12.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:12.862+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:19:13.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:13.097+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:13.105+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:19:13.229+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:13.229+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:13.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:13.245+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:19:13.268+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.416 seconds
[2025-07-18T11:19:43.910+0000] {processor.py:186} INFO - Started process (PID=3780) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:19:43.911+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:19:43.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:43.913+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:19:44.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.144+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:44.153+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:19:44.266+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.266+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:44.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.277+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:19:44.299+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.396 seconds
[2025-07-18T11:20:14.758+0000] {processor.py:186} INFO - Started process (PID=3921) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:20:14.759+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:20:14.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:14.762+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:20:14.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:14.983+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:14.992+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:20:15.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.097+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:15.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.109+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:20:15.130+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.378 seconds
