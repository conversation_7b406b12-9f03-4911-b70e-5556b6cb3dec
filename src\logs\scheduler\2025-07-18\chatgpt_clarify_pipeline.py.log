[2025-07-18T10:16:50.522+0000] {processor.py:186} INFO - Started process (PID=268) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:16:50.524+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:16:50.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.526+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:16:50.577+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.574+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:50.579+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:16:50.600+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.086 seconds
[2025-07-18T10:17:20.784+0000] {processor.py:186} INFO - Started process (PID=399) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:17:20.785+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:17:20.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.788+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:17:20.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.819+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.823+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:17:20.844+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.065 seconds
[2025-07-18T10:17:51.625+0000] {processor.py:186} INFO - Started process (PID=528) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:17:51.626+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:17:51.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.627+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:17:51.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.804+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:51.809+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:17:51.825+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.207 seconds
[2025-07-18T10:18:22.765+0000] {processor.py:186} INFO - Started process (PID=661) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:18:22.766+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:18:22.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:22.767+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:18:22.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:22.797+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:22.802+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:18:22.818+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.059 seconds
[2025-07-18T10:18:53.706+0000] {processor.py:186} INFO - Started process (PID=792) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:18:53.707+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:18:53.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:53.708+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:18:53.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:53.738+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:53.741+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:18:53.760+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.060 seconds
[2025-07-18T10:19:24.682+0000] {processor.py:186} INFO - Started process (PID=923) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:19:24.683+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:19:24.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:24.684+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:19:24.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:24.713+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:24.718+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:19:24.733+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.057 seconds
[2025-07-18T10:19:55.671+0000] {processor.py:186} INFO - Started process (PID=1054) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:19:55.671+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:19:55.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:55.673+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:19:55.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:55.702+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:55.707+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:19:55.725+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.060 seconds
[2025-07-18T10:20:26.667+0000] {processor.py:186} INFO - Started process (PID=1185) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:20:26.668+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:20:26.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:26.669+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:20:26.702+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:26.700+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:26.704+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:20:26.720+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.058 seconds
[2025-07-18T10:20:57.622+0000] {processor.py:186} INFO - Started process (PID=1316) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:20:57.622+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:20:57.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.623+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:20:57.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.655+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:57.659+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:20:57.674+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.058 seconds
[2025-07-18T10:21:28.615+0000] {processor.py:186} INFO - Started process (PID=1447) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:21:28.616+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:21:28.617+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.617+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:21:28.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.651+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:28.656+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:21:28.674+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.065 seconds
[2025-07-18T10:21:58.773+0000] {processor.py:186} INFO - Started process (PID=1578) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:21:58.774+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:21:58.776+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:58.775+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:21:58.807+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:58.805+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:58.809+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:21:58.825+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.057 seconds
[2025-07-18T10:22:29.805+0000] {processor.py:186} INFO - Started process (PID=1709) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:22:29.806+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:22:29.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:29.807+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:22:29.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:29.840+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:29.844+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:22:29.860+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.061 seconds
[2025-07-18T10:23:00.779+0000] {processor.py:186} INFO - Started process (PID=1840) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:23:00.780+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:23:00.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:00.781+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:23:00.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:00.810+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:00.815+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:23:00.830+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.056 seconds
[2025-07-18T10:23:31.636+0000] {processor.py:186} INFO - Started process (PID=1971) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:23:31.637+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:23:31.638+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.638+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:23:31.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.667+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:31.671+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:23:31.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.056 seconds
[2025-07-18T10:24:01.736+0000] {processor.py:186} INFO - Started process (PID=2102) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:24:01.737+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:24:01.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.738+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:24:01.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.773+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:01.778+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:24:01.799+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.070 seconds
[2025-07-18T10:24:31.856+0000] {processor.py:186} INFO - Started process (PID=2231) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:24:31.858+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:24:31.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.859+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:24:31.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.890+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_clarify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_clarify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.895+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:24:31.910+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.060 seconds
[2025-07-18T10:25:05.689+0000] {processor.py:186} INFO - Started process (PID=2362) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:25:05.690+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:25:05.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.691+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:25:05.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.871+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:05.880+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:25:05.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.968+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:05.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.977+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:25:05.999+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.315 seconds
[2025-07-18T10:26:23.744+0000] {processor.py:186} INFO - Started process (PID=266) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:26:23.745+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:26:23.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.747+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:26:24.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.110+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:24.115+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:26:24.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.204+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:24.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.214+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:26:24.233+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.494 seconds
[2025-07-18T10:26:56.083+0000] {processor.py:186} INFO - Started process (PID=397) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:26:56.084+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:26:56.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.086+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:26:56.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.438+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:56.446+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:26:56.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.541+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:56.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.553+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:26:56.571+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.494 seconds
[2025-07-18T10:27:27.324+0000] {processor.py:186} INFO - Started process (PID=528) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:27:27.325+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:27:27.328+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.327+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:27:27.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.671+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:27.680+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:27:27.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.777+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:27.787+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.786+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:27:27.806+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.488 seconds
[2025-07-18T10:27:58.803+0000] {processor.py:186} INFO - Started process (PID=661) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:27:58.803+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:27:58.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:58.805+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:27:59.023+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.022+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:59.032+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:27:59.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.147+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:59.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.159+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:27:59.180+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.383 seconds
[2025-07-18T10:28:29.282+0000] {processor.py:186} INFO - Started process (PID=792) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:28:29.283+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:28:29.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:29.285+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:28:29.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:29.487+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:29.496+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:28:29.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:29.599+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:29.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:29.612+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:28:29.635+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.358 seconds
[2025-07-18T10:28:59.757+0000] {processor.py:186} INFO - Started process (PID=921) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:28:59.758+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:28:59.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.760+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:28:59.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.983+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:59.992+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:29:00.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.101+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:00.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.112+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:29:00.132+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.384 seconds
[2025-07-18T10:29:30.431+0000] {processor.py:186} INFO - Started process (PID=1052) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:29:30.432+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:29:30.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:30.433+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:29:30.629+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:30.628+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:30.638+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:29:30.735+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:30.734+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:30.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:30.744+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:29:30.764+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.339 seconds
[2025-07-18T10:30:01.139+0000] {processor.py:186} INFO - Started process (PID=1183) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:30:01.140+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:30:01.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.142+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:30:01.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.334+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:01.343+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:30:01.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.447+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:01.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.458+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:30:01.478+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.344 seconds
[2025-07-18T10:30:32.042+0000] {processor.py:186} INFO - Started process (PID=1315) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:30:32.043+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:30:32.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.045+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:30:32.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.230+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:32.238+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:30:32.330+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.329+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:32.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.341+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:30:32.360+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.323 seconds
[2025-07-18T10:31:02.604+0000] {processor.py:186} INFO - Started process (PID=1447) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:31:02.605+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:31:02.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.607+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:31:02.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.802+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:02.815+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:31:02.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.922+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:02.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.934+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:31:02.953+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.355 seconds
[2025-07-18T10:31:33.052+0000] {processor.py:186} INFO - Started process (PID=1578) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:31:33.053+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:31:33.055+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.054+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:31:33.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.222+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:33.230+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:31:33.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.312+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:33.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.323+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:31:33.340+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.294 seconds
[2025-07-18T10:32:03.817+0000] {processor.py:186} INFO - Started process (PID=1707) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:32:03.819+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:32:03.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.821+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:32:04.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.036+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:04.046+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:32:04.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.154+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:04.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.170+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:32:04.192+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.383 seconds
[2025-07-18T10:32:34.566+0000] {processor.py:186} INFO - Started process (PID=1840) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:32:34.567+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:32:34.569+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.569+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:32:34.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.740+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:34.748+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:32:34.836+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.836+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:34.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.844+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:32:34.861+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.300 seconds
[2025-07-18T10:33:05.002+0000] {processor.py:186} INFO - Started process (PID=1971) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:33:05.003+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:33:05.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.005+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:33:05.186+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.186+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:05.195+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:33:05.286+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.286+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:05.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.296+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:33:05.314+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.318 seconds
[2025-07-18T10:33:35.582+0000] {processor.py:186} INFO - Started process (PID=2102) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:33:35.583+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:33:35.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.585+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:33:35.776+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.776+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:35.785+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:33:35.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.884+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:35.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.894+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:33:35.914+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.338 seconds
[2025-07-18T10:34:06.279+0000] {processor.py:186} INFO - Started process (PID=2233) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:34:06.280+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:34:06.282+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.281+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:34:06.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.476+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:06.485+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:34:06.576+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.576+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:06.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:06.585+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:34:06.601+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.328 seconds
[2025-07-18T10:34:37.298+0000] {processor.py:186} INFO - Started process (PID=2364) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:34:37.299+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:34:37.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.300+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:34:37.504+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.504+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:37.511+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:34:37.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.612+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:37.622+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:37.622+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:34:37.639+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.347 seconds
[2025-07-18T10:35:07.831+0000] {processor.py:186} INFO - Started process (PID=2495) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:35:07.832+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:35:07.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.835+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:35:08.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.034+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:08.042+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:35:08.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.139+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:08.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.150+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:35:08.170+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.345 seconds
[2025-07-18T10:35:38.552+0000] {processor.py:186} INFO - Started process (PID=2624) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:35:38.552+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:35:38.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.554+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:35:38.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.756+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:38.765+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:35:38.868+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.867+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:38.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.878+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:35:38.899+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.353 seconds
[2025-07-18T10:36:09.656+0000] {processor.py:186} INFO - Started process (PID=2757) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:36:09.657+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:36:09.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.659+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:36:09.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.845+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:09.853+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:36:09.948+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.948+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:09.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.958+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:36:09.976+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.326 seconds
[2025-07-18T10:36:40.267+0000] {processor.py:186} INFO - Started process (PID=2888) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:36:40.268+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:36:40.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.270+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:36:40.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.473+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:40.483+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:36:40.584+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.583+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:40.594+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:40.594+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:36:40.613+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.351 seconds
[2025-07-18T10:37:10.810+0000] {processor.py:186} INFO - Started process (PID=3019) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:37:10.810+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:37:10.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.813+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:37:11.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.010+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:11.019+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:37:11.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.116+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:11.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.129+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:37:11.149+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.345 seconds
[2025-07-18T10:37:41.989+0000] {processor.py:186} INFO - Started process (PID=3150) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:37:41.990+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:37:41.992+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.992+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:37:42.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:42.176+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:42.186+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:37:42.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:42.278+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:42.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:42.289+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:37:42.308+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.325 seconds
[2025-07-18T10:38:12.525+0000] {processor.py:186} INFO - Started process (PID=3281) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:38:12.526+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:38:12.528+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.527+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:38:12.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.725+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:12.732+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:38:12.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.829+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:12.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.838+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:38:12.856+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.337 seconds
[2025-07-18T10:38:43.365+0000] {processor.py:186} INFO - Started process (PID=3412) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:38:43.366+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:38:43.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.367+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:38:43.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.551+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:43.560+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:38:43.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.651+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:43.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:43.660+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:38:43.680+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.320 seconds
[2025-07-18T10:39:14.188+0000] {processor.py:186} INFO - Started process (PID=3548) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:39:14.190+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:39:14.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:14.193+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:39:14.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:14.447+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:14.456+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:39:14.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:14.561+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:14.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:14.573+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:39:14.593+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.414 seconds
[2025-07-18T10:39:44.893+0000] {processor.py:186} INFO - Started process (PID=3679) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:39:44.894+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:39:44.896+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.896+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:39:45.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.088+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:45.096+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:39:45.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.201+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:45.211+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.210+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:39:45.227+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.341 seconds
[2025-07-18T10:40:16.020+0000] {processor.py:186} INFO - Started process (PID=3810) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:40:16.021+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:40:16.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.024+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:40:16.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.246+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:16.254+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:40:16.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.365+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:16.377+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.377+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:40:16.400+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.386 seconds
[2025-07-18T10:40:46.667+0000] {processor.py:186} INFO - Started process (PID=3936) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:40:46.668+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:40:46.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.671+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:40:46.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.881+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:46.890+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:40:46.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.977+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:46.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.987+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:40:47.005+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.346 seconds
[2025-07-18T10:41:17.135+0000] {processor.py:186} INFO - Started process (PID=4065) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:41:17.136+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:41:17.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.138+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:41:17.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.352+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:17.362+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:41:17.470+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.469+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:17.482+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.482+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:41:17.506+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.377 seconds
[2025-07-18T10:41:47.682+0000] {processor.py:186} INFO - Started process (PID=4201) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:41:47.683+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:41:47.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.684+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:41:47.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.874+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:47.882+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:41:47.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.973+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:47.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.984+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:41:48.005+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.329 seconds
[2025-07-18T10:42:18.545+0000] {processor.py:186} INFO - Started process (PID=4339) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:42:18.545+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:42:18.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.547+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:42:18.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.769+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:18.777+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:42:18.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.877+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:18.888+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.887+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:42:18.907+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.368 seconds
[2025-07-18T10:43:00.481+0000] {processor.py:186} INFO - Started process (PID=271) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:43:00.482+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:43:00.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.484+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:43:00.804+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.804+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:00.810+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:43:00.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.894+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:00.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.904+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:43:00.921+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.446 seconds
[2025-07-18T10:43:32.890+0000] {processor.py:186} INFO - Started process (PID=407) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:43:32.891+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:43:32.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.893+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:43:33.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.250+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:33.257+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:43:33.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.352+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:33.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.361+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:43:33.380+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.496 seconds
[2025-07-18T10:44:03.916+0000] {processor.py:186} INFO - Started process (PID=545) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:44:03.917+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:44:03.919+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:03.919+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:44:04.199+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:04.198+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:04.206+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:44:04.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:04.310+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:04.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:04.322+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:44:04.344+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.434 seconds
[2025-07-18T10:44:34.630+0000] {processor.py:186} INFO - Started process (PID=681) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:44:34.631+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:44:34.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.633+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:44:34.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.830+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:34.836+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:44:34.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.930+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:34.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.941+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:44:34.959+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.334 seconds
[2025-07-18T10:45:05.542+0000] {processor.py:186} INFO - Started process (PID=817) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:45:05.543+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:45:05.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.546+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:45:05.792+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.791+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:05.806+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:45:05.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.923+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:05.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.934+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:45:05.955+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.419 seconds
[2025-07-18T10:45:36.139+0000] {processor.py:186} INFO - Started process (PID=953) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:45:36.139+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:45:36.143+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.142+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:45:36.343+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.343+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:36.353+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:45:36.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.457+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:36.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.468+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:45:36.486+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.354 seconds
[2025-07-18T10:46:06.812+0000] {processor.py:186} INFO - Started process (PID=1087) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:46:06.814+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:46:06.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:06.818+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:46:07.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.068+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:07.076+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:46:07.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.180+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:07.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.191+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:46:07.211+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.408 seconds
[2025-07-18T10:46:37.736+0000] {processor.py:186} INFO - Started process (PID=1223) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:46:37.737+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:46:37.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:46:37.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.957+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:37.969+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:46:38.081+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:38.080+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:38.093+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:38.093+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:46:38.116+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.387 seconds
[2025-07-18T10:48:05.967+0000] {processor.py:186} INFO - Started process (PID=271) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:48:05.968+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:48:05.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.970+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:48:06.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.308+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:06.315+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:48:06.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.410+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:06.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.421+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:48:06.439+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.478 seconds
[2025-07-18T10:48:37.025+0000] {processor.py:186} INFO - Started process (PID=407) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:48:37.026+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:48:37.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.028+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:48:37.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.382+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:37.389+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:48:37.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.474+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:37.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.484+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:48:37.502+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.483 seconds
[2025-07-18T10:49:08.382+0000] {processor.py:186} INFO - Started process (PID=543) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:49:08.383+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:49:08.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.386+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:49:08.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.803+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:08.811+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:49:08.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.913+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:08.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.923+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:49:08.943+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.567 seconds
[2025-07-18T10:49:39.136+0000] {processor.py:186} INFO - Started process (PID=681) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:49:39.136+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:49:39.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.138+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:49:39.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.334+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:39.344+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:49:39.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.439+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:39.450+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.450+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:49:39.468+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.338 seconds
[2025-07-18T10:50:10.212+0000] {processor.py:186} INFO - Started process (PID=817) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:50:10.213+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:50:10.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.215+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:50:10.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.427+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:10.438+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:50:10.539+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.538+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:10.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.551+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:50:10.575+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.368 seconds
[2025-07-18T10:50:41.007+0000] {processor.py:186} INFO - Started process (PID=953) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:50:41.008+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:50:41.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.010+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:50:41.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.230+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:41.240+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:50:41.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:41.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.359+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:50:41.380+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.379 seconds
[2025-07-18T10:51:11.743+0000] {processor.py:186} INFO - Started process (PID=1089) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:51:11.744+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:51:11.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:11.746+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:51:11.945+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:11.945+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:11.953+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:51:12.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.053+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:12.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.064+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:51:12.084+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.346 seconds
[2025-07-18T10:51:42.159+0000] {processor.py:186} INFO - Started process (PID=1226) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:51:42.159+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:51:42.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.161+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:51:42.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.356+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:42.364+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:51:42.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.458+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:42.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.468+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:51:42.486+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.333 seconds
[2025-07-18T10:52:12.600+0000] {processor.py:186} INFO - Started process (PID=1362) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:52:12.601+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:52:12.604+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.603+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:52:12.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.810+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:12.820+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:52:12.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.922+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:12.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.933+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:52:12.953+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.358 seconds
[2025-07-18T10:52:43.284+0000] {processor.py:186} INFO - Started process (PID=1498) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:52:43.285+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:52:43.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.287+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:52:43.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.493+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:43.501+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:52:43.600+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.599+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:43.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.611+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:52:43.632+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.355 seconds
[2025-07-18T10:53:13.820+0000] {processor.py:186} INFO - Started process (PID=1634) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:53:13.821+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:53:13.823+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.823+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:53:14.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:14.025+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:14.035+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:53:14.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:14.135+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:14.147+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:14.147+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:53:14.170+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.357 seconds
[2025-07-18T10:53:44.284+0000] {processor.py:186} INFO - Started process (PID=1768) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:53:44.285+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:53:44.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.286+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:53:44.473+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.473+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:44.482+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:53:44.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.574+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:44.584+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.584+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:53:44.600+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.322 seconds
[2025-07-18T10:54:15.455+0000] {processor.py:186} INFO - Started process (PID=1906) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:54:15.457+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:54:15.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:15.459+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:54:15.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:15.676+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:15.685+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:54:15.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:15.788+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:15.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:15.799+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:54:15.819+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.370 seconds
[2025-07-18T10:54:45.943+0000] {processor.py:186} INFO - Started process (PID=2042) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:54:45.944+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:54:45.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.946+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:54:46.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:46.139+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:46.147+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:54:46.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:46.241+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:46.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:46.251+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:54:46.269+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.332 seconds
[2025-07-18T10:55:16.339+0000] {processor.py:186} INFO - Started process (PID=2178) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:55:16.340+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:55:16.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:16.342+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:55:16.528+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:16.528+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:16.536+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:55:16.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:16.621+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:16.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:16.630+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:55:16.650+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.316 seconds
[2025-07-18T10:57:30.544+0000] {processor.py:186} INFO - Started process (PID=271) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:57:30.545+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:57:30.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.547+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:57:30.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.874+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:30.882+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:57:30.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.983+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:30.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.994+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:57:31.011+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.473 seconds
[2025-07-18T10:58:02.207+0000] {processor.py:186} INFO - Started process (PID=407) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:58:02.208+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:58:02.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.210+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:58:02.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.516+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:02.524+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:58:02.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.611+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:02.622+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.622+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:58:02.640+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.438 seconds
[2025-07-18T10:58:33.818+0000] {processor.py:186} INFO - Started process (PID=545) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:58:33.819+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:58:33.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:33.821+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:58:34.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.129+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:34.136+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:58:34.222+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.221+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:34.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.230+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:58:34.246+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.433 seconds
[2025-07-18T10:59:04.519+0000] {processor.py:186} INFO - Started process (PID=681) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:59:04.520+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:59:04.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.521+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:59:04.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.747+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:04.755+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:59:04.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.864+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:04.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.874+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:59:04.893+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.380 seconds
[2025-07-18T10:59:34.997+0000] {processor.py:186} INFO - Started process (PID=817) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:59:34.998+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T10:59:35.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.000+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:59:35.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.187+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:35.197+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T10:59:35.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.292+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:35.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.303+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T10:59:35.331+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.339 seconds
[2025-07-18T11:00:05.484+0000] {processor.py:186} INFO - Started process (PID=953) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:00:05.485+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:00:05.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.487+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:00:05.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.682+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:05.691+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:00:05.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.788+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:05.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.800+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:00:05.822+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.343 seconds
[2025-07-18T11:00:36.175+0000] {processor.py:186} INFO - Started process (PID=1087) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:00:36.176+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:00:36.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.178+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:00:36.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.368+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:36.377+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:00:36.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.474+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:36.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.484+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:00:36.500+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.331 seconds
[2025-07-18T11:01:07.017+0000] {processor.py:186} INFO - Started process (PID=1225) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:01:07.018+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:01:07.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.020+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:01:07.207+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.206+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:07.216+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:01:07.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.319+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:07.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.329+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:01:07.349+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.337 seconds
[2025-07-18T11:01:38.042+0000] {processor.py:186} INFO - Started process (PID=1366) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:01:38.042+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:01:38.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.044+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:01:38.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.233+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:38.242+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:01:38.343+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.342+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:38.354+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.354+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:01:38.375+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.339 seconds
[2025-07-18T11:02:09.347+0000] {processor.py:186} INFO - Started process (PID=1495) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:02:09.348+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:02:09.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.349+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:02:09.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.530+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:09.538+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:02:09.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.624+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:09.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.635+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:02:09.654+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.313 seconds
[2025-07-18T11:02:40.002+0000] {processor.py:186} INFO - Started process (PID=1631) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:02:40.003+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:02:40.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.005+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:02:40.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.210+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:40.217+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:02:40.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.326+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:40.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.341+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:02:40.361+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.365 seconds
[2025-07-18T11:03:10.620+0000] {processor.py:186} INFO - Started process (PID=1767) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:03:10.621+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:03:10.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.623+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:03:10.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.844+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:10.852+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:03:10.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.954+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:10.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.967+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:03:10.990+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.376 seconds
[2025-07-18T11:03:41.676+0000] {processor.py:186} INFO - Started process (PID=1903) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:03:41.677+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:03:41.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.679+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:03:41.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.889+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:41.899+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:03:42.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.008+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:42.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.019+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:03:42.038+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.368 seconds
[2025-07-18T11:04:12.532+0000] {processor.py:186} INFO - Started process (PID=2039) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:04:12.532+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:04:12.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:12.535+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:04:12.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:12.762+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:12.773+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:04:12.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:12.877+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:12.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:12.889+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:04:12.913+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.388 seconds
[2025-07-18T11:04:43.130+0000] {processor.py:186} INFO - Started process (PID=2175) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:04:43.132+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:04:43.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.134+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:04:43.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.341+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:43.349+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:04:43.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.455+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:43.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.465+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:04:43.488+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.365 seconds
[2025-07-18T11:05:13.892+0000] {processor.py:186} INFO - Started process (PID=2311) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:05:13.893+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:05:13.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.894+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:05:14.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:14.124+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:14.135+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:05:14.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:14.253+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:14.267+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:14.267+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:05:14.294+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.410 seconds
[2025-07-18T11:06:53.240+0000] {processor.py:186} INFO - Started process (PID=283) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:06:53.242+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:06:53.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:53.244+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:06:53.602+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:53.601+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:53.607+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:06:53.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:53.706+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:53.717+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:53.716+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:06:53.734+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.501 seconds
[2025-07-18T11:07:24.489+0000] {processor.py:186} INFO - Started process (PID=424) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:07:24.490+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:07:24.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:24.492+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:07:24.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:24.893+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:24.901+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:07:25.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.040+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:25.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.053+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:07:25.071+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.587 seconds
[2025-07-18T11:07:55.680+0000] {processor.py:186} INFO - Started process (PID=565) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:07:55.681+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:07:55.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.684+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:07:55.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.890+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:55.898+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:07:56.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.002+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:56.013+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.012+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:07:56.029+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.355 seconds
[2025-07-18T11:08:26.613+0000] {processor.py:186} INFO - Started process (PID=706) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:08:26.614+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:08:26.617+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.616+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:08:26.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.820+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:26.829+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:08:26.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.926+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:26.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.935+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:08:26.949+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.343 seconds
[2025-07-18T11:08:57.433+0000] {processor.py:186} INFO - Started process (PID=847) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:08:57.434+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:08:57.436+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.436+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:08:57.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.651+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:57.660+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:08:57.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.771+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:57.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.785+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:08:57.806+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.379 seconds
[2025-07-18T11:09:28.446+0000] {processor.py:186} INFO - Started process (PID=988) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:09:28.447+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:09:28.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.449+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:09:28.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.651+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:28.660+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:09:28.764+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.763+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:28.776+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.776+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:09:28.810+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.371 seconds
[2025-07-18T11:09:59.461+0000] {processor.py:186} INFO - Started process (PID=1131) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:09:59.462+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:09:59.465+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:59.464+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:09:59.666+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:59.666+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:59.675+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:09:59.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:59.802+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:59.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:59.811+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:09:59.830+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.376 seconds
[2025-07-18T11:10:30.068+0000] {processor.py:186} INFO - Started process (PID=1272) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:10:30.069+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:10:30.071+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:30.071+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:10:30.274+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:30.274+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:30.281+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:10:30.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:30.374+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:30.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:30.387+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:10:30.407+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.345 seconds
[2025-07-18T11:11:00.751+0000] {processor.py:186} INFO - Started process (PID=1413) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:11:00.752+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:11:00.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.754+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:11:00.938+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.937+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:00.947+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:11:01.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.043+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:01.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.052+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:11:01.069+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.324 seconds
[2025-07-18T11:11:31.554+0000] {processor.py:186} INFO - Started process (PID=1552) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:11:31.555+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:11:31.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.558+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:11:31.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.754+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:31.763+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:11:31.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.872+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:31.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.884+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:11:31.902+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.355 seconds
[2025-07-18T11:12:02.352+0000] {processor.py:186} INFO - Started process (PID=1695) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:12:02.353+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:12:02.355+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:02.355+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:12:02.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:02.553+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:02.562+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:12:02.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:02.649+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:02.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:02.661+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:12:02.679+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.333 seconds
[2025-07-18T11:12:32.855+0000] {processor.py:186} INFO - Started process (PID=1834) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:12:32.856+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:12:32.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.859+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:12:33.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.051+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:33.060+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:12:33.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.156+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:33.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.169+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:12:33.187+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.338 seconds
[2025-07-18T11:13:05.010+0000] {processor.py:186} INFO - Started process (PID=1977) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:13:05.011+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:13:05.013+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.013+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:13:05.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.208+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:05.217+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:13:05.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.321+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:05.331+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.331+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:13:05.349+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.344 seconds
[2025-07-18T11:13:35.489+0000] {processor.py:186} INFO - Started process (PID=2116) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:13:35.490+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:13:35.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.492+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:13:35.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.672+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:35.681+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:13:35.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.787+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:35.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.802+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:13:35.826+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.343 seconds
[2025-07-18T11:14:06.602+0000] {processor.py:186} INFO - Started process (PID=2259) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:14:06.603+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:14:06.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:06.606+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:14:06.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:06.837+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:06.848+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:14:06.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:06.958+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:06.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:06.971+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:14:06.995+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.400 seconds
[2025-07-18T11:14:37.213+0000] {processor.py:186} INFO - Started process (PID=2400) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:14:37.214+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:14:37.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.216+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:14:37.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.416+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:37.424+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:14:37.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.527+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:37.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:37.537+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:14:37.557+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.350 seconds
[2025-07-18T11:15:07.843+0000] {processor.py:186} INFO - Started process (PID=2541) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:15:07.845+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:15:07.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:07.847+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:15:08.055+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.055+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:08.063+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:15:08.167+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.166+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:08.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.180+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:15:08.202+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.367 seconds
[2025-07-18T11:15:38.824+0000] {processor.py:186} INFO - Started process (PID=2682) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:15:38.824+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:15:38.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.826+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:15:39.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.045+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:39.055+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:15:39.167+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.166+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:39.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.180+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:15:39.203+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.385 seconds
[2025-07-18T11:16:09.779+0000] {processor.py:186} INFO - Started process (PID=2823) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:16:09.780+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:16:09.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:09.783+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:16:10.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.026+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:10.034+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:16:10.136+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.136+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:10.147+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.147+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:16:10.167+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.394 seconds
[2025-07-18T11:16:40.273+0000] {processor.py:186} INFO - Started process (PID=2964) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:16:40.274+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:16:40.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:40.276+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:16:40.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:40.498+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:40.510+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:16:40.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:40.619+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:40.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:40.631+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:16:40.654+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.386 seconds
[2025-07-18T11:17:11.458+0000] {processor.py:186} INFO - Started process (PID=3103) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:17:11.459+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:17:11.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:11.461+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:17:11.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:11.702+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:11.713+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:17:11.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:11.827+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:11.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:11.839+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:17:11.862+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.410 seconds
[2025-07-18T11:17:42.181+0000] {processor.py:186} INFO - Started process (PID=3244) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:17:42.182+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:17:42.186+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.185+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:17:42.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.391+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:42.400+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:17:42.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.501+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:42.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.513+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:17:42.534+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.359 seconds
[2025-07-18T11:18:12.979+0000] {processor.py:186} INFO - Started process (PID=3385) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:18:12.981+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:18:12.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.984+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:18:13.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:13.238+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:13.248+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:18:13.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:13.362+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:13.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:13.375+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:18:13.395+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.424 seconds
[2025-07-18T11:18:43.563+0000] {processor.py:186} INFO - Started process (PID=3526) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:18:43.564+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:18:43.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.566+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:18:43.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.773+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:43.782+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:18:43.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.876+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:43.891+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.891+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:18:43.910+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.353 seconds
[2025-07-18T11:19:14.942+0000] {processor.py:186} INFO - Started process (PID=3667) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:19:14.944+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:19:14.949+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:14.949+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:19:15.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.268+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:15.276+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:19:15.413+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.412+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:15.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.427+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:19:15.450+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.519 seconds
[2025-07-18T11:19:45.630+0000] {processor.py:186} INFO - Started process (PID=3813) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:19:45.631+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:19:45.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.633+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:19:45.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.845+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:45.853+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:19:45.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.951+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:45.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.965+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:19:45.986+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.363 seconds
[2025-07-18T11:20:16.497+0000] {processor.py:186} INFO - Started process (PID=3954) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:20:16.498+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:20:16.502+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:16.501+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:20:16.723+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:16.723+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:16.731+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:20:16.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:16.837+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:16.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:16.851+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:20:16.878+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.391 seconds
