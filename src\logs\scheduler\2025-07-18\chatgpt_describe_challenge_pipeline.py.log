[2025-07-18T10:16:50.171+0000] {processor.py:186} INFO - Started process (PID=246) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:16:50.172+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:16:50.175+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.174+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:16:50.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.216+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:50.221+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:16:50.246+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.081 seconds
[2025-07-18T10:17:20.488+0000] {processor.py:186} INFO - Started process (PID=379) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:17:20.489+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:17:20.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.491+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:17:20.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.523+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.527+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:17:20.545+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.062 seconds
[2025-07-18T10:17:51.144+0000] {processor.py:186} INFO - Started process (PID=508) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:17:51.145+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:17:51.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.146+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:17:51.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.316+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:51.320+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:17:51.338+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.200 seconds
[2025-07-18T10:18:21.537+0000] {processor.py:186} INFO - Started process (PID=641) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:18:21.538+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:18:21.540+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.539+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:18:21.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.573+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.576+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:18:21.593+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.062 seconds
[2025-07-18T10:18:52.491+0000] {processor.py:186} INFO - Started process (PID=772) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:18:52.492+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:18:52.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.493+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:18:52.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.522+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:52.525+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:18:52.540+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.054 seconds
[2025-07-18T10:19:23.389+0000] {processor.py:186} INFO - Started process (PID=901) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:19:23.390+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:19:23.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.391+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:19:23.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.421+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:23.425+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:19:23.441+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.057 seconds
[2025-07-18T10:19:54.441+0000] {processor.py:186} INFO - Started process (PID=1034) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:19:54.442+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:19:54.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.443+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:19:54.482+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.477+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:54.483+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:19:54.501+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.066 seconds
[2025-07-18T10:20:25.449+0000] {processor.py:186} INFO - Started process (PID=1165) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:20:25.449+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:20:25.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.450+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:20:25.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.480+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:25.485+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:20:25.501+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.058 seconds
[2025-07-18T10:20:56.390+0000] {processor.py:186} INFO - Started process (PID=1296) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:20:56.391+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:20:56.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.392+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:20:56.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.424+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:56.428+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:20:56.443+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.059 seconds
[2025-07-18T10:21:27.382+0000] {processor.py:186} INFO - Started process (PID=1427) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:21:27.383+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:21:27.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:21:27.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.413+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:27.419+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:21:27.437+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.061 seconds
[2025-07-18T10:21:57.555+0000] {processor.py:186} INFO - Started process (PID=1558) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:21:57.556+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:21:57.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.557+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:21:57.593+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.590+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:57.594+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:21:57.608+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.059 seconds
[2025-07-18T10:22:28.601+0000] {processor.py:186} INFO - Started process (PID=1689) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:22:28.601+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:22:28.602+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.602+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:22:28.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.631+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:28.636+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:22:28.651+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.056 seconds
[2025-07-18T10:22:59.557+0000] {processor.py:186} INFO - Started process (PID=1820) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:22:59.557+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:22:59.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.559+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:22:59.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.590+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:59.594+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:22:59.611+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.060 seconds
[2025-07-18T10:23:30.425+0000] {processor.py:186} INFO - Started process (PID=1949) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:23:30.425+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:23:30.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.426+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:23:30.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.456+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:30.460+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:23:30.476+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.057 seconds
[2025-07-18T10:24:01.399+0000] {processor.py:186} INFO - Started process (PID=2082) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:24:01.400+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:24:01.402+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.402+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:24:01.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.445+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:01.450+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:24:01.465+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.075 seconds
[2025-07-18T10:24:31.644+0000] {processor.py:186} INFO - Started process (PID=2213) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:24:31.645+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:24:31.646+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.646+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:24:31.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.677+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.681+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:24:31.699+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.061 seconds
[2025-07-18T10:25:04.981+0000] {processor.py:186} INFO - Started process (PID=2344) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:25:04.982+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:25:04.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.983+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:25:05.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.169+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:05.176+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:25:05.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.261+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:05.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.271+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:25:05.293+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.317 seconds
[2025-07-18T10:26:22.618+0000] {processor.py:186} INFO - Started process (PID=246) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:26:22.619+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:26:22.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:22.621+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:26:22.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:22.988+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:22.995+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:26:23.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.094+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:23.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.105+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:26:23.124+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.512 seconds
[2025-07-18T10:26:55.031+0000] {processor.py:186} INFO - Started process (PID=377) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:26:55.032+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:26:55.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.034+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:26:55.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.362+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:55.369+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:26:55.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.466+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:55.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.478+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:26:55.498+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.473 seconds
[2025-07-18T10:27:26.222+0000] {processor.py:186} INFO - Started process (PID=508) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:27:26.223+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:27:26.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.226+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:27:26.622+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.622+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:26.631+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:27:26.729+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.728+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:26.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.738+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:27:26.759+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.543 seconds
[2025-07-18T10:27:56.992+0000] {processor.py:186} INFO - Started process (PID=639) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:27:56.993+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:27:56.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.994+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:27:57.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.192+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:57.202+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:27:57.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:57.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.311+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:27:57.332+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.346 seconds
[2025-07-18T10:28:28.090+0000] {processor.py:186} INFO - Started process (PID=772) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:28:28.091+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:28:28.093+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.093+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:28:28.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.310+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:28.320+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:28:28.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.417+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:28.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.429+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:28:28.450+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.368 seconds
[2025-07-18T10:28:58.943+0000] {processor.py:186} INFO - Started process (PID=903) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:28:58.944+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:28:58.948+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.947+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:28:59.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.161+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:59.170+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:28:59.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.269+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:59.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.280+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:28:59.300+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.369 seconds
[2025-07-18T10:29:29.600+0000] {processor.py:186} INFO - Started process (PID=1034) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:29:29.601+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:29:29.604+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.603+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:29:29.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.805+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:29.816+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:29:29.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:29.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.944+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:29:29.960+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.366 seconds
[2025-07-18T10:30:00.014+0000] {processor.py:186} INFO - Started process (PID=1165) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:30:00.015+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:30:00.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:00.017+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:30:00.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:00.205+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:00.213+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:30:00.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:00.312+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:00.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:00.322+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:30:00.344+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.336 seconds
[2025-07-18T10:30:30.924+0000] {processor.py:186} INFO - Started process (PID=1294) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:30:30.925+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:30:30.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:30.927+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:30:31.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.109+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:31.118+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:30:31.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.210+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:31.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.220+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:30:31.240+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.321 seconds
[2025-07-18T10:31:01.477+0000] {processor.py:186} INFO - Started process (PID=1425) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:31:01.478+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:31:01.480+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:01.480+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:31:01.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:01.679+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:01.689+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:31:01.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:01.782+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:01.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:01.794+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:31:01.814+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.342 seconds
[2025-07-18T10:31:31.995+0000] {processor.py:186} INFO - Started process (PID=1556) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:31:31.996+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:31:31.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:31.998+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:31:32.172+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.172+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:32.181+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:31:32.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.278+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:32.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.291+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:31:32.308+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.320 seconds
[2025-07-18T10:32:03.014+0000] {processor.py:186} INFO - Started process (PID=1689) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:32:03.015+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:32:03.018+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.017+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:32:03.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.196+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:03.205+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:32:03.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.295+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:03.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.306+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:32:03.323+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.314 seconds
[2025-07-18T10:32:33.430+0000] {processor.py:186} INFO - Started process (PID=1820) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:32:33.431+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:32:33.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.434+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:32:33.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.620+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:33.628+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:32:33.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.725+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:33.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.736+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:32:33.756+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.332 seconds
[2025-07-18T10:33:03.920+0000] {processor.py:186} INFO - Started process (PID=1951) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:33:03.920+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:33:03.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:03.922+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:33:04.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.097+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:04.105+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:33:04.190+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.189+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:04.199+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.199+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:33:04.217+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.302 seconds
[2025-07-18T10:33:34.521+0000] {processor.py:186} INFO - Started process (PID=2082) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:33:34.522+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:33:34.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.525+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:33:34.702+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.702+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:34.711+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:33:34.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.801+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:34.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.810+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:33:34.828+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.313 seconds
[2025-07-18T10:34:05.131+0000] {processor.py:186} INFO - Started process (PID=2213) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:34:05.132+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:34:05.134+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.134+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:34:05.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.324+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:05.332+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:34:05.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.427+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:05.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.437+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:34:05.455+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.331 seconds
[2025-07-18T10:34:36.202+0000] {processor.py:186} INFO - Started process (PID=2344) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:34:36.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:34:36.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.205+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:34:36.403+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.403+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:36.412+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:34:36.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.515+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:36.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.524+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:34:36.541+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.345 seconds
[2025-07-18T10:35:06.678+0000] {processor.py:186} INFO - Started process (PID=2475) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:35:06.679+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:35:06.681+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:06.681+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:35:06.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:06.856+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:06.865+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:35:06.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:06.957+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:06.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:06.967+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:35:06.987+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.315 seconds
[2025-07-18T10:35:37.728+0000] {processor.py:186} INFO - Started process (PID=2606) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:35:37.729+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:35:37.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:37.731+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:35:37.954+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:37.954+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:37.963+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:35:38.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.057+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:38.067+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.067+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:35:38.084+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.363 seconds
[2025-07-18T10:36:08.486+0000] {processor.py:186} INFO - Started process (PID=2737) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:36:08.487+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:36:08.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.488+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:36:08.692+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.692+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:08.701+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:36:08.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.800+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:08.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.810+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:36:08.829+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.350 seconds
[2025-07-18T10:36:39.116+0000] {processor.py:186} INFO - Started process (PID=2868) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:36:39.117+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:36:39.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.121+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:36:39.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.310+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:39.318+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:36:39.403+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.403+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:39.413+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.412+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:36:39.432+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.322 seconds
[2025-07-18T10:37:09.957+0000] {processor.py:186} INFO - Started process (PID=2999) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:37:09.958+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:37:09.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:09.960+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:37:10.186+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.186+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:10.193+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:37:10.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.292+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:10.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.305+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:37:10.324+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.372 seconds
[2025-07-18T10:37:40.848+0000] {processor.py:186} INFO - Started process (PID=3130) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:37:40.849+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:37:40.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:40.852+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:37:41.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.064+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:41.074+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:37:41.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.169+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:41.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.180+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:37:41.196+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.358 seconds
[2025-07-18T10:38:11.328+0000] {processor.py:186} INFO - Started process (PID=3261) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:38:11.329+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:38:11.331+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:11.331+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:38:11.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:11.533+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:11.542+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:38:11.640+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:11.639+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:11.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:11.649+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:38:11.669+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.347 seconds
[2025-07-18T10:38:42.284+0000] {processor.py:186} INFO - Started process (PID=3392) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:38:42.286+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:38:42.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.288+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:38:42.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.468+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:42.476+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:38:42.571+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.570+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:42.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.579+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:38:42.596+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.317 seconds
[2025-07-18T10:39:12.935+0000] {processor.py:186} INFO - Started process (PID=3523) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:39:12.936+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:39:12.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:12.939+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:39:13.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.132+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:13.142+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:39:13.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.268+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:13.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.279+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:39:13.297+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.368 seconds
[2025-07-18T10:39:43.663+0000] {processor.py:186} INFO - Started process (PID=3654) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:39:43.664+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:39:43.666+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:43.666+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:39:43.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:43.853+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:43.863+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:39:43.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:43.960+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:43.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:43.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:39:43.991+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.334 seconds
[2025-07-18T10:40:14.385+0000] {processor.py:186} INFO - Started process (PID=3783) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:40:14.386+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:40:14.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.388+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:40:14.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.579+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:14.588+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:40:14.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.676+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:14.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.685+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:40:14.701+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.322 seconds
[2025-07-18T10:40:45.566+0000] {processor.py:186} INFO - Started process (PID=3916) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:40:45.567+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:40:45.569+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:45.569+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:40:45.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:45.750+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:45.759+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:40:45.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:45.843+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:45.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:45.852+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:40:45.869+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.309 seconds
[2025-07-18T10:41:16.309+0000] {processor.py:186} INFO - Started process (PID=4047) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:41:16.311+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:41:16.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:16.314+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:41:16.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:16.524+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:16.532+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:41:16.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:16.632+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:16.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:16.642+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:41:16.658+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.359 seconds
[2025-07-18T10:41:46.896+0000] {processor.py:186} INFO - Started process (PID=4183) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:41:46.897+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:41:46.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:46.899+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:41:47.123+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.123+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:47.135+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:41:47.235+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.235+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:47.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.246+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:41:47.263+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.373 seconds
[2025-07-18T10:42:17.375+0000] {processor.py:186} INFO - Started process (PID=4317) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:42:17.377+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:42:17.380+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:17.380+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:42:17.602+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:17.601+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:17.611+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:42:17.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:17.713+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:17.723+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:17.723+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:42:17.740+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.373 seconds
[2025-07-18T10:42:59.516+0000] {processor.py:186} INFO - Started process (PID=251) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:42:59.517+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:42:59.519+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.518+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:42:59.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.827+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:59.835+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:42:59.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.919+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:59.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.929+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:42:59.946+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.436 seconds
[2025-07-18T10:43:31.901+0000] {processor.py:186} INFO - Started process (PID=389) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:43:31.902+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:43:31.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:31.904+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:43:32.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.202+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:32.210+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:43:32.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.295+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:32.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.303+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:43:32.319+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.423 seconds
[2025-07-18T10:44:02.476+0000] {processor.py:186} INFO - Started process (PID=525) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:44:02.476+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:44:02.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:02.478+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:44:02.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:02.816+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:02.823+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:44:02.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:02.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:02.921+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:02.920+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:44:02.938+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.467 seconds
[2025-07-18T10:44:33.824+0000] {processor.py:186} INFO - Started process (PID=660) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:44:33.824+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:44:33.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:33.826+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:44:34.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.034+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:34.043+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:44:34.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.135+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:34.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.146+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:44:34.164+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.346 seconds
[2025-07-18T10:45:04.345+0000] {processor.py:186} INFO - Started process (PID=797) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:45:04.346+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:45:04.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:04.351+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:45:04.566+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:04.565+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:04.575+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:45:04.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:04.674+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:04.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:04.685+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:45:04.705+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.369 seconds
[2025-07-18T10:45:34.949+0000] {processor.py:186} INFO - Started process (PID=933) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:45:34.950+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:45:34.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:34.952+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:45:35.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:35.159+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:35.168+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:45:35.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:35.270+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:35.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:35.281+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:45:35.300+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.357 seconds
[2025-07-18T10:46:05.871+0000] {processor.py:186} INFO - Started process (PID=1069) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:46:05.873+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:46:05.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:05.876+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:46:06.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:06.079+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:06.088+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:46:06.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:06.197+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:06.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:06.208+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:46:06.243+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.381 seconds
[2025-07-18T10:46:36.831+0000] {processor.py:186} INFO - Started process (PID=1205) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:46:36.832+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:46:36.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:36.834+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:46:37.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.052+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:37.062+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:46:37.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.178+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:37.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.191+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:46:37.213+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.388 seconds
[2025-07-18T10:47:07.293+0000] {processor.py:186} INFO - Started process (PID=1341) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:47:07.295+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:47:07.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.297+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:47:07.520+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.520+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:07.530+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:47:07.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.644+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:07.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.656+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:47:07.678+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.391 seconds
[2025-07-18T10:48:04.929+0000] {processor.py:186} INFO - Started process (PID=251) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:48:04.930+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:48:04.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.932+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:48:05.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.270+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:05.280+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:48:05.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.374+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:05.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.384+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:48:05.403+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.480 seconds
[2025-07-18T10:48:35.977+0000] {processor.py:186} INFO - Started process (PID=387) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:48:35.978+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:48:35.981+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.981+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:48:36.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.311+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:36.317+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:48:36.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.410+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:36.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.421+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:48:36.443+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.473 seconds
[2025-07-18T10:49:07.110+0000] {processor.py:186} INFO - Started process (PID=525) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:49:07.111+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:49:07.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:07.113+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:49:07.481+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:07.481+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:07.487+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:49:07.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:07.599+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:07.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:07.611+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:49:07.635+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.531 seconds
[2025-07-18T10:49:38.338+0000] {processor.py:186} INFO - Started process (PID=661) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:49:38.339+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:49:38.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.341+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:49:38.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.531+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:38.539+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:49:38.638+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.638+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:38.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.649+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:49:38.669+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.336 seconds
[2025-07-18T10:50:09.049+0000] {processor.py:186} INFO - Started process (PID=797) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:50:09.050+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:50:09.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:09.052+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:50:09.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:09.254+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:09.263+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:50:09.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:09.362+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:09.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:09.372+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:50:09.393+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.350 seconds
[2025-07-18T10:50:39.782+0000] {processor.py:186} INFO - Started process (PID=933) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:50:39.783+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:50:39.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.784+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:50:39.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.987+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:39.997+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:50:40.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:40.103+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:40.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:40.117+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:50:40.138+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.362 seconds
[2025-07-18T10:51:10.499+0000] {processor.py:186} INFO - Started process (PID=1069) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:51:10.500+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:51:10.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.502+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:51:10.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.751+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:10.760+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:51:10.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.871+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:10.883+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.883+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:51:10.907+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.414 seconds
[2025-07-18T10:51:41.004+0000] {processor.py:186} INFO - Started process (PID=1206) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:51:41.005+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:51:41.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.007+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:51:41.209+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.209+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:41.221+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:51:41.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.327+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:41.339+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.338+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:51:41.360+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.361 seconds
[2025-07-18T10:52:11.815+0000] {processor.py:186} INFO - Started process (PID=1347) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:52:11.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:52:11.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:11.818+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:52:12.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.025+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:12.036+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:52:12.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.134+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:12.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.146+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:52:12.169+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.360 seconds
[2025-07-18T10:52:42.493+0000] {processor.py:186} INFO - Started process (PID=1483) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:52:42.494+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:52:42.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.497+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:52:42.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.687+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:42.696+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:52:42.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.800+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:42.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.814+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:52:42.835+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.348 seconds
[2025-07-18T10:53:13.015+0000] {processor.py:186} INFO - Started process (PID=1619) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:53:13.016+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:53:13.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.018+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:53:13.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.218+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:13.229+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:53:13.336+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.336+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:13.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.349+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:53:13.371+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.361 seconds
[2025-07-18T10:53:43.576+0000] {processor.py:186} INFO - Started process (PID=1755) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:53:43.577+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:53:43.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.579+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:53:43.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.761+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:43.770+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:53:43.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.862+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:43.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.873+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:53:43.890+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.319 seconds
[2025-07-18T10:54:14.196+0000] {processor.py:186} INFO - Started process (PID=1884) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:54:14.197+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:54:14.200+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.199+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:54:14.401+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.401+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:14.410+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:54:14.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.515+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:14.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.526+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:54:14.549+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.358 seconds
[2025-07-18T10:54:44.783+0000] {processor.py:186} INFO - Started process (PID=2020) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:54:44.784+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:54:44.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.786+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:54:44.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.982+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:44.992+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:54:45.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.086+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:45.098+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.097+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:54:45.117+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.340 seconds
[2025-07-18T10:55:15.276+0000] {processor.py:186} INFO - Started process (PID=2156) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:55:15.277+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:55:15.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:55:15.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.455+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:15.465+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:55:15.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.561+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:15.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.572+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:55:15.591+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.322 seconds
[2025-07-18T10:55:46.139+0000] {processor.py:186} INFO - Started process (PID=2292) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:55:46.140+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:55:46.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.146+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:55:46.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.374+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:46.385+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:55:46.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.491+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:46.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.508+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:55:46.529+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.397 seconds
[2025-07-18T10:57:29.577+0000] {processor.py:186} INFO - Started process (PID=251) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:57:29.578+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:57:29.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.580+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:57:29.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.898+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:29.905+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:57:29.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.989+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:29.999+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.999+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:57:30.018+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.447 seconds
[2025-07-18T10:58:01.231+0000] {processor.py:186} INFO - Started process (PID=387) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:58:01.232+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:58:01.235+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.235+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:58:01.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.564+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:01.571+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:58:01.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.662+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:01.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.672+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:58:01.690+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.464 seconds
[2025-07-18T10:58:32.362+0000] {processor.py:186} INFO - Started process (PID=525) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:58:32.363+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:58:32.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:32.364+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:58:32.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:32.671+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:32.682+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:58:32.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:32.769+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:32.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:32.778+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:58:32.795+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.439 seconds
[2025-07-18T10:59:03.017+0000] {processor.py:186} INFO - Started process (PID=661) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:59:03.018+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:59:03.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:03.020+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:59:03.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:03.203+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:03.211+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:59:03.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:03.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:03.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:03.308+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:59:03.326+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.315 seconds
[2025-07-18T10:59:33.880+0000] {processor.py:186} INFO - Started process (PID=797) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:59:33.881+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T10:59:33.883+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.883+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:59:34.069+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.068+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:34.079+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T10:59:34.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.175+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:34.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.187+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T10:59:34.207+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.332 seconds
[2025-07-18T11:00:04.324+0000] {processor.py:186} INFO - Started process (PID=931) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:00:04.325+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:00:04.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.327+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:00:04.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.536+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:04.546+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:00:04.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.664+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:04.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.675+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:00:04.694+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.376 seconds
[2025-07-18T11:00:35.425+0000] {processor.py:186} INFO - Started process (PID=1069) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:00:35.426+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:00:35.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:35.428+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:00:35.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:35.620+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:35.629+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:00:35.715+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:35.715+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:35.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:35.725+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:00:35.744+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.325 seconds
[2025-07-18T11:01:05.845+0000] {processor.py:186} INFO - Started process (PID=1205) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:01:05.846+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:01:05.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.848+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:01:06.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.052+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:06.060+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:01:06.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.155+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:06.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.165+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:01:06.184+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.345 seconds
[2025-07-18T11:01:37.223+0000] {processor.py:186} INFO - Started process (PID=1341) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:01:37.224+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:01:37.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.225+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:01:37.420+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.420+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:37.430+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:01:37.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.531+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:37.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.542+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:01:37.563+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.346 seconds
[2025-07-18T11:02:08.604+0000] {processor.py:186} INFO - Started process (PID=1477) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:02:08.605+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:02:08.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:08.607+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:02:08.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:08.817+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:08.826+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:02:08.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:08.920+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:08.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:08.932+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:02:08.952+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.353 seconds
[2025-07-18T11:02:39.238+0000] {processor.py:186} INFO - Started process (PID=1613) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:02:39.239+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:02:39.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:39.242+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:02:39.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:39.441+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:39.451+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:02:39.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:39.546+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:39.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:39.558+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:02:39.576+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.343 seconds
[2025-07-18T11:03:09.768+0000] {processor.py:186} INFO - Started process (PID=1749) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:03:09.768+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:03:09.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.770+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:03:09.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.978+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:09.986+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:03:10.093+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.092+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:10.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.105+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:03:10.125+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.364 seconds
[2025-07-18T11:03:40.837+0000] {processor.py:186} INFO - Started process (PID=1885) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:03:40.838+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:03:40.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:40.840+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:03:41.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.054+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:41.065+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:03:41.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.181+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:41.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.193+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:03:41.218+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.387 seconds
[2025-07-18T11:04:11.586+0000] {processor.py:186} INFO - Started process (PID=2021) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:04:11.588+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:04:11.590+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.590+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:04:11.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.847+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:11.857+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:04:11.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.975+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:11.989+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.989+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:04:12.014+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.435 seconds
[2025-07-18T11:04:42.285+0000] {processor.py:186} INFO - Started process (PID=2157) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:04:42.286+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:04:42.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:42.288+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:04:42.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:42.489+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:42.497+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:04:42.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:42.608+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:42.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:42.619+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:04:42.644+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.365 seconds
[2025-07-18T11:05:13.011+0000] {processor.py:186} INFO - Started process (PID=2293) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:05:13.012+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:05:13.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.014+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:05:13.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.226+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:13.236+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:05:13.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.349+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:13.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.361+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:05:13.383+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.379 seconds
[2025-07-18T11:06:52.140+0000] {processor.py:186} INFO - Started process (PID=257) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:06:52.141+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:06:52.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.144+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:06:52.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.525+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:52.533+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:06:52.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.634+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:52.647+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.647+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:06:52.673+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.541 seconds
[2025-07-18T11:07:23.475+0000] {processor.py:186} INFO - Started process (PID=404) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:07:23.476+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:07:23.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.477+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:07:23.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.842+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:23.850+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:07:23.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.941+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:23.953+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.952+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:07:23.970+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.501 seconds
[2025-07-18T11:07:54.841+0000] {processor.py:186} INFO - Started process (PID=545) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:07:54.842+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:07:54.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.844+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:07:55.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.087+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:55.101+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:07:55.207+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.206+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:55.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.220+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:07:55.239+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.404 seconds
[2025-07-18T11:08:25.750+0000] {processor.py:186} INFO - Started process (PID=686) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:08:25.752+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:08:25.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.754+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:08:26.023+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.023+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:26.030+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:08:26.143+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.143+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:26.154+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.154+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:08:26.172+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.428 seconds
[2025-07-18T11:08:56.251+0000] {processor.py:186} INFO - Started process (PID=827) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:08:56.252+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:08:56.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.254+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:08:56.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.467+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:56.475+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:08:56.584+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.584+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:56.597+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.597+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:08:56.627+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.382 seconds
[2025-07-18T11:09:27.640+0000] {processor.py:186} INFO - Started process (PID=970) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:09:27.641+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:09:27.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.644+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:09:27.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.851+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:27.861+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:09:27.963+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.963+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:27.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.975+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:09:27.996+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.363 seconds
[2025-07-18T11:09:58.117+0000] {processor.py:186} INFO - Started process (PID=1111) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:09:58.119+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:09:58.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.121+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:09:58.343+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.343+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:58.352+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:09:58.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.488+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:58.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.500+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:09:58.520+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.411 seconds
[2025-07-18T11:10:28.847+0000] {processor.py:186} INFO - Started process (PID=1252) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:10:28.848+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:10:28.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.850+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:10:29.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.036+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:29.046+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:10:29.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.142+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:29.152+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.152+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:10:29.171+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.329 seconds
[2025-07-18T11:10:59.588+0000] {processor.py:186} INFO - Started process (PID=1391) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:10:59.589+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:10:59.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.592+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:10:59.846+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.845+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:59.856+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:10:59.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.994+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:00.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.006+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:11:00.026+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.444 seconds
[2025-07-18T11:11:30.476+0000] {processor.py:186} INFO - Started process (PID=1532) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:11:30.477+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:11:30.480+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.479+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:11:30.666+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.666+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:30.674+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:11:30.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.761+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:30.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.771+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:11:30.790+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.319 seconds
[2025-07-18T11:12:01.223+0000] {processor.py:186} INFO - Started process (PID=1673) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:12:01.224+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:12:01.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.227+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:12:01.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.438+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:01.448+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:12:01.554+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.554+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:01.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.567+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:12:01.587+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.370 seconds
[2025-07-18T11:12:32.069+0000] {processor.py:186} INFO - Started process (PID=1816) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:12:32.070+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:12:32.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.072+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:12:32.258+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.258+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:32.267+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:12:32.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.361+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:32.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.372+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:12:32.395+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.332 seconds
[2025-07-18T11:13:03.911+0000] {processor.py:186} INFO - Started process (PID=1957) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:13:03.911+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:13:03.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:03.913+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:13:04.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.091+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:04.099+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:13:04.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.193+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:04.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.203+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:13:04.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.317 seconds
[2025-07-18T11:13:34.348+0000] {processor.py:186} INFO - Started process (PID=2096) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:13:34.349+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:13:34.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:34.351+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:13:34.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:34.537+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:34.546+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:13:34.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:34.651+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:34.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:34.663+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:13:34.683+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.341 seconds
[2025-07-18T11:14:05.348+0000] {processor.py:186} INFO - Started process (PID=2239) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:14:05.349+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:14:05.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.351+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:14:05.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.545+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:05.554+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:14:05.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.660+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:05.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.671+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:14:05.692+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.350 seconds
[2025-07-18T11:14:35.847+0000] {processor.py:186} INFO - Started process (PID=2380) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:14:35.848+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:14:35.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:35.850+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:14:36.056+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:36.056+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:36.067+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:14:36.167+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:36.167+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:36.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:36.178+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:14:36.197+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.357 seconds
[2025-07-18T11:15:06.630+0000] {processor.py:186} INFO - Started process (PID=2521) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:15:06.631+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:15:06.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.633+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:15:06.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.827+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:06.836+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:15:06.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.936+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:06.949+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.949+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:15:06.968+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.345 seconds
[2025-07-18T11:15:37.906+0000] {processor.py:186} INFO - Started process (PID=2662) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:15:37.907+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:15:37.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.909+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:15:38.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.193+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:38.205+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:15:38.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.345+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:38.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.357+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:15:38.375+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.476 seconds
[2025-07-18T11:16:08.535+0000] {processor.py:186} INFO - Started process (PID=2803) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:16:08.536+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:16:08.539+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.539+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:16:08.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.744+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:08.754+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:16:08.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.869+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:08.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.880+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:16:08.897+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.368 seconds
[2025-07-18T11:16:39.097+0000] {processor.py:186} INFO - Started process (PID=2944) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:16:39.098+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:16:39.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.100+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:16:39.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.291+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:39.301+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:16:39.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.406+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:39.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.417+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:16:39.437+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.346 seconds
[2025-07-18T11:17:10.566+0000] {processor.py:186} INFO - Started process (PID=3085) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:17:10.567+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:17:10.569+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.569+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:17:10.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.800+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:10.812+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:17:10.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.931+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:10.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.943+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:17:10.965+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.406 seconds
[2025-07-18T11:17:41.358+0000] {processor.py:186} INFO - Started process (PID=3226) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:17:41.359+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:17:41.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:41.361+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:17:41.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:41.563+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:41.572+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:17:41.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:41.672+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:41.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:41.682+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:17:41.701+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.350 seconds
[2025-07-18T11:18:12.080+0000] {processor.py:186} INFO - Started process (PID=3367) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:18:12.081+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:18:12.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.083+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:18:12.309+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.308+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:12.322+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:18:12.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.440+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:12.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.454+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:18:12.476+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.401 seconds
[2025-07-18T11:18:42.764+0000] {processor.py:186} INFO - Started process (PID=3508) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:18:42.764+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:18:42.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.766+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:18:42.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.975+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:42.986+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:18:43.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.089+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:43.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.102+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:18:43.121+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.364 seconds
[2025-07-18T11:19:13.835+0000] {processor.py:186} INFO - Started process (PID=3649) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:19:13.836+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:19:13.839+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:13.839+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:19:14.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:14.105+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:14.112+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:19:14.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:14.236+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:14.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:14.249+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:19:14.272+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.444 seconds
[2025-07-18T11:19:44.402+0000] {processor.py:186} INFO - Started process (PID=3793) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:19:44.403+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:19:44.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.406+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:19:44.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.615+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:44.625+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:19:44.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.727+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:44.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.737+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:19:44.757+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.362 seconds
[2025-07-18T11:20:15.570+0000] {processor.py:186} INFO - Started process (PID=3936) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:20:15.571+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:20:15.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.573+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:20:15.780+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.780+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:15.789+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:20:15.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.926+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:15.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.943+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:20:15.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.402 seconds
[2025-07-18T12:38:09.607+0000] {processor.py:186} INFO - Started process (PID=252) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:38:09.608+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T12:38:09.610+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.610+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:38:09.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.683+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:09.692+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:38:09.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.940+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_describe_challenge_pipeline
[2025-07-18T12:38:09.950+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.950+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_describe_challenge_pipeline
[2025-07-18T12:38:09.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.961+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_describe_challenge_pipeline
[2025-07-18T12:38:09.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.971+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_describe_challenge_pipeline
[2025-07-18T12:38:09.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.977+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_describe_challenge_pipeline
[2025-07-18T12:38:09.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.982+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_describe_challenge_pipeline
[2025-07-18T12:38:09.989+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.988+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_describe_challenge_pipeline
[2025-07-18T12:38:09.989+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.989+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:09.999+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T12:38:10.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.999+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_describe_challenge_pipeline
[2025-07-18T12:38:10.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:10.000+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T12:38:10.017+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.416 seconds
[2025-07-18T12:38:40.165+0000] {processor.py:186} INFO - Started process (PID=388) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:38:40.166+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T12:38:40.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:40.169+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:38:40.241+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:40.241+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:40.251+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:38:40.477+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:40.477+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:40.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:40.488+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T12:38:40.508+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.349 seconds
[2025-07-18T12:39:10.774+0000] {processor.py:186} INFO - Started process (PID=526) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:39:10.775+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T12:39:10.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:10.776+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:39:10.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:10.990+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:10.999+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:39:11.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:11.089+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:11.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:11.100+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T12:39:11.116+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.348 seconds
[2025-07-18T12:39:41.221+0000] {processor.py:186} INFO - Started process (PID=662) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:39:41.221+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T12:39:41.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:41.223+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:39:41.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:41.292+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:41.303+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:39:41.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:41.417+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:41.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:41.429+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T12:39:41.455+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.240 seconds
[2025-07-18T12:40:12.258+0000] {processor.py:186} INFO - Started process (PID=799) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:40:12.259+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T12:40:12.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:12.261+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:40:12.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:12.332+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:12.340+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:40:12.435+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:12.435+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:12.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:12.446+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T12:40:12.466+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.214 seconds
[2025-07-18T12:40:43.279+0000] {processor.py:186} INFO - Started process (PID=935) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:40:43.280+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T12:40:43.284+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:43.283+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:40:43.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:43.366+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:43.373+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:40:43.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:43.508+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:43.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:43.526+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T12:40:43.549+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.278 seconds
[2025-07-18T12:41:13.804+0000] {processor.py:186} INFO - Started process (PID=1071) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:41:13.805+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T12:41:13.807+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:13.806+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:41:13.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:13.874+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:41:13.882+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T12:41:13.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:13.976+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:41:13.989+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:13.988+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T12:41:14.007+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.208 seconds
