[2025-07-18T10:16:49.407+0000] {processor.py:186} INFO - Started process (PID=185) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:16:49.408+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:16:49.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.410+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:16:49.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.434+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:49.440+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:16:49.460+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.059 seconds
[2025-07-18T10:17:19.715+0000] {processor.py:186} INFO - Started process (PID=316) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:17:19.716+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:17:19.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:19.718+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:17:19.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:19.734+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:19.740+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:17:19.763+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.055 seconds
[2025-07-18T10:17:50.116+0000] {processor.py:186} INFO - Started process (PID=453) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:17:50.117+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:17:50.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.118+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:17:50.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.144+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:50.150+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:17:50.172+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.066 seconds
[2025-07-18T10:18:20.917+0000] {processor.py:186} INFO - Started process (PID=584) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:18:20.918+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:18:20.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:20.920+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:18:20.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:20.934+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:20.939+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:18:20.956+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.046 seconds
[2025-07-18T10:18:51.798+0000] {processor.py:186} INFO - Started process (PID=715) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:18:51.799+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:18:51.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:51.801+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:18:51.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:51.818+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:51.822+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:18:51.844+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.053 seconds
[2025-07-18T10:19:22.729+0000] {processor.py:186} INFO - Started process (PID=846) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:19:22.730+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:19:22.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:22.731+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:19:22.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:22.745+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:22.749+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:19:22.773+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.050 seconds
[2025-07-18T10:19:53.682+0000] {processor.py:186} INFO - Started process (PID=977) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:19:53.682+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:19:53.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:53.683+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:19:53.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:53.697+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:53.701+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:19:53.718+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.041 seconds
[2025-07-18T10:20:24.716+0000] {processor.py:186} INFO - Started process (PID=1108) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:20:24.718+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:20:24.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:24.719+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:20:24.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:24.735+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:24.739+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:20:24.757+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.046 seconds
[2025-07-18T10:20:55.701+0000] {processor.py:186} INFO - Started process (PID=1239) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:20:55.702+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:20:55.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:55.703+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:20:55.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:55.717+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:55.722+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:20:55.740+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.046 seconds
[2025-07-18T10:21:26.669+0000] {processor.py:186} INFO - Started process (PID=1370) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:21:26.670+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:21:26.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:26.671+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:21:26.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:26.685+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:26.689+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:21:26.707+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.043 seconds
[2025-07-18T10:21:56.819+0000] {processor.py:186} INFO - Started process (PID=1501) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:21:56.820+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:21:56.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:56.820+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:21:56.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:56.834+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:56.838+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:21:56.859+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.045 seconds
[2025-07-18T10:22:27.874+0000] {processor.py:186} INFO - Started process (PID=1632) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:22:27.874+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:22:27.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:27.875+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:22:27.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:27.891+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:27.896+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:22:27.921+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.052 seconds
[2025-07-18T10:22:58.868+0000] {processor.py:186} INFO - Started process (PID=1763) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:22:58.869+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:22:58.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:58.870+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:22:58.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:58.887+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:58.891+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:22:58.911+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.049 seconds
[2025-07-18T10:23:29.799+0000] {processor.py:186} INFO - Started process (PID=1894) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:23:29.800+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:23:29.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:29.801+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:23:29.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:29.815+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:29.819+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:23:29.842+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.049 seconds
[2025-07-18T10:24:00.648+0000] {processor.py:186} INFO - Started process (PID=2025) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:24:00.649+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:24:00.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:00.650+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:24:00.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:00.665+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:00.670+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:24:00.687+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.045 seconds
[2025-07-18T10:24:30.874+0000] {processor.py:186} INFO - Started process (PID=2156) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:24:30.875+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:24:30.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:30.875+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:24:30.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:30.890+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_unified_question_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_unified_question_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:30.894+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:24:30.912+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.044 seconds
[2025-07-18T10:25:01.861+0000] {processor.py:186} INFO - Started process (PID=2287) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:25:01.862+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:25:01.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:01.863+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:25:02.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:02.245+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:02.259+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:25:02.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:02.452+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:02.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:02.463+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:25:02.489+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.634 seconds
[2025-07-18T10:26:18.322+0000] {processor.py:186} INFO - Started process (PID=185) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:26:18.323+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:26:18.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:18.326+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:26:19.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:19.138+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:19.149+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:26:19.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:19.342+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:19.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:19.352+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:26:19.371+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 1.060 seconds
[2025-07-18T10:26:49.462+0000] {processor.py:186} INFO - Started process (PID=322) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:26:49.463+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:26:49.465+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:49.464+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:26:49.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:49.809+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:49.816+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:26:49.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:49.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:49.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:49.922+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:26:49.938+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.482 seconds
[2025-07-18T10:27:20.381+0000] {processor.py:186} INFO - Started process (PID=453) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:27:20.382+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:27:20.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:20.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:27:20.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:20.771+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:20.780+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:27:20.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:20.915+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:20.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:20.926+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:27:20.947+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.573 seconds
[2025-07-18T10:27:51.374+0000] {processor.py:186} INFO - Started process (PID=584) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:27:51.375+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:27:51.377+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:51.377+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:27:51.597+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:51.596+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:51.606+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:27:51.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:51.708+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:51.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:51.720+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:27:51.742+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.375 seconds
[2025-07-18T10:28:22.001+0000] {processor.py:186} INFO - Started process (PID=715) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:28:22.002+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:28:22.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:22.004+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:28:22.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:22.214+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:22.225+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:28:22.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:22.333+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:22.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:22.345+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:28:22.367+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.373 seconds
[2025-07-18T10:28:52.464+0000] {processor.py:186} INFO - Started process (PID=846) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:28:52.465+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:28:52.467+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:52.467+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:28:52.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:52.683+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:52.693+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:28:52.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:52.796+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:52.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:52.808+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:28:52.829+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.372 seconds
[2025-07-18T10:29:23.033+0000] {processor.py:186} INFO - Started process (PID=977) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:29:23.035+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:29:23.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:23.039+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:29:23.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:23.272+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:23.281+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:29:23.399+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:23.398+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:23.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:23.410+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:29:23.427+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.407 seconds
[2025-07-18T10:29:53.529+0000] {processor.py:186} INFO - Started process (PID=1108) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:29:53.530+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:29:53.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:53.533+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:29:53.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:53.803+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:53.811+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:29:53.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:53.913+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:53.925+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:53.925+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:29:53.947+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.425 seconds
[2025-07-18T10:30:24.282+0000] {processor.py:186} INFO - Started process (PID=1239) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:30:24.283+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:30:24.286+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:24.286+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:30:24.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:24.495+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:24.505+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:30:24.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:24.612+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:24.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:24.624+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:30:24.641+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.364 seconds
[2025-07-18T10:30:54.738+0000] {processor.py:186} INFO - Started process (PID=1370) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:30:54.739+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:30:54.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:54.741+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:30:54.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:54.968+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:54.977+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:30:55.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:55.089+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:55.101+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:55.101+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:30:55.123+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.392 seconds
[2025-07-18T10:31:25.765+0000] {processor.py:186} INFO - Started process (PID=1501) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:31:25.766+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:31:25.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:25.767+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:31:25.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:25.957+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:25.967+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:31:26.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:26.060+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:26.071+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:26.071+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:31:26.090+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.331 seconds
[2025-07-18T10:31:56.239+0000] {processor.py:186} INFO - Started process (PID=1632) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:31:56.241+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:31:56.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:56.244+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:31:56.502+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:56.502+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:56.513+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:31:56.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:56.637+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:56.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:56.647+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:31:56.667+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.436 seconds
[2025-07-18T10:32:26.852+0000] {processor.py:186} INFO - Started process (PID=1763) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:32:26.853+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:32:26.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:26.855+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:32:27.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:27.040+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:27.048+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:32:27.147+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:27.147+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:27.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:27.157+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:32:27.175+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.329 seconds
[2025-07-18T10:32:57.264+0000] {processor.py:186} INFO - Started process (PID=1894) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:32:57.265+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:32:57.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:57.268+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:32:57.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:57.462+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:57.472+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:32:57.568+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:57.567+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:57.577+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:57.577+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:32:57.595+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.339 seconds
[2025-07-18T10:33:27.859+0000] {processor.py:186} INFO - Started process (PID=2025) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:33:27.861+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:33:27.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:27.863+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:33:28.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:28.082+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:28.093+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:33:28.199+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:28.199+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:28.212+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:28.212+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:33:28.232+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.382 seconds
[2025-07-18T10:33:58.462+0000] {processor.py:186} INFO - Started process (PID=2156) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:33:58.463+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:33:58.465+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:58.465+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:33:58.656+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:58.655+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:58.666+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:33:58.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:58.762+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:58.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:58.775+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:33:58.793+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.336 seconds
[2025-07-18T10:34:29.146+0000] {processor.py:186} INFO - Started process (PID=2287) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:34:29.148+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:34:29.152+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:29.151+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:34:29.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:29.381+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:29.388+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:34:29.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:29.514+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:29.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:29.527+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:34:29.547+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.411 seconds
[2025-07-18T10:35:00.292+0000] {processor.py:186} INFO - Started process (PID=2418) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:35:00.293+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:35:00.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:00.295+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:35:00.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:00.499+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:00.509+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:35:00.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:00.607+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:00.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:00.620+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:35:00.641+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.356 seconds
[2025-07-18T10:35:30.882+0000] {processor.py:186} INFO - Started process (PID=2549) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:35:30.883+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:35:30.885+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:30.885+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:35:31.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:31.095+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:31.105+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:35:31.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:31.206+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:31.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:31.217+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:35:31.236+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.359 seconds
[2025-07-18T10:36:01.697+0000] {processor.py:186} INFO - Started process (PID=2680) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:36:01.698+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:36:01.701+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:01.701+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:36:01.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:01.893+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:01.901+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:36:01.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:01.995+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:02.006+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:02.006+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:36:02.025+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.335 seconds
[2025-07-18T10:36:32.734+0000] {processor.py:186} INFO - Started process (PID=2811) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:36:32.735+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:36:32.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:32.737+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:36:32.919+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:32.919+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:32.928+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:36:33.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:33.025+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:33.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:33.035+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:36:33.055+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.327 seconds
[2025-07-18T10:37:03.461+0000] {processor.py:186} INFO - Started process (PID=2942) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:37:03.462+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:37:03.464+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:03.464+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:37:03.660+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:03.660+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:03.668+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:37:03.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:03.760+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:03.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:03.771+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:37:03.792+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.337 seconds
[2025-07-18T10:37:34.119+0000] {processor.py:186} INFO - Started process (PID=3073) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:37:34.120+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:37:34.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:34.122+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:37:34.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:34.358+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:34.369+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:37:34.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:34.494+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:34.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:34.507+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:37:34.524+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.412 seconds
[2025-07-18T10:38:05.135+0000] {processor.py:186} INFO - Started process (PID=3204) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:38:05.136+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:38:05.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:05.139+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:38:05.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:05.348+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:05.356+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:38:05.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:05.462+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:05.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:05.474+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:38:05.496+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.368 seconds
[2025-07-18T10:38:35.858+0000] {processor.py:186} INFO - Started process (PID=3335) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:38:35.859+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:38:35.862+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:35.862+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:38:36.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:36.082+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:36.093+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:38:36.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:36.201+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:36.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:36.213+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:38:36.232+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.380 seconds
[2025-07-18T10:39:06.640+0000] {processor.py:186} INFO - Started process (PID=3466) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:39:06.641+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:39:06.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:06.643+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:39:06.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:06.829+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:06.839+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:39:06.938+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:06.937+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:06.948+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:06.948+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:39:06.968+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.334 seconds
[2025-07-18T10:39:37.173+0000] {processor.py:186} INFO - Started process (PID=3597) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:39:37.174+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:39:37.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:37.176+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:39:37.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:37.366+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:37.376+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:39:37.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:37.484+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:37.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:37.497+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:39:37.673+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.505 seconds
[2025-07-18T10:40:07.778+0000] {processor.py:186} INFO - Started process (PID=3728) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:40:07.779+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:40:07.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:07.781+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:40:07.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:07.983+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:07.990+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:40:08.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:08.088+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:08.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:08.254+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:40:08.271+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.500 seconds
[2025-07-18T10:40:38.676+0000] {processor.py:186} INFO - Started process (PID=3859) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:40:38.677+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:40:38.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:38.679+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:40:38.862+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:38.862+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:38.872+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:40:38.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:38.968+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:39.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:39.121+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:40:39.140+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.469 seconds
[2025-07-18T10:41:09.530+0000] {processor.py:186} INFO - Started process (PID=3990) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:41:09.531+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:41:09.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:09.533+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:41:09.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:09.714+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:09.724+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:41:09.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:09.819+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:09.963+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:09.963+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:41:09.982+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.457 seconds
[2025-07-18T10:41:40.314+0000] {processor.py:186} INFO - Started process (PID=4126) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:41:40.315+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:41:40.317+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:40.317+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:41:40.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:40.542+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:40.553+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:41:40.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:40.860+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:40.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:40.873+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:41:40.893+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.586 seconds
[2025-07-18T10:42:11.076+0000] {processor.py:186} INFO - Started process (PID=4262) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:42:11.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:42:11.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:11.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:42:11.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:11.256+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:11.265+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:42:11.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:11.544+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:11.554+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:11.554+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:42:11.572+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.503 seconds
[2025-07-18T10:42:56.623+0000] {processor.py:186} INFO - Started process (PID=190) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:42:56.624+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:42:56.626+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:56.626+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:42:56.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:56.982+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:56.988+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:42:57.093+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.093+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:57.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.104+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:42:57.126+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.508 seconds
[2025-07-18T10:43:27.380+0000] {processor.py:186} INFO - Started process (PID=328) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:43:27.382+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:43:27.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:27.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:43:27.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:27.704+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:27.712+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:43:27.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:27.790+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:27.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:27.798+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:43:27.815+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.441 seconds
[2025-07-18T10:43:57.871+0000] {processor.py:186} INFO - Started process (PID=468) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:43:57.872+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:43:57.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:57.874+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:43:58.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:58.180+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:58.188+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:43:58.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:58.276+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:58.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:58.284+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:43:58.302+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.436 seconds
[2025-07-18T10:44:28.756+0000] {processor.py:186} INFO - Started process (PID=604) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:44:28.757+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:44:28.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:28.759+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:44:28.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:28.969+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:28.979+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:44:29.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:29.078+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:29.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:29.089+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:44:29.112+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.363 seconds
[2025-07-18T10:44:59.745+0000] {processor.py:186} INFO - Started process (PID=740) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:44:59.747+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:44:59.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:59.749+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:44:59.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:59.955+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:59.964+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:45:00.067+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:00.067+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:00.080+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:00.079+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:45:00.101+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.362 seconds
[2025-07-18T10:45:30.417+0000] {processor.py:186} INFO - Started process (PID=878) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:45:30.418+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:45:30.420+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:30.420+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:45:30.622+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:30.622+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:30.631+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:45:30.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:30.737+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:30.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:30.752+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:45:30.772+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.360 seconds
[2025-07-18T10:46:01.304+0000] {processor.py:186} INFO - Started process (PID=1012) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:46:01.305+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:46:01.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:01.307+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:46:01.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:01.529+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:01.539+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:46:01.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:01.668+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:01.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:01.684+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:46:01.703+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.405 seconds
[2025-07-18T10:46:31.807+0000] {processor.py:186} INFO - Started process (PID=1148) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:46:31.809+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:46:31.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:31.811+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:46:32.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:32.045+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:32.057+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:46:32.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:32.178+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:32.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:32.191+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:46:32.212+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.411 seconds
[2025-07-18T10:47:02.451+0000] {processor.py:186} INFO - Started process (PID=1284) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:47:02.452+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:47:02.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:02.454+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:47:02.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:02.682+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:02.691+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:47:02.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:02.800+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:02.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:02.814+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:47:02.836+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.390 seconds
[2025-07-18T10:48:02.018+0000] {processor.py:186} INFO - Started process (PID=196) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:48:02.020+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:48:02.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:02.023+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:48:02.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:02.386+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:02.393+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:48:02.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:02.489+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:02.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:02.499+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:48:02.520+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.509 seconds
[2025-07-18T10:48:32.678+0000] {processor.py:186} INFO - Started process (PID=332) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:48:32.679+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:48:32.681+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:32.680+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:48:33.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:33.004+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:33.012+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:48:33.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:33.099+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:33.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:33.108+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:48:33.127+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.455 seconds
[2025-07-18T10:49:03.926+0000] {processor.py:186} INFO - Started process (PID=468) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:49:03.927+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:49:03.930+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:03.929+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:49:04.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.290+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:04.297+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:49:04.411+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.410+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:04.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.420+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:49:04.440+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.521 seconds
[2025-07-18T10:49:35.282+0000] {processor.py:186} INFO - Started process (PID=606) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:49:35.283+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:49:35.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:35.284+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:49:35.490+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:35.490+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:35.500+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:49:35.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:35.607+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:35.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:35.620+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:49:35.643+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.367 seconds
[2025-07-18T10:50:05.975+0000] {processor.py:186} INFO - Started process (PID=745) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:50:05.976+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:50:05.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:05.978+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:50:06.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.216+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:06.223+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:50:06.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.321+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:06.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.334+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:50:06.355+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.386 seconds
[2025-07-18T10:50:36.572+0000] {processor.py:186} INFO - Started process (PID=878) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:50:36.573+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:50:36.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:36.575+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:50:36.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:36.785+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:36.795+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:50:36.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:36.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:36.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:36.923+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:50:36.944+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.378 seconds
[2025-07-18T10:51:07.345+0000] {processor.py:186} INFO - Started process (PID=1014) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:51:07.345+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:51:07.347+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:07.347+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:51:07.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:07.537+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:07.546+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:51:07.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:07.650+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:07.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:07.661+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:51:07.682+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.343 seconds
[2025-07-18T10:51:37.824+0000] {processor.py:186} INFO - Started process (PID=1151) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:51:37.825+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:51:37.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:37.826+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:51:38.023+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.023+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:38.032+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:51:38.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.137+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:38.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.149+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:51:38.170+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.352 seconds
[2025-07-18T10:52:08.270+0000] {processor.py:186} INFO - Started process (PID=1287) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:52:08.271+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:52:08.274+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:08.274+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:52:08.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:08.522+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:08.531+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:52:08.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:08.637+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:08.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:08.649+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:52:08.671+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.407 seconds
[2025-07-18T10:52:38.825+0000] {processor.py:186} INFO - Started process (PID=1423) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:52:38.826+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:52:38.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:38.828+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:52:39.018+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.018+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:39.028+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:52:39.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.140+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:39.152+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.152+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:52:39.173+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.354 seconds
[2025-07-18T10:53:09.454+0000] {processor.py:186} INFO - Started process (PID=1559) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:53:09.455+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:53:09.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:09.457+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:53:09.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:09.658+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:09.667+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:53:09.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:09.772+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:09.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:09.784+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:53:09.805+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.357 seconds
[2025-07-18T10:53:40.000+0000] {processor.py:186} INFO - Started process (PID=1695) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:53:40.001+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:53:40.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.002+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:53:40.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.192+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:40.201+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:53:40.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.295+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:40.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.305+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:53:40.325+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.331 seconds
[2025-07-18T10:54:10.652+0000] {processor.py:186} INFO - Started process (PID=1831) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:54:10.653+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:54:10.655+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:10.655+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:54:10.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:10.872+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:10.881+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:54:10.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:10.991+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:11.002+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.002+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:54:11.023+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.378 seconds
[2025-07-18T10:54:41.411+0000] {processor.py:186} INFO - Started process (PID=1967) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:54:41.412+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:54:41.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:41.415+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:54:41.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:41.620+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:41.630+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:54:41.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:41.725+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:41.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:41.737+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:54:41.758+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.353 seconds
[2025-07-18T10:55:12.062+0000] {processor.py:186} INFO - Started process (PID=2103) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:55:12.063+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:55:12.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.065+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:55:12.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.253+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:12.261+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:55:12.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.363+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:12.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.374+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:55:12.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.336 seconds
[2025-07-18T10:55:42.943+0000] {processor.py:186} INFO - Started process (PID=2239) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:55:42.944+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:55:42.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:42.946+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:55:43.147+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.146+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:43.156+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:55:43.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.245+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:43.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.257+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:55:43.278+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.341 seconds
[2025-07-18T10:57:26.776+0000] {processor.py:186} INFO - Started process (PID=190) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:57:26.777+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:57:26.780+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:26.780+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:57:27.158+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.158+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:27.163+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:57:27.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.245+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:27.255+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.255+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:57:27.276+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.505 seconds
[2025-07-18T10:57:57.571+0000] {processor.py:186} INFO - Started process (PID=334) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:57:57.572+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:57:57.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:57.574+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:57:57.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:57.892+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:57.899+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:57:57.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:57.987+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:57.996+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:57.996+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:57:58.013+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.448 seconds
[2025-07-18T10:58:28.219+0000] {processor.py:186} INFO - Started process (PID=470) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:58:28.220+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:58:28.222+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:28.222+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:58:28.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:28.525+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:28.533+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:58:28.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:28.635+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:28.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:28.644+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:58:28.661+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.448 seconds
[2025-07-18T10:58:58.758+0000] {processor.py:186} INFO - Started process (PID=606) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:58:58.759+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:58:58.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:58.760+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:58:58.942+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:58.942+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:58.950+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:58:59.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:59.037+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:59.048+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:59.048+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:58:59.065+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.313 seconds
[2025-07-18T10:59:29.611+0000] {processor.py:186} INFO - Started process (PID=740) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:59:29.611+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T10:59:29.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:29.613+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:59:29.812+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:29.812+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:29.821+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T10:59:29.917+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:29.917+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:29.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:29.928+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T10:59:29.947+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.342 seconds
[2025-07-18T11:00:00.148+0000] {processor.py:186} INFO - Started process (PID=876) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:00:00.149+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:00:00.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:00.151+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:00:00.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:00.360+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:00.369+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:00:00.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:00.474+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:00.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:00.489+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:00:00.510+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.368 seconds
[2025-07-18T11:00:31.320+0000] {processor.py:186} INFO - Started process (PID=1012) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:00:31.321+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:00:31.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:31.323+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:00:31.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:31.509+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:31.517+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:00:31.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:31.614+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:31.626+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:31.626+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:00:31.644+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.329 seconds
[2025-07-18T11:01:02.010+0000] {processor.py:186} INFO - Started process (PID=1148) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:01:02.011+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:01:02.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.014+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:01:02.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.219+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:02.230+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:01:02.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.335+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:02.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.346+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:01:02.368+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.365 seconds
[2025-07-18T11:01:32.670+0000] {processor.py:186} INFO - Started process (PID=1284) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:01:32.671+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:01:32.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:32.673+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:01:32.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:32.852+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:32.861+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:01:32.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:32.957+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:32.967+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:32.967+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:01:32.986+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.321 seconds
[2025-07-18T11:02:03.888+0000] {processor.py:186} INFO - Started process (PID=1420) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:02:03.889+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:02:03.891+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:03.891+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:02:04.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.109+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:04.118+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:02:04.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.221+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:04.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.232+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:02:04.253+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.372 seconds
[2025-07-18T11:02:34.775+0000] {processor.py:186} INFO - Started process (PID=1556) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:02:34.776+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:02:34.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:34.779+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:02:35.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.007+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:35.017+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:02:35.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.129+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:35.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.144+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:02:35.164+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.394 seconds
[2025-07-18T11:03:05.257+0000] {processor.py:186} INFO - Started process (PID=1692) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:03:05.258+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:03:05.261+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:05.260+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:03:05.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:05.487+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:05.495+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:03:05.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:05.595+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:05.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:05.607+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:03:05.629+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.377 seconds
[2025-07-18T11:03:35.929+0000] {processor.py:186} INFO - Started process (PID=1828) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:03:35.930+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:03:35.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:35.933+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:03:36.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.148+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:36.158+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:03:36.266+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.265+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:36.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.277+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:03:36.298+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.374 seconds
[2025-07-18T11:04:06.620+0000] {processor.py:186} INFO - Started process (PID=1964) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:04:06.621+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:04:06.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:06.623+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:04:06.855+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:06.855+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:06.866+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:04:06.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:06.986+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:06.999+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:06.999+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:04:07.024+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.411 seconds
[2025-07-18T11:04:37.268+0000] {processor.py:186} INFO - Started process (PID=2100) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:04:37.269+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:04:37.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:37.272+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:04:37.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:37.511+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:37.521+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:04:37.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:37.634+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:37.647+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:37.647+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:04:37.671+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.410 seconds
[2025-07-18T11:05:07.761+0000] {processor.py:186} INFO - Started process (PID=2236) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:05:07.761+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:05:07.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:07.765+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:05:08.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:08.001+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:08.017+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:05:08.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:08.149+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:08.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:08.163+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:05:08.184+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.430 seconds
[2025-07-18T11:05:38.352+0000] {processor.py:186} INFO - Started process (PID=2372) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:05:38.354+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:05:38.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:38.356+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:05:38.582+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:38.581+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:38.589+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:05:38.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:38.693+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:38.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:38.708+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:05:38.731+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.385 seconds
[2025-07-18T11:06:49.421+0000] {processor.py:186} INFO - Started process (PID=202) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:06:49.422+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:06:49.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:49.424+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:06:49.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:49.809+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:49.818+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:06:49.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:49.927+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:49.939+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:49.939+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:06:49.955+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.539 seconds
[2025-07-18T11:07:20.058+0000] {processor.py:186} INFO - Started process (PID=343) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:07:20.059+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:07:20.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:20.061+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:07:20.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:20.442+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:20.448+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:07:20.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:20.546+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:20.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:20.557+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:07:20.580+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.529 seconds
[2025-07-18T11:07:51.566+0000] {processor.py:186} INFO - Started process (PID=484) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:07:51.567+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:07:51.571+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:51.570+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:07:51.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:51.961+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:51.968+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:07:52.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:52.094+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:52.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:52.110+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:07:52.133+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.574 seconds
[2025-07-18T11:08:22.883+0000] {processor.py:186} INFO - Started process (PID=631) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:08:22.884+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:08:22.886+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:22.886+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:08:23.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.099+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:23.109+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:08:23.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.219+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:23.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.231+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:08:23.253+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.376 seconds
[2025-07-18T11:08:53.792+0000] {processor.py:186} INFO - Started process (PID=772) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:08:53.793+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:08:53.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:53.795+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:08:53.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:53.998+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:54.008+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:08:54.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.112+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:54.123+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.123+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:08:54.143+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.357 seconds
[2025-07-18T11:09:24.297+0000] {processor.py:186} INFO - Started process (PID=913) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:09:24.299+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:09:24.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:24.301+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:09:24.518+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:24.518+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:24.528+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:09:24.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:24.641+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:24.655+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:24.655+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:09:24.678+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.387 seconds
[2025-07-18T11:09:54.916+0000] {processor.py:186} INFO - Started process (PID=1054) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:09:54.917+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:09:54.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:54.919+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:09:55.134+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.134+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:55.143+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:09:55.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.245+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:55.256+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.256+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:09:55.277+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.367 seconds
[2025-07-18T11:10:25.885+0000] {processor.py:186} INFO - Started process (PID=1195) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:10:25.885+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:10:25.887+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:25.887+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:10:26.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.102+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:26.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:10:26.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.210+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:26.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.223+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:10:26.242+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.364 seconds
[2025-07-18T11:10:56.858+0000] {processor.py:186} INFO - Started process (PID=1336) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:10:56.858+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:10:56.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:56.861+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:10:57.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.059+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:57.068+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:10:57.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.168+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:57.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.180+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:10:57.201+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.349 seconds
[2025-07-18T11:11:27.401+0000] {processor.py:186} INFO - Started process (PID=1477) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:11:27.402+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:11:27.405+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:27.405+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:11:27.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:27.612+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:27.621+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:11:27.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:27.730+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:27.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:27.742+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:11:27.760+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.365 seconds
[2025-07-18T11:11:58.500+0000] {processor.py:186} INFO - Started process (PID=1618) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:11:58.501+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:11:58.504+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:58.504+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:11:58.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:58.721+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:58.730+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:11:58.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:58.827+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:58.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:58.849+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:11:58.878+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.383 seconds
[2025-07-18T11:12:29.078+0000] {processor.py:186} INFO - Started process (PID=1759) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:12:29.079+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:12:29.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:29.082+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:12:29.289+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:29.289+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:29.299+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:12:29.395+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:29.394+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:29.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:29.405+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:12:29.427+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.358 seconds
[2025-07-18T11:13:00.420+0000] {processor.py:186} INFO - Started process (PID=1900) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:13:00.421+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:13:00.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:00.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:13:00.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:00.631+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:00.640+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:13:00.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:00.745+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:00.757+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:00.756+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:13:00.779+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.364 seconds
[2025-07-18T11:13:31.330+0000] {processor.py:186} INFO - Started process (PID=2041) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:13:31.330+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:13:31.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:31.332+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:13:31.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:31.547+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:31.557+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:13:31.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:31.657+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:31.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:31.669+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:13:31.687+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.363 seconds
[2025-07-18T11:14:01.970+0000] {processor.py:186} INFO - Started process (PID=2182) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:14:01.972+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:14:01.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:01.976+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:14:02.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:02.214+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:02.222+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:14:02.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:02.333+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:02.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:02.346+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:14:02.367+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.405 seconds
[2025-07-18T11:14:32.587+0000] {processor.py:186} INFO - Started process (PID=2323) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:14:32.588+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:14:32.590+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:32.590+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:14:32.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:32.795+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:32.806+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:14:32.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:32.912+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:32.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:32.923+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:14:32.945+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.364 seconds
[2025-07-18T11:15:03.616+0000] {processor.py:186} INFO - Started process (PID=2464) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:15:03.616+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:15:03.619+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:03.618+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:15:03.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:03.851+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:03.862+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:15:03.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:03.972+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:03.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:03.985+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:15:04.005+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.396 seconds
[2025-07-18T11:15:34.460+0000] {processor.py:186} INFO - Started process (PID=2605) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:15:34.461+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:15:34.464+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:34.464+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:15:34.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:34.708+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:34.720+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:15:34.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:34.839+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:34.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:34.853+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:15:34.875+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.422 seconds
[2025-07-18T11:16:05.117+0000] {processor.py:186} INFO - Started process (PID=2746) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:16:05.118+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:16:05.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.119+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:16:05.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.340+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:05.350+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:16:05.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.452+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:05.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.463+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:16:05.485+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.374 seconds
[2025-07-18T11:16:35.907+0000] {processor.py:186} INFO - Started process (PID=2887) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:16:35.908+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:16:35.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:35.910+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:16:36.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.120+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:36.130+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:16:36.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.232+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:36.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.244+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:16:36.264+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.363 seconds
[2025-07-18T11:17:06.728+0000] {processor.py:186} INFO - Started process (PID=3028) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:17:06.730+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:17:06.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:06.733+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:17:06.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:06.972+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:06.983+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:17:07.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:07.098+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:07.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:07.111+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:17:07.133+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.413 seconds
[2025-07-18T11:17:37.251+0000] {processor.py:186} INFO - Started process (PID=3169) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:17:37.252+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:17:37.255+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:37.255+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:17:37.473+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:37.473+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:37.485+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:17:37.602+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:37.602+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:37.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:37.615+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:17:37.633+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.387 seconds
[2025-07-18T11:18:07.884+0000] {processor.py:186} INFO - Started process (PID=3310) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:18:07.885+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:18:07.888+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:07.888+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:18:08.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:08.127+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:08.136+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:18:08.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:08.250+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:08.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:08.262+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:18:08.285+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.406 seconds
[2025-07-18T11:18:38.859+0000] {processor.py:186} INFO - Started process (PID=3451) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:18:38.860+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:18:38.862+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:38.862+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:18:39.092+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:39.092+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:39.101+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:18:39.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:39.215+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:39.229+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:39.229+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:18:39.248+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.395 seconds
[2025-07-18T11:19:10.174+0000] {processor.py:186} INFO - Started process (PID=3592) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:19:10.175+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:19:10.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:10.177+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:19:10.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:10.383+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:10.393+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:19:10.510+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:10.509+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:10.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:10.522+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:19:10.545+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.379 seconds
[2025-07-18T11:19:40.837+0000] {processor.py:186} INFO - Started process (PID=3738) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:19:40.838+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:19:40.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:40.841+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:19:41.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.041+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:41.050+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:19:41.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.156+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:41.172+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.171+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:19:41.191+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.360 seconds
[2025-07-18T11:20:11.297+0000] {processor.py:186} INFO - Started process (PID=3879) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:20:11.298+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:20:11.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:11.300+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:20:11.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:11.498+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:11.507+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:20:11.619+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:11.619+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:11.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:11.632+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:20:11.654+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.362 seconds
[2025-07-18T12:38:06.823+0000] {processor.py:186} INFO - Started process (PID=191) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:38:06.824+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T12:38:06.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:06.826+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:38:06.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:06.910+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:06.917+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:38:07.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.096+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_unified_question_pipeline
[2025-07-18T12:38:07.118+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.117+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_unified_question_pipeline
[2025-07-18T12:38:07.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.132+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_unified_question_pipeline
[2025-07-18T12:38:07.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.152+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_unified_question_pipeline
[2025-07-18T12:38:07.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.166+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_unified_question_pipeline
[2025-07-18T12:38:07.182+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.181+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_unified_question_pipeline
[2025-07-18T12:38:07.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.194+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_unified_question_pipeline
[2025-07-18T12:38:07.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.195+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:07.379+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T12:38:07.382+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.381+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_unified_question_pipeline
[2025-07-18T12:38:07.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:07.385+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T12:38:07.405+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.588 seconds
[2025-07-18T12:38:37.778+0000] {processor.py:186} INFO - Started process (PID=333) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:38:37.779+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T12:38:37.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:37.780+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:38:37.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:37.863+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:37.871+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:38:38.101+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:38.100+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:38.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:38.110+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T12:38:38.128+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.356 seconds
[2025-07-18T12:39:08.517+0000] {processor.py:186} INFO - Started process (PID=469) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:39:08.518+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T12:39:08.520+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:08.520+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:39:08.710+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:08.710+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:08.716+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:39:08.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:08.807+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:08.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:08.818+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T12:39:08.838+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.326 seconds
[2025-07-18T12:39:39.022+0000] {processor.py:186} INFO - Started process (PID=605) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:39:39.024+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T12:39:39.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:39.025+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:39:39.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:39.097+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:39.105+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:39:39.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:39.198+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:39.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:39.208+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T12:39:39.226+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.209 seconds
[2025-07-18T12:40:09.861+0000] {processor.py:186} INFO - Started process (PID=742) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:40:09.862+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T12:40:09.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:09.865+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:40:09.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:09.940+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:09.950+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:40:10.048+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:10.048+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:10.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:10.057+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T12:40:10.076+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.224 seconds
[2025-07-18T12:40:40.816+0000] {processor.py:186} INFO - Started process (PID=878) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:40:40.817+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T12:40:40.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:40.820+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:40:40.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:40.906+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:40.914+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:40:41.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:41.012+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:41.021+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:41.021+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T12:40:41.041+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.230 seconds
[2025-07-18T12:41:11.391+0000] {processor.py:186} INFO - Started process (PID=1014) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:41:11.392+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T12:41:11.395+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:11.395+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:41:11.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:11.478+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:41:11.486+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:41:11.584+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:11.584+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:41:11.595+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:11.594+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T12:41:11.614+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.228 seconds
[2025-07-18T12:41:42.162+0000] {processor.py:186} INFO - Started process (PID=1152) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:41:42.163+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T12:41:42.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:42.165+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:41:42.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:42.238+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:41:42.247+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T12:41:42.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:42.356+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:41:42.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:42.369+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T12:41:42.390+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.232 seconds
