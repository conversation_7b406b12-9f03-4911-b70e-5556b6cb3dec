[2025-07-18T10:16:50.405+0000] {processor.py:186} INFO - Started process (PID=258) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:16:50.406+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:16:50.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.408+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:16:50.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.449+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:50.455+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:16:50.475+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.076 seconds
[2025-07-18T10:17:20.599+0000] {processor.py:186} INFO - Started process (PID=387) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:17:20.600+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:17:20.601+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.601+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:17:20.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.630+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.635+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:17:20.652+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.059 seconds
[2025-07-18T10:17:51.382+0000] {processor.py:186} INFO - Started process (PID=518) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:17:51.383+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:17:51.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.385+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:17:51.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:51.567+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:51.571+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:17:51.586+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.210 seconds
[2025-07-18T10:18:21.726+0000] {processor.py:186} INFO - Started process (PID=651) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:18:21.727+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:18:21.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:18:21.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.759+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.764+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:18:21.778+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.058 seconds
[2025-07-18T10:18:52.582+0000] {processor.py:186} INFO - Started process (PID=782) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:18:52.583+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:18:52.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.584+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:18:52.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.617+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:52.621+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:18:52.642+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.066 seconds
[2025-07-18T10:19:23.557+0000] {processor.py:186} INFO - Started process (PID=913) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:19:23.558+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:19:23.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.559+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:19:23.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.590+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:23.594+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:19:23.610+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.059 seconds
[2025-07-18T10:19:54.537+0000] {processor.py:186} INFO - Started process (PID=1044) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:19:54.538+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:19:54.539+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.539+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:19:54.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.572+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:54.576+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:19:54.592+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.060 seconds
[2025-07-18T10:20:25.537+0000] {processor.py:186} INFO - Started process (PID=1175) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:20:25.538+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:20:25.539+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.539+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:20:25.576+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.573+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:25.577+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:20:25.594+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.063 seconds
[2025-07-18T10:20:56.479+0000] {processor.py:186} INFO - Started process (PID=1306) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:20:56.480+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:20:56.482+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.482+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:20:56.521+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.518+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:56.522+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:20:56.540+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.067 seconds
[2025-07-18T10:21:27.476+0000] {processor.py:186} INFO - Started process (PID=1437) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:21:27.477+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:21:27.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.478+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:21:27.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.513+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:27.518+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:21:27.534+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.065 seconds
[2025-07-18T10:21:57.655+0000] {processor.py:186} INFO - Started process (PID=1568) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:21:57.656+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:21:57.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.657+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:21:57.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.686+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:57.690+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:21:57.704+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.055 seconds
[2025-07-18T10:22:28.684+0000] {processor.py:186} INFO - Started process (PID=1699) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:22:28.685+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:22:28.686+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.686+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:22:28.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.717+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:28.722+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:22:28.738+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.059 seconds
[2025-07-18T10:22:59.648+0000] {processor.py:186} INFO - Started process (PID=1830) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:22:59.649+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:22:59.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.650+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:22:59.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.684+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:59.688+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:22:59.704+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.062 seconds
[2025-07-18T10:23:30.517+0000] {processor.py:186} INFO - Started process (PID=1959) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:23:30.518+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:23:30.519+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.519+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:23:30.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.548+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:30.552+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:23:30.569+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.057 seconds
[2025-07-18T10:24:01.504+0000] {processor.py:186} INFO - Started process (PID=2092) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:24:01.505+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:24:01.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.506+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:24:01.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.539+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:01.544+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:24:01.564+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.067 seconds
[2025-07-18T10:24:31.742+0000] {processor.py:186} INFO - Started process (PID=2223) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:24:31.743+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:24:31.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.744+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:24:31.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.783+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_classify_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_classify_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.786+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:24:31.804+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.069 seconds
[2025-07-18T10:25:05.331+0000] {processor.py:186} INFO - Started process (PID=2354) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:25:05.332+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:25:05.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.333+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:25:05.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.513+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:05.522+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:25:05.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.606+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:05.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:05.615+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:25:05.634+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.309 seconds
[2025-07-18T10:26:23.170+0000] {processor.py:186} INFO - Started process (PID=256) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:26:23.171+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:26:23.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.174+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:26:23.566+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.566+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:23.574+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:26:23.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.679+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:23.689+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:23.688+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:26:23.706+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.542 seconds
[2025-07-18T10:26:55.541+0000] {processor.py:186} INFO - Started process (PID=387) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:26:55.543+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:26:55.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.545+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:26:55.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:55.905+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:55.913+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:26:56.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.002+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:56.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.012+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:26:56.034+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.499 seconds
[2025-07-18T10:27:26.799+0000] {processor.py:186} INFO - Started process (PID=518) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:27:26.800+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:27:26.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.802+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:27:27.143+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.143+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:27.149+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:27:27.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.250+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:27.259+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:27.258+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:27:27.277+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.485 seconds
[2025-07-18T10:27:57.384+0000] {processor.py:186} INFO - Started process (PID=649) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:27:57.385+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:27:57.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.388+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:27:57.576+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.576+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:57.585+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:27:57.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.694+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:57.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:57.707+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:27:57.730+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.351 seconds
[2025-07-18T10:28:28.501+0000] {processor.py:186} INFO - Started process (PID=780) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:28:28.501+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:28:28.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.503+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:28:28.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.705+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:28.715+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:28:28.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.823+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:28.836+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.836+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:28:28.856+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.361 seconds
[2025-07-18T10:28:59.341+0000] {processor.py:186} INFO - Started process (PID=913) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:28:59.342+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:28:59.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.344+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:28:59.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.533+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:59.542+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:28:59.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.642+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:59.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:59.654+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:28:59.681+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.345 seconds
[2025-07-18T10:29:30.023+0000] {processor.py:186} INFO - Started process (PID=1044) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:29:30.024+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:29:30.027+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:30.026+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:29:30.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:30.221+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:30.230+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:29:30.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:30.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:30.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:30.349+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:29:30.369+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.351 seconds
[2025-07-18T10:30:00.773+0000] {processor.py:186} INFO - Started process (PID=1175) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:30:00.774+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:30:00.776+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:00.776+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:30:00.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:00.961+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:00.971+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:30:01.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.058+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:01.067+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.067+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:30:01.083+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.315 seconds
[2025-07-18T10:30:31.658+0000] {processor.py:186} INFO - Started process (PID=1306) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:30:31.659+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:30:31.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.661+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:30:31.857+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.857+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:31.865+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:30:31.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.972+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:31.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:31.982+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:30:32.003+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.351 seconds
[2025-07-18T10:31:02.228+0000] {processor.py:186} INFO - Started process (PID=1437) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:31:02.229+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:31:02.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.231+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:31:02.420+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.420+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:02.429+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:31:02.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.522+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:02.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:02.533+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:31:02.552+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.330 seconds
[2025-07-18T10:31:32.690+0000] {processor.py:186} INFO - Started process (PID=1568) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:31:32.691+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:31:32.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.693+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:31:32.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.875+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:32.884+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:31:32.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.976+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:32.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:32.987+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:31:33.004+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.320 seconds
[2025-07-18T10:32:03.380+0000] {processor.py:186} INFO - Started process (PID=1697) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:32:03.381+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:32:03.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.382+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:32:03.590+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.589+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:03.598+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:32:03.699+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.699+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:03.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:03.712+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:32:03.736+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.362 seconds
[2025-07-18T10:32:33.843+0000] {processor.py:186} INFO - Started process (PID=1828) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:32:33.844+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:32:33.846+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.846+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:32:34.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.037+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:34.046+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:32:34.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.142+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:34.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:34.152+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:32:34.171+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.334 seconds
[2025-07-18T10:33:04.274+0000] {processor.py:186} INFO - Started process (PID=1959) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:33:04.274+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:33:04.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.276+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:33:04.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.454+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:04.464+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:33:04.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.563+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:04.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:04.573+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:33:04.592+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.324 seconds
[2025-07-18T10:33:34.889+0000] {processor.py:186} INFO - Started process (PID=2090) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:33:34.890+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:33:34.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.892+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:33:35.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.072+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:35.081+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:33:35.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.169+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:35.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:35.178+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:33:35.196+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.313 seconds
[2025-07-18T10:34:05.518+0000] {processor.py:186} INFO - Started process (PID=2221) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:34:05.519+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:34:05.521+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.520+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:34:05.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.714+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:05.722+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:34:05.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.808+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:05.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.818+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:34:05.836+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.325 seconds
[2025-07-18T10:34:36.597+0000] {processor.py:186} INFO - Started process (PID=2352) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:34:36.598+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:34:36.601+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.600+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:34:36.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.779+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:36.787+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:34:36.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.879+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:36.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.889+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:34:36.908+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.316 seconds
[2025-07-18T10:35:07.044+0000] {processor.py:186} INFO - Started process (PID=2483) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:35:07.045+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:35:07.048+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.048+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:35:07.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.238+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:07.246+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:35:07.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.345+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:07.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:07.356+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:35:07.380+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.341 seconds
[2025-07-18T10:35:38.141+0000] {processor.py:186} INFO - Started process (PID=2614) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:35:38.142+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:35:38.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.144+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:35:38.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.345+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:38.355+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:35:38.460+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.459+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:38.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:38.474+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:35:38.498+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.363 seconds
[2025-07-18T10:36:08.890+0000] {processor.py:186} INFO - Started process (PID=2745) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:36:08.891+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:36:08.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.892+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:36:09.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.096+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:09.106+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:36:09.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.200+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:09.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:09.212+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:36:09.233+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.349 seconds
[2025-07-18T10:36:39.487+0000] {processor.py:186} INFO - Started process (PID=2876) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:36:39.488+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:36:39.490+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.490+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:36:39.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.675+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:39.685+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:36:39.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.801+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:39.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.814+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:36:39.835+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.353 seconds
[2025-07-18T10:37:10.369+0000] {processor.py:186} INFO - Started process (PID=3007) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:37:10.371+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:37:10.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.373+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:37:10.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.587+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:10.595+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:37:10.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.706+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:10.723+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:10.723+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:37:10.743+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.379 seconds
[2025-07-18T10:37:41.606+0000] {processor.py:186} INFO - Started process (PID=3140) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:37:41.606+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:37:41.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.608+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:37:41.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.814+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:41.824+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:37:41.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.918+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:41.930+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:41.930+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:37:41.951+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.351 seconds
[2025-07-18T10:38:12.127+0000] {processor.py:186} INFO - Started process (PID=3271) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:38:12.128+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:38:12.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.130+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:38:12.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.333+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:12.343+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:38:12.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.442+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:12.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:12.454+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:38:12.473+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.353 seconds
[2025-07-18T10:38:42.656+0000] {processor.py:186} INFO - Started process (PID=3400) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:38:42.656+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:38:42.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.658+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:38:42.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.835+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:42.843+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:38:42.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.940+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:42.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.951+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:38:42.969+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.319 seconds
[2025-07-18T10:39:13.358+0000] {processor.py:186} INFO - Started process (PID=3531) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:39:13.359+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:39:13.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.362+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:39:13.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.554+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:13.563+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:39:13.660+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.659+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:13.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:13.670+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:39:13.688+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.335 seconds
[2025-07-18T10:39:44.061+0000] {processor.py:186} INFO - Started process (PID=3662) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:39:44.062+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:39:44.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.065+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:39:44.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.307+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:44.316+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:39:44.411+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.411+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:44.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:44.421+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:39:44.443+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.389 seconds
[2025-07-18T10:40:14.762+0000] {processor.py:186} INFO - Started process (PID=3793) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:40:14.763+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:40:14.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.765+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:40:14.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.968+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:14.978+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:40:15.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:15.094+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:15.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:15.105+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:40:15.128+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.372 seconds
[2025-07-18T10:40:46.270+0000] {processor.py:186} INFO - Started process (PID=3926) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:40:46.271+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:40:46.273+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.273+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:40:46.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.458+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:46.467+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:40:46.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.567+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:46.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:46.579+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:40:46.603+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.339 seconds
[2025-07-18T10:41:16.708+0000] {processor.py:186} INFO - Started process (PID=4055) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:41:16.709+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:41:16.711+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:16.711+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:41:16.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:16.943+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:16.950+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:41:17.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.054+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:17.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.064+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:41:17.081+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.379 seconds
[2025-07-18T10:41:47.299+0000] {processor.py:186} INFO - Started process (PID=4191) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:41:47.300+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:41:47.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.302+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:41:47.502+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.502+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:47.511+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:41:47.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.621+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:47.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:47.632+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:41:47.647+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.353 seconds
[2025-07-18T10:42:17.805+0000] {processor.py:186} INFO - Started process (PID=4327) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:42:17.806+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:42:17.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:17.809+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:42:18.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.011+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:18.022+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:42:18.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.115+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:18.125+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:18.125+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:42:18.143+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.346 seconds
[2025-07-18T10:42:59.987+0000] {processor.py:186} INFO - Started process (PID=261) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:42:59.988+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:42:59.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.990+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:43:00.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.320+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:00.326+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:43:00.411+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.410+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:00.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:00.420+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:43:00.439+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.458 seconds
[2025-07-18T10:43:32.357+0000] {processor.py:186} INFO - Started process (PID=397) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:43:32.358+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:43:32.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.360+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:43:32.699+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.699+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:32.707+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:43:32.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.817+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:32.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:32.828+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:43:32.849+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.497 seconds
[2025-07-18T10:44:03.455+0000] {processor.py:186} INFO - Started process (PID=535) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:44:03.456+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:44:03.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:03.457+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:44:03.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:03.760+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:03.768+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:44:03.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:03.851+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:03.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:03.859+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:44:03.878+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.428 seconds
[2025-07-18T10:44:34.218+0000] {processor.py:186} INFO - Started process (PID=669) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:44:34.219+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:44:34.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.221+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:44:34.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.430+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:34.438+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:44:34.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.545+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:34.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:34.557+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:44:34.578+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.366 seconds
[2025-07-18T10:45:05.143+0000] {processor.py:186} INFO - Started process (PID=807) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:45:05.144+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:45:05.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.146+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:45:05.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.348+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:05.358+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:45:05.467+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.467+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:05.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:05.478+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:45:05.501+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.364 seconds
[2025-07-18T10:45:35.753+0000] {processor.py:186} INFO - Started process (PID=943) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:45:35.754+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:45:35.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:35.759+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:45:35.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:35.958+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:35.967+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:45:36.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.060+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:36.069+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.069+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:45:36.087+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.339 seconds
[2025-07-18T10:46:06.316+0000] {processor.py:186} INFO - Started process (PID=1077) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:46:06.317+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:46:06.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:06.319+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:46:06.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:06.562+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:06.571+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:46:06.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:06.703+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:06.715+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:06.715+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:46:06.740+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.430 seconds
[2025-07-18T10:46:37.270+0000] {processor.py:186} INFO - Started process (PID=1215) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:46:37.272+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:46:37.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.275+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:46:37.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.507+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:37.517+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:46:37.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.631+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:37.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:37.643+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:46:37.664+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.401 seconds
[2025-07-18T10:47:07.742+0000] {processor.py:186} INFO - Started process (PID=1409) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:47:07.744+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:47:07.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.747+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:47:08.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.999+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:08.020+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:47:08.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:08.186+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:08.207+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:08.207+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:47:08.238+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.503 seconds
[2025-07-18T10:48:05.445+0000] {processor.py:186} INFO - Started process (PID=261) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:48:05.446+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:48:05.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.448+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:48:05.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.798+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:05.804+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:48:05.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.894+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:05.905+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:05.905+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:48:05.924+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.485 seconds
[2025-07-18T10:48:36.488+0000] {processor.py:186} INFO - Started process (PID=397) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:48:36.490+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:48:36.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.492+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:48:36.855+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.855+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:36.861+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:48:36.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.956+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:36.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:36.965+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:48:36.984+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.501 seconds
[2025-07-18T10:49:07.688+0000] {processor.py:186} INFO - Started process (PID=533) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:49:07.689+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:49:07.692+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:07.692+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:49:08.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.096+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:08.106+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:49:08.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.295+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:08.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:08.307+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:49:08.329+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.648 seconds
[2025-07-18T10:49:38.727+0000] {processor.py:186} INFO - Started process (PID=669) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:49:38.728+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:49:38.730+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.729+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:49:38.930+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:38.929+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:38.938+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:49:39.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.045+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:39.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.057+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:49:39.077+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.357 seconds
[2025-07-18T10:50:09.445+0000] {processor.py:186} INFO - Started process (PID=807) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:50:09.446+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:50:09.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:09.448+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:50:09.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:09.643+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:09.651+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:50:09.745+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:09.745+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:09.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:09.755+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:50:09.775+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.337 seconds
[2025-07-18T10:50:40.192+0000] {processor.py:186} INFO - Started process (PID=943) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:50:40.192+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:50:40.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:40.194+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:50:40.399+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:40.398+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:40.408+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:50:40.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:40.505+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:40.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:40.516+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:50:40.536+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.351 seconds
[2025-07-18T10:51:10.963+0000] {processor.py:186} INFO - Started process (PID=1079) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:51:10.964+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:51:10.967+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.966+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:51:11.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:11.174+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:11.181+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:51:11.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:11.285+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:11.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:11.296+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:51:11.316+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.359 seconds
[2025-07-18T10:51:41.405+0000] {processor.py:186} INFO - Started process (PID=1216) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:51:41.406+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:51:41.409+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.409+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:51:41.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.596+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:41.605+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:51:41.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.702+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:41.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:41.714+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:51:41.733+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.333 seconds
[2025-07-18T10:52:11.822+0000] {processor.py:186} INFO - Started process (PID=1350) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:52:11.823+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:52:11.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:11.825+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:52:12.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.028+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:12.037+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:52:12.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.134+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:12.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:12.145+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:52:12.167+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.351 seconds
[2025-07-18T10:52:42.501+0000] {processor.py:186} INFO - Started process (PID=1486) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:52:42.502+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:52:42.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.505+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:52:42.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.700+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:42.708+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:52:42.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.810+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:42.823+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:42.822+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:52:42.844+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.349 seconds
[2025-07-18T10:53:13.024+0000] {processor.py:186} INFO - Started process (PID=1622) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:53:13.025+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:53:13.027+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.027+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:53:13.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.223+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:13.233+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:53:13.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.346+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:13.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:13.359+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:53:13.379+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.362 seconds
[2025-07-18T10:53:43.928+0000] {processor.py:186} INFO - Started process (PID=1760) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:53:43.929+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:53:43.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.931+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:53:44.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.105+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:44.113+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:53:44.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.201+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:44.211+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.211+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:53:44.228+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.306 seconds
[2025-07-18T10:54:15.050+0000] {processor.py:186} INFO - Started process (PID=1896) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:54:15.051+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:54:15.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:15.053+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:54:15.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:15.270+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:15.280+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:54:15.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:15.384+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:15.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:15.394+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:54:15.412+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.368 seconds
[2025-07-18T10:54:45.546+0000] {processor.py:186} INFO - Started process (PID=2032) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:54:45.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:54:45.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.549+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:54:45.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.746+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:45.754+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:54:45.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.856+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:45.868+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:45.868+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:54:45.889+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.349 seconds
[2025-07-18T10:55:15.983+0000] {processor.py:186} INFO - Started process (PID=2168) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:55:15.984+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:55:15.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.987+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:55:16.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:16.166+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:16.174+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:55:16.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:16.262+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:16.273+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:16.272+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:55:16.291+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.313 seconds
[2025-07-18T10:55:46.600+0000] {processor.py:186} INFO - Started process (PID=2366) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:55:46.601+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:55:46.604+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.604+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:55:46.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.956+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:46.972+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:55:47.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:47.163+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:47.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:47.178+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:55:47.221+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.631 seconds
[2025-07-18T10:57:30.061+0000] {processor.py:186} INFO - Started process (PID=261) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:57:30.062+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:57:30.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.064+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:57:30.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.386+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:30.393+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:57:30.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.475+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:30.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:30.489+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:57:30.505+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.450 seconds
[2025-07-18T10:58:01.729+0000] {processor.py:186} INFO - Started process (PID=397) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:58:01.730+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:58:01.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.732+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:58:02.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.036+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:02.044+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:58:02.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.133+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:02.141+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.141+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:58:02.162+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.438 seconds
[2025-07-18T10:58:32.858+0000] {processor.py:186} INFO - Started process (PID=533) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:58:32.859+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:58:32.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:32.861+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:58:33.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:33.194+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:33.202+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:58:33.286+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:33.286+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:33.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:33.296+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:58:33.313+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.462 seconds
[2025-07-18T10:59:03.377+0000] {processor.py:186} INFO - Started process (PID=666) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:59:03.378+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:59:03.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:03.380+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:59:03.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:03.575+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:03.581+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:59:03.674+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:03.673+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:03.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:03.682+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:59:03.700+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.328 seconds
[2025-07-18T10:59:33.889+0000] {processor.py:186} INFO - Started process (PID=800) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:59:33.890+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T10:59:33.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.892+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:59:34.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.085+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:34.094+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T10:59:34.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.193+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:34.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:34.205+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T10:59:34.223+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.340 seconds
[2025-07-18T11:00:04.703+0000] {processor.py:186} INFO - Started process (PID=936) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:00:04.704+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:00:04.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.706+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:00:04.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.904+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:04.912+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:00:05.033+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.033+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:05.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.044+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:00:05.061+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.366 seconds
[2025-07-18T11:00:35.801+0000] {processor.py:186} INFO - Started process (PID=1077) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:00:35.802+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:00:35.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:35.804+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:00:35.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:35.998+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:36.005+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:00:36.101+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.100+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:36.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.111+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:00:36.128+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.332 seconds
[2025-07-18T11:01:06.248+0000] {processor.py:186} INFO - Started process (PID=1213) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:01:06.249+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:01:06.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.252+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:01:06.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.459+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:06.468+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:01:06.588+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.587+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:06.600+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:06.600+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:01:06.621+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.380 seconds
[2025-07-18T11:01:37.601+0000] {processor.py:186} INFO - Started process (PID=1351) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:01:37.602+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:01:37.604+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.604+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:01:37.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.795+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:37.805+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:01:37.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.896+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:37.907+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.906+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:01:37.927+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.332 seconds
[2025-07-18T11:02:08.988+0000] {processor.py:186} INFO - Started process (PID=1487) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:02:08.989+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:02:08.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:08.991+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:02:09.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.180+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:09.186+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:02:09.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.271+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:09.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:09.282+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:02:09.300+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.318 seconds
[2025-07-18T11:02:39.612+0000] {processor.py:186} INFO - Started process (PID=1622) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:02:39.613+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:02:39.617+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:39.616+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:02:39.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:39.808+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:39.817+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:02:39.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:39.913+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:39.925+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:39.925+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:02:39.946+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.339 seconds
[2025-07-18T11:03:10.177+0000] {processor.py:186} INFO - Started process (PID=1759) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:03:10.178+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:03:10.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.180+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:03:10.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.416+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:10.425+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:03:10.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.525+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:10.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:10.537+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:03:10.557+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.387 seconds
[2025-07-18T11:03:41.260+0000] {processor.py:186} INFO - Started process (PID=1895) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:03:41.261+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:03:41.264+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.264+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:03:41.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.463+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:41.472+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:03:41.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.578+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:41.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:41.589+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:03:41.610+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.356 seconds
[2025-07-18T11:04:12.087+0000] {processor.py:186} INFO - Started process (PID=2031) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:04:12.088+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:04:12.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:12.090+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:04:12.305+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:12.305+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:12.314+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:04:12.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:12.429+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:12.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:12.441+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:04:12.465+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.384 seconds
[2025-07-18T11:04:42.698+0000] {processor.py:186} INFO - Started process (PID=2167) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:04:42.699+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:04:42.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:42.702+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:04:42.913+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:42.913+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:42.921+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:04:43.031+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.031+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:43.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.045+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:04:43.065+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.373 seconds
[2025-07-18T11:05:13.444+0000] {processor.py:186} INFO - Started process (PID=2303) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:05:13.445+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:05:13.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.447+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:05:13.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.677+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:13.687+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:05:13.792+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.791+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:13.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:13.803+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:05:13.825+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.386 seconds
[2025-07-18T11:06:52.720+0000] {processor.py:186} INFO - Started process (PID=273) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:06:52.722+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:06:52.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.724+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:06:53.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:53.072+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:53.078+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:06:53.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:53.174+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:53.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:53.184+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:06:53.199+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.486 seconds
[2025-07-18T11:07:24.012+0000] {processor.py:186} INFO - Started process (PID=414) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:07:24.013+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:07:24.015+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:24.015+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:07:24.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:24.326+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:24.333+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:07:24.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:24.418+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:24.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:24.427+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:07:24.446+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.440 seconds
[2025-07-18T11:07:55.283+0000] {processor.py:186} INFO - Started process (PID=555) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:07:55.284+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:07:55.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.286+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:07:55.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.497+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:55.507+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:07:55.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.610+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:55.622+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:55.621+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:07:55.639+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.364 seconds
[2025-07-18T11:08:26.217+0000] {processor.py:186} INFO - Started process (PID=696) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:08:26.218+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:08:26.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.220+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:08:26.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.442+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:26.450+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:08:26.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.548+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:26.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:26.558+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:08:26.577+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.367 seconds
[2025-07-18T11:08:57.043+0000] {processor.py:186} INFO - Started process (PID=839) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:08:57.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:08:57.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.046+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:08:57.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.233+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:57.241+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:08:57.336+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.336+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:57.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:57.348+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:08:57.367+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.330 seconds
[2025-07-18T11:09:28.059+0000] {processor.py:186} INFO - Started process (PID=978) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:09:28.060+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:09:28.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.062+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:09:28.266+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.266+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:28.275+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:09:28.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.374+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:28.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:28.386+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:09:28.406+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.354 seconds
[2025-07-18T11:09:58.581+0000] {processor.py:186} INFO - Started process (PID=1119) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:09:58.582+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:09:58.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.585+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:09:58.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.818+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:58.826+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:09:58.939+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.938+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:58.950+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:58.950+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:09:58.974+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.399 seconds
[2025-07-18T11:10:29.224+0000] {processor.py:186} INFO - Started process (PID=1260) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:10:29.225+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:10:29.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.227+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:10:29.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.441+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:29.449+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:10:29.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.548+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:29.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:29.559+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:10:29.581+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.363 seconds
[2025-07-18T11:11:00.065+0000] {processor.py:186} INFO - Started process (PID=1401) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:11:00.066+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:11:00.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.068+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:11:00.265+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.265+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:00.272+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:11:00.364+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.363+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:00.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:00.372+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:11:00.387+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.328 seconds
[2025-07-18T11:11:31.177+0000] {processor.py:186} INFO - Started process (PID=1544) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:11:31.178+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:11:31.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.180+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:11:31.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.363+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:31.371+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:11:31.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.465+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:31.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:31.476+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:11:31.496+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.325 seconds
[2025-07-18T11:12:01.992+0000] {processor.py:186} INFO - Started process (PID=1685) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:12:01.993+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:12:01.996+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.996+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:12:02.190+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:02.190+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:02.199+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:12:02.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:02.287+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:02.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:02.297+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:12:02.315+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.331 seconds
[2025-07-18T11:12:32.455+0000] {processor.py:186} INFO - Started process (PID=1824) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:12:32.456+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:12:32.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.458+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:12:32.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.659+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:32.668+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:12:32.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.776+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:32.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:32.788+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:12:32.806+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.357 seconds
[2025-07-18T11:13:04.267+0000] {processor.py:186} INFO - Started process (PID=1965) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:13:04.268+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:13:04.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.270+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:13:04.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.461+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:04.472+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:13:04.568+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.568+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:04.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:04.579+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:13:04.600+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.339 seconds
[2025-07-18T11:13:35.101+0000] {processor.py:186} INFO - Started process (PID=2108) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:13:35.102+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:13:35.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.104+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:13:35.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.299+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:35.307+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:13:35.409+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.409+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:35.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:35.418+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:13:35.436+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.342 seconds
[2025-07-18T11:14:05.751+0000] {processor.py:186} INFO - Started process (PID=2247) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:14:05.752+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:14:05.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.754+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:14:05.950+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.949+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:05.955+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:14:06.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:06.059+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:06.069+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:06.069+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:14:06.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.345 seconds
[2025-07-18T11:14:36.266+0000] {processor.py:186} INFO - Started process (PID=2388) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:14:36.267+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:14:36.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:36.270+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:14:36.616+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:36.616+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:36.623+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:14:36.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:36.728+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:36.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:36.739+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:14:36.758+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.501 seconds
[2025-07-18T11:15:07.028+0000] {processor.py:186} INFO - Started process (PID=2529) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:15:07.029+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:15:07.031+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:07.031+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:15:07.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:07.240+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:07.248+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:15:07.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:07.341+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:07.353+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:07.353+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:15:07.373+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.352 seconds
[2025-07-18T11:15:38.415+0000] {processor.py:186} INFO - Started process (PID=2672) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:15:38.416+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:15:38.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.418+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:15:38.625+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.625+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:38.634+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:15:38.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.737+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:38.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:38.749+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:15:38.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.360 seconds
[2025-07-18T11:16:08.950+0000] {processor.py:186} INFO - Started process (PID=2813) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:16:08.951+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:16:08.954+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.954+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:16:09.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:09.161+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:09.170+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:16:09.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:09.276+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:09.289+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:09.288+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:16:09.309+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.364 seconds
[2025-07-18T11:16:39.491+0000] {processor.py:186} INFO - Started process (PID=2954) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:16:39.492+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:16:39.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.494+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:16:39.686+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.686+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:39.694+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:16:39.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.785+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:39.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:39.796+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:16:39.813+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.328 seconds
[2025-07-18T11:17:11.020+0000] {processor.py:186} INFO - Started process (PID=3095) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:17:11.021+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:17:11.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:11.024+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:17:11.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:11.246+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:11.256+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:17:11.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:11.372+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:11.382+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:11.382+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:17:11.404+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.390 seconds
[2025-07-18T11:17:41.763+0000] {processor.py:186} INFO - Started process (PID=3236) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:17:41.764+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:17:41.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:41.765+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:17:41.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:41.974+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:41.985+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:17:42.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.091+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:42.101+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:42.101+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:17:42.120+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.363 seconds
[2025-07-18T11:18:12.536+0000] {processor.py:186} INFO - Started process (PID=3377) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:18:12.537+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:18:12.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.540+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:18:12.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.750+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:12.759+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:18:12.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.870+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:12.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:12.883+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:18:12.907+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.377 seconds
[2025-07-18T11:18:43.179+0000] {processor.py:186} INFO - Started process (PID=3518) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:18:43.180+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:18:43.182+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.182+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:18:43.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.369+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:43.378+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:18:43.473+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.473+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:43.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:43.484+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:18:43.503+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.330 seconds
[2025-07-18T11:19:14.345+0000] {processor.py:186} INFO - Started process (PID=3659) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:19:14.346+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:19:14.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:14.348+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:19:14.666+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:14.665+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:14.676+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:19:14.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:14.839+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:14.857+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:14.857+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:19:14.888+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.552 seconds
[2025-07-18T11:19:45.181+0000] {processor.py:186} INFO - Started process (PID=3805) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:19:45.182+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:19:45.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.184+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:19:45.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.417+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:45.426+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:19:45.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.531+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:45.542+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:45.542+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:19:45.564+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.390 seconds
[2025-07-18T11:20:16.025+0000] {processor.py:186} INFO - Started process (PID=3946) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:20:16.027+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:20:16.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:16.029+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:20:16.266+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:16.265+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:16.276+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:20:16.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:16.389+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:16.402+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:16.402+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:20:16.423+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.407 seconds
