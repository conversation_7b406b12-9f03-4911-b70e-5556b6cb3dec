[2025-07-18T10:16:49.533+0000] {processor.py:186} INFO - Started process (PID=195) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:16:49.537+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:16:49.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.540+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:16:49.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.628+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:49.634+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:16:49.659+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.139 seconds
[2025-07-18T10:17:19.927+0000] {processor.py:186} INFO - Started process (PID=334) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:17:19.927+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:17:19.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:19.929+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:17:19.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:19.957+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:19.961+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:17:19.978+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.057 seconds
[2025-07-18T10:17:50.302+0000] {processor.py:186} INFO - Started process (PID=465) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:17:50.303+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:17:50.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.304+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:17:50.336+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.332+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:50.337+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:17:50.355+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.060 seconds
[2025-07-18T10:18:21.002+0000] {processor.py:186} INFO - Started process (PID=594) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:18:21.003+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:18:21.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.004+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:18:21.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.033+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.037+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:18:21.054+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.057 seconds
[2025-07-18T10:18:51.971+0000] {processor.py:186} INFO - Started process (PID=727) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:18:51.972+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:18:51.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:51.973+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:18:52.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.002+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:52.006+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:18:52.021+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.056 seconds
[2025-07-18T10:19:22.825+0000] {processor.py:186} INFO - Started process (PID=856) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:19:22.826+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:19:22.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:22.827+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:19:22.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:22.858+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:22.862+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:19:22.880+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.061 seconds
[2025-07-18T10:19:53.858+0000] {processor.py:186} INFO - Started process (PID=989) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:19:53.859+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:19:53.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:53.860+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:19:53.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:53.907+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:53.914+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:19:53.930+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.081 seconds
[2025-07-18T10:20:24.897+0000] {processor.py:186} INFO - Started process (PID=1120) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:20:24.898+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:20:24.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:24.899+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:20:24.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:24.934+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:24.938+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:20:24.956+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.065 seconds
[2025-07-18T10:20:55.862+0000] {processor.py:186} INFO - Started process (PID=1251) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:20:55.863+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:20:55.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:55.864+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:20:55.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:55.893+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:55.897+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:20:55.913+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.057 seconds
[2025-07-18T10:21:26.833+0000] {processor.py:186} INFO - Started process (PID=1382) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:21:26.834+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:21:26.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:26.835+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:21:26.867+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:26.865+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:26.868+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:21:26.885+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.058 seconds
[2025-07-18T10:21:56.990+0000] {processor.py:186} INFO - Started process (PID=1513) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:21:56.991+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:21:56.992+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:56.992+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:21:57.023+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.020+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:57.024+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:21:57.039+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.054 seconds
[2025-07-18T10:22:28.042+0000] {processor.py:186} INFO - Started process (PID=1644) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:22:28.043+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:22:28.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.044+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:22:28.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.072+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:28.075+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:22:28.090+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.053 seconds
[2025-07-18T10:22:59.030+0000] {processor.py:186} INFO - Started process (PID=1775) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:22:59.031+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:22:59.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.032+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:22:59.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.060+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:59.064+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:22:59.080+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.056 seconds
[2025-07-18T10:23:29.966+0000] {processor.py:186} INFO - Started process (PID=1906) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:23:29.967+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:23:29.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:29.968+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:23:30.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:29.998+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:30.002+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:23:30.018+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.058 seconds
[2025-07-18T10:24:00.811+0000] {processor.py:186} INFO - Started process (PID=2037) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:24:00.812+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:24:00.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:00.813+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:24:00.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:00.846+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:00.850+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:24:00.867+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.062 seconds
[2025-07-18T10:24:31.049+0000] {processor.py:186} INFO - Started process (PID=2168) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:24:31.050+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:24:31.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.051+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:24:31.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.081+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.084+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:24:31.100+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.057 seconds
[2025-07-18T10:25:02.866+0000] {processor.py:186} INFO - Started process (PID=2299) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:25:02.867+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:25:02.868+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:02.867+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:25:03.049+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.049+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:03.058+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:25:03.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.144+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:03.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.155+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:25:03.175+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.315 seconds
[2025-07-18T10:26:19.975+0000] {processor.py:186} INFO - Started process (PID=203) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:26:19.977+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:26:19.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:19.979+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:26:20.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:20.351+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:20.359+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:26:20.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:20.458+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:20.467+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:20.467+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:26:20.485+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.517 seconds
[2025-07-18T10:26:51.021+0000] {processor.py:186} INFO - Started process (PID=334) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:26:51.022+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:26:51.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:51.024+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:26:51.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:51.407+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:51.414+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:26:51.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:51.557+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:51.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:51.575+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:26:51.630+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.614 seconds
[2025-07-18T10:27:22.028+0000] {processor.py:186} INFO - Started process (PID=465) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:27:22.029+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:27:22.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:22.031+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:27:22.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:22.375+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:22.382+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:27:22.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:22.489+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:22.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:22.500+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:27:22.520+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.498 seconds
[2025-07-18T10:27:52.815+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:27:52.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:27:52.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:52.818+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:27:53.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:53.004+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:53.014+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:27:53.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:53.105+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:53.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:53.116+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:27:53.135+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.325 seconds
[2025-07-18T10:28:23.438+0000] {processor.py:186} INFO - Started process (PID=727) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:28:23.438+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:28:23.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:23.440+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:28:23.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:23.686+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:23.694+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:28:23.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:23.796+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:23.807+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:23.807+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:28:23.827+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.395 seconds
[2025-07-18T10:28:53.897+0000] {processor.py:186} INFO - Started process (PID=858) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:28:53.898+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:28:53.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:53.901+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:28:54.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:54.093+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:54.104+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:28:54.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:54.204+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:54.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:54.215+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:28:54.237+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.345 seconds
[2025-07-18T10:29:24.493+0000] {processor.py:186} INFO - Started process (PID=989) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:29:24.494+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:29:24.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:24.496+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:29:24.689+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:24.689+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:24.696+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:29:24.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:24.788+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:24.799+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:24.799+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:29:24.818+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.331 seconds
[2025-07-18T10:29:55.023+0000] {processor.py:186} INFO - Started process (PID=1120) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:29:55.023+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:29:55.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:55.025+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:29:55.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:55.219+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:55.228+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:29:55.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:55.319+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:55.328+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:55.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:29:55.350+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.333 seconds
[2025-07-18T10:30:25.714+0000] {processor.py:186} INFO - Started process (PID=1251) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:30:25.715+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:30:25.718+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:25.718+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:30:25.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:25.923+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:25.932+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:30:26.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:26.033+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:26.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:26.044+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:30:26.061+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.355 seconds
[2025-07-18T10:30:56.204+0000] {processor.py:186} INFO - Started process (PID=1382) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:30:56.204+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:30:56.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:56.207+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:30:56.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:56.421+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:56.431+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:30:56.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:56.533+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:56.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:56.543+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:30:56.563+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.365 seconds
[2025-07-18T10:31:27.153+0000] {processor.py:186} INFO - Started process (PID=1513) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:31:27.154+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:31:27.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:27.156+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:31:27.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:27.345+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:27.355+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:31:27.450+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:27.450+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:27.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:27.459+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:31:27.478+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.330 seconds
[2025-07-18T10:31:57.755+0000] {processor.py:186} INFO - Started process (PID=1644) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:31:57.756+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:31:57.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:57.759+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:31:57.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:57.982+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:57.990+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:31:58.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:58.088+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:58.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:58.099+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:31:58.120+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.373 seconds
[2025-07-18T10:32:28.237+0000] {processor.py:186} INFO - Started process (PID=1775) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:32:28.237+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:32:28.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:28.239+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:32:28.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:28.422+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:28.432+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:32:28.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:28.524+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:28.535+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:28.534+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:32:28.553+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.322 seconds
[2025-07-18T10:32:58.671+0000] {processor.py:186} INFO - Started process (PID=1906) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:32:58.672+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:32:58.674+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:58.674+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:32:58.857+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:58.857+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:58.867+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:32:58.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:58.969+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:58.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:58.980+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:32:58.999+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.333 seconds
[2025-07-18T10:33:29.299+0000] {processor.py:186} INFO - Started process (PID=2037) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:33:29.300+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:33:29.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:29.302+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:33:29.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:29.500+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:29.508+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:33:29.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:29.598+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:29.610+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:29.610+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:33:29.630+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.337 seconds
[2025-07-18T10:33:59.841+0000] {processor.py:186} INFO - Started process (PID=2168) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:33:59.842+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:33:59.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:59.844+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:34:00.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:00.045+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:00.056+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:34:00.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:00.150+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:00.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:00.160+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:34:00.180+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.344 seconds
[2025-07-18T10:34:30.562+0000] {processor.py:186} INFO - Started process (PID=2297) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:34:30.563+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:34:30.566+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:30.566+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:34:30.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:30.800+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:30.809+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:34:30.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:30.928+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:30.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:30.940+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:34:30.960+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.408 seconds
[2025-07-18T10:35:01.402+0000] {processor.py:186} INFO - Started process (PID=2428) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:35:01.404+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:35:01.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:01.406+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:35:01.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:01.621+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:01.630+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:35:01.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:01.735+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:01.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:01.746+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:35:01.770+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.380 seconds
[2025-07-18T10:35:31.985+0000] {processor.py:186} INFO - Started process (PID=2559) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:35:31.986+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:35:31.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:31.988+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:35:32.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:32.223+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:32.234+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:35:32.401+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:32.400+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:32.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:32.415+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:35:32.437+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.459 seconds
[2025-07-18T10:36:02.849+0000] {processor.py:186} INFO - Started process (PID=2690) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:36:02.850+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:36:02.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:02.852+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:36:03.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:03.057+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:03.064+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:36:03.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:03.159+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:03.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:03.169+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:36:03.190+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.347 seconds
[2025-07-18T10:36:33.791+0000] {processor.py:186} INFO - Started process (PID=2821) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:36:33.792+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:36:33.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:33.794+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:36:34.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:34.040+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:34.048+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:36:34.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:34.154+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:34.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:34.164+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:36:34.181+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.397 seconds
[2025-07-18T10:37:04.684+0000] {processor.py:186} INFO - Started process (PID=2952) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:37:04.685+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:37:04.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:04.687+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:37:04.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:04.871+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:04.880+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:37:04.981+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:04.981+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:04.992+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:04.992+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:37:05.012+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.334 seconds
[2025-07-18T10:37:35.569+0000] {processor.py:186} INFO - Started process (PID=3083) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:37:35.570+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:37:35.572+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:35.572+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:37:35.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:35.795+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:35.804+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:37:35.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:35.901+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:35.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:35.911+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:37:35.931+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.368 seconds
[2025-07-18T10:38:06.380+0000] {processor.py:186} INFO - Started process (PID=3214) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:38:06.381+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:38:06.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:06.383+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:38:06.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:06.587+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:06.596+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:38:06.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:06.733+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:06.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:06.747+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:38:06.768+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.395 seconds
[2025-07-18T10:38:37.275+0000] {processor.py:186} INFO - Started process (PID=3345) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:38:37.276+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:38:37.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:37.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:38:37.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:37.479+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:37.485+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:38:37.603+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:37.602+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:37.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:37.615+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:38:37.635+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.367 seconds
[2025-07-18T10:39:08.017+0000] {processor.py:186} INFO - Started process (PID=3478) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:39:08.017+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:39:08.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:08.019+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:39:08.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:08.194+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:08.203+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:39:08.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:08.286+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:08.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:08.297+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:39:08.316+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.305 seconds
[2025-07-18T10:39:38.588+0000] {processor.py:186} INFO - Started process (PID=3607) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:39:38.589+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:39:38.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:38.592+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:39:38.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:38.824+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:38.835+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:39:38.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:38.941+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:38.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:38.955+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:39:38.976+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.394 seconds
[2025-07-18T10:40:09.338+0000] {processor.py:186} INFO - Started process (PID=3738) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:40:09.339+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:40:09.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:09.341+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:40:09.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:09.541+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:09.548+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:40:09.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:09.650+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:09.666+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:09.665+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:40:09.696+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.365 seconds
[2025-07-18T10:40:40.215+0000] {processor.py:186} INFO - Started process (PID=3869) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:40:40.216+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:40:40.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:40.219+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:40:40.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:40.407+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:40.416+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:40:40.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:40.506+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:40.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:40.647+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:40:40.665+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.456 seconds
[2025-07-18T10:41:11.046+0000] {processor.py:186} INFO - Started process (PID=4000) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:41:11.047+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:41:11.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:11.049+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:41:11.266+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:11.265+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:11.274+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:41:11.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:11.525+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:11.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:11.535+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:41:11.555+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.515 seconds
[2025-07-18T10:41:41.645+0000] {processor.py:186} INFO - Started process (PID=4136) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:41:41.646+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:41:41.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:41.649+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:41:41.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:41.864+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:41.873+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:41:42.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:42.145+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:42.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:42.155+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:41:42.173+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.534 seconds
[2025-07-18T10:42:12.650+0000] {processor.py:186} INFO - Started process (PID=4272) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:42:12.651+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:42:12.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:12.653+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:42:12.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:12.904+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:12.912+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:42:13.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:13.163+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:13.173+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:13.172+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:42:13.188+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.545 seconds
[2025-07-18T10:42:57.168+0000] {processor.py:186} INFO - Started process (PID=200) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:42:57.169+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:42:57.171+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.171+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:42:57.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.561+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:57.566+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:42:57.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.659+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:57.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:57.671+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:42:57.691+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.528 seconds
[2025-07-18T10:43:27.877+0000] {processor.py:186} INFO - Started process (PID=342) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:43:27.878+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:43:27.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:27.880+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:43:28.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:28.242+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:28.249+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:43:28.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:28.336+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:28.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:28.346+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:43:28.363+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.493 seconds
[2025-07-18T10:43:59.013+0000] {processor.py:186} INFO - Started process (PID=478) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:43:59.014+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:43:59.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.016+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:43:59.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.351+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:59.357+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:43:59.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.451+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:59.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:59.461+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:43:59.480+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.472 seconds
[2025-07-18T10:44:29.927+0000] {processor.py:186} INFO - Started process (PID=614) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:44:29.928+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:44:29.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:29.929+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:44:30.118+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.118+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:30.126+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:44:30.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.227+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:30.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:30.238+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:44:30.257+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.335 seconds
[2025-07-18T10:45:00.936+0000] {processor.py:186} INFO - Started process (PID=750) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:45:00.936+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:45:00.939+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:00.938+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:45:01.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.142+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:01.152+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:45:01.259+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.259+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:01.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:01.272+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:45:01.296+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.366 seconds
[2025-07-18T10:45:31.458+0000] {processor.py:186} INFO - Started process (PID=886) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:45:31.459+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:45:31.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:31.461+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:45:31.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:31.653+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:31.662+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:45:31.757+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:31.756+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:31.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:31.767+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:45:31.787+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.335 seconds
[2025-07-18T10:46:02.373+0000] {processor.py:186} INFO - Started process (PID=1022) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:46:02.374+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:46:02.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:02.376+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:46:02.594+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:02.594+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:02.604+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:46:02.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:02.719+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:02.730+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:02.730+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:46:02.750+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.382 seconds
[2025-07-18T10:46:33.123+0000] {processor.py:186} INFO - Started process (PID=1158) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:46:33.124+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:46:33.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:33.126+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:46:33.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:33.359+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:33.370+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:46:33.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:33.492+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:33.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:33.504+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:46:33.527+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.411 seconds
[2025-07-18T10:47:03.926+0000] {processor.py:186} INFO - Started process (PID=1294) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:47:03.927+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:47:03.930+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:03.929+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:47:04.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:04.129+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:04.138+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:47:04.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:04.236+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:04.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:04.250+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:47:04.271+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.351 seconds
[2025-07-18T10:48:02.567+0000] {processor.py:186} INFO - Started process (PID=206) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:48:02.568+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:48:02.571+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:02.570+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:48:02.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:02.931+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:02.938+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:48:03.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.029+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:03.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.039+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:48:03.059+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.499 seconds
[2025-07-18T10:48:33.192+0000] {processor.py:186} INFO - Started process (PID=342) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:48:33.193+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:48:33.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:33.195+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:48:33.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:33.579+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:33.587+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:48:33.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:33.694+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:33.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:33.706+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:48:33.724+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.538 seconds
[2025-07-18T10:49:04.477+0000] {processor.py:186} INFO - Started process (PID=478) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:49:04.478+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:49:04.481+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.480+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:49:04.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.806+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:04.812+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:49:04.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.905+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:04.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:04.915+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:49:04.939+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.467 seconds
[2025-07-18T10:49:35.688+0000] {processor.py:186} INFO - Started process (PID=616) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:49:35.690+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:49:35.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:35.692+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:49:35.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:35.881+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:35.891+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:49:35.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:35.985+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:35.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:35.995+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:49:36.016+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.334 seconds
[2025-07-18T10:50:06.147+0000] {processor.py:186} INFO - Started process (PID=750) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:50:06.148+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:50:06.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.151+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:50:06.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.366+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:06.376+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:50:06.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.478+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:06.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:06.489+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:50:06.511+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.371 seconds
[2025-07-18T10:50:36.980+0000] {processor.py:186} INFO - Started process (PID=888) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:50:36.981+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:50:36.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:36.984+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:50:37.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:37.176+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:37.185+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:50:37.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:37.286+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:37.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:37.297+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:50:37.317+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.342 seconds
[2025-07-18T10:51:07.721+0000] {processor.py:186} INFO - Started process (PID=1024) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:51:07.722+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:51:07.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:07.724+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:51:07.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:07.906+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:07.915+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:51:08.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.011+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:08.021+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.021+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:51:08.043+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.327 seconds
[2025-07-18T10:51:38.233+0000] {processor.py:186} INFO - Started process (PID=1161) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:51:38.235+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:51:38.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.237+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:51:38.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.475+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:38.483+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:51:38.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.588+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:38.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:38.598+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:51:38.618+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.394 seconds
[2025-07-18T10:52:08.724+0000] {processor.py:186} INFO - Started process (PID=1297) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:52:08.726+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:52:08.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:08.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:52:08.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:08.931+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:08.938+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:52:09.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.038+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:09.049+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.049+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:52:09.071+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.352 seconds
[2025-07-18T10:52:39.233+0000] {processor.py:186} INFO - Started process (PID=1433) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:52:39.234+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:52:39.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.236+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:52:39.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.446+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:39.455+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:52:39.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.549+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:39.560+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:39.560+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:52:39.579+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.352 seconds
[2025-07-18T10:53:09.860+0000] {processor.py:186} INFO - Started process (PID=1569) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:53:09.861+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:53:09.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:09.864+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:53:10.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.064+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:10.074+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:53:10.172+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.171+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:10.182+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:10.182+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:53:10.200+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.346 seconds
[2025-07-18T10:53:40.376+0000] {processor.py:186} INFO - Started process (PID=1705) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:53:40.377+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:53:40.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.379+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:53:40.565+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.564+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:40.574+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:53:40.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.661+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:40.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:40.670+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:53:40.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.316 seconds
[2025-07-18T10:54:11.073+0000] {processor.py:186} INFO - Started process (PID=1841) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:54:11.075+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:54:11.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.077+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:54:11.355+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.355+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:11.362+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:54:11.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.461+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:11.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.473+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:54:11.496+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.430 seconds
[2025-07-18T10:54:41.813+0000] {processor.py:186} INFO - Started process (PID=1977) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:54:41.814+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:54:41.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:41.816+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:54:42.013+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.012+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:42.022+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:54:42.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.120+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:42.131+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.131+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:54:42.151+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.346 seconds
[2025-07-18T10:55:12.442+0000] {processor.py:186} INFO - Started process (PID=2113) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:55:12.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:55:12.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.445+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:55:12.646+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.646+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:12.655+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:55:12.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.741+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:12.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:12.751+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:55:12.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.332 seconds
[2025-07-18T10:55:43.317+0000] {processor.py:186} INFO - Started process (PID=2249) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:55:43.318+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:55:43.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.320+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:55:43.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.496+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:43.503+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:55:43.594+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.593+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:43.604+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:43.604+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:55:43.621+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.309 seconds
[2025-07-18T10:57:27.320+0000] {processor.py:186} INFO - Started process (PID=200) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:57:27.321+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:57:27.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.324+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:57:27.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.687+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:27.695+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:57:27.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.797+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:27.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:27.808+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:57:27.826+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.512 seconds
[2025-07-18T10:57:58.075+0000] {processor.py:186} INFO - Started process (PID=342) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:57:58.076+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:57:58.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:58.077+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:57:58.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:58.479+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:58.484+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:57:58.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:58.569+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:58.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:58.579+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:57:58.596+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.527 seconds
[2025-07-18T10:58:28.719+0000] {processor.py:186} INFO - Started process (PID=478) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:58:28.720+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:58:28.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:28.722+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:58:29.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:29.053+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:29.060+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:58:29.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:29.152+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:29.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:29.161+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:58:29.178+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.465 seconds
[2025-07-18T10:58:59.794+0000] {processor.py:186} INFO - Started process (PID=614) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:58:59.795+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:58:59.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:59.796+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:58:59.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:59.975+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:59.984+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:59:00.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:00.086+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:00.098+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:00.098+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:59:00.113+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.325 seconds
[2025-07-18T10:59:30.694+0000] {processor.py:186} INFO - Started process (PID=750) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:59:30.695+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T10:59:30.697+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:30.697+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:59:30.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:30.882+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:30.890+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T10:59:30.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:30.983+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:30.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:30.995+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T10:59:31.015+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.327 seconds
[2025-07-18T11:00:01.242+0000] {processor.py:186} INFO - Started process (PID=886) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:00:01.243+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:00:01.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:01.246+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:00:01.469+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:01.468+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:01.478+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:00:01.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:01.591+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:01.604+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:01.604+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:00:01.624+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.390 seconds
[2025-07-18T11:00:32.035+0000] {processor.py:186} INFO - Started process (PID=1024) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:00:32.036+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:00:32.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:32.038+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:00:32.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:32.210+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:32.219+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:00:32.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:32.303+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:32.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:32.312+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:00:32.329+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.299 seconds
[2025-07-18T11:01:02.828+0000] {processor.py:186} INFO - Started process (PID=1160) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:01:02.829+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:01:02.831+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:02.830+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:01:03.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:03.047+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:03.058+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:01:03.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:03.170+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:03.183+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:03.183+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:01:03.208+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.386 seconds
[2025-07-18T11:01:33.375+0000] {processor.py:186} INFO - Started process (PID=1296) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:01:33.376+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:01:33.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.378+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:01:33.569+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.569+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:33.577+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:01:33.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.671+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:33.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:33.682+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:01:33.702+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.333 seconds
[2025-07-18T11:02:04.301+0000] {processor.py:186} INFO - Started process (PID=1430) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:02:04.302+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:02:04.305+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.305+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:02:04.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.499+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:04.508+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:02:04.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.610+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:04.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:04.624+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:02:04.648+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.353 seconds
[2025-07-18T11:02:35.204+0000] {processor.py:186} INFO - Started process (PID=1566) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:02:35.205+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:02:35.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.208+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:02:35.405+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.404+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:35.412+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:02:35.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.512+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:35.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:35.523+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:02:35.542+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.344 seconds
[2025-07-18T11:03:05.689+0000] {processor.py:186} INFO - Started process (PID=1702) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:03:05.690+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:03:05.692+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:05.692+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:03:05.907+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:05.906+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:05.916+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:03:06.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:06.031+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:06.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:06.044+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:03:06.065+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.382 seconds
[2025-07-18T11:03:36.348+0000] {processor.py:186} INFO - Started process (PID=1838) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:03:36.350+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:03:36.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.352+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:03:36.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.557+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:36.565+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:03:36.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.665+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:36.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:36.676+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:03:36.698+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.357 seconds
[2025-07-18T11:04:07.470+0000] {processor.py:186} INFO - Started process (PID=1976) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:04:07.471+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:04:07.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:07.474+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:04:07.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:07.684+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:07.692+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:04:07.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:07.817+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:07.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:07.830+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:04:07.855+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.392 seconds
[2025-07-18T11:04:38.105+0000] {processor.py:186} INFO - Started process (PID=2112) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:04:38.106+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:04:38.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.108+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:04:38.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.320+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:38.331+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:04:38.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.434+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:38.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:38.449+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:04:38.474+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.376 seconds
[2025-07-18T11:05:08.665+0000] {processor.py:186} INFO - Started process (PID=2248) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:05:08.666+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:05:08.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:08.669+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:05:08.913+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:08.913+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:08.920+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:05:09.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:09.053+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:09.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:09.071+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:05:09.100+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.442 seconds
[2025-07-18T11:05:39.173+0000] {processor.py:186} INFO - Started process (PID=2384) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:05:39.175+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:05:39.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:39.177+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:05:39.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:39.389+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:39.398+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:05:39.504+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:39.503+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:39.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:39.515+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:05:39.537+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.369 seconds
[2025-07-18T11:06:49.994+0000] {processor.py:186} INFO - Started process (PID=212) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:06:49.995+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:06:49.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:49.997+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:06:50.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.310+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:50.317+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:06:50.409+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.409+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:50.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.418+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:06:50.436+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.448 seconds
[2025-07-18T11:07:21.078+0000] {processor.py:186} INFO - Started process (PID=353) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:07:21.079+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:07:21.081+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:21.081+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:07:21.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:21.437+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:21.444+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:07:21.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:21.546+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:21.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:21.559+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:07:21.583+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.511 seconds
[2025-07-18T11:07:52.607+0000] {processor.py:186} INFO - Started process (PID=500) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:07:52.607+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:07:52.610+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:52.610+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:07:52.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:52.931+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:52.938+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:07:53.031+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.031+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:53.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.043+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:07:53.063+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.463 seconds
[2025-07-18T11:08:23.708+0000] {processor.py:186} INFO - Started process (PID=643) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:08:23.709+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:08:23.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.711+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:08:23.930+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:23.930+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:23.939+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:08:24.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.064+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:24.081+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.081+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:08:24.100+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.398 seconds
[2025-07-18T11:08:54.602+0000] {processor.py:186} INFO - Started process (PID=784) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:08:54.604+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:08:54.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.606+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:08:54.804+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.804+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:54.814+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:08:54.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.915+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:54.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:54.927+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:08:54.948+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.352 seconds
[2025-07-18T11:09:25.112+0000] {processor.py:186} INFO - Started process (PID=925) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:09:25.113+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:09:25.115+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.115+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:09:25.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.314+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:25.324+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:09:25.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.424+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:25.435+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.435+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:09:25.468+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.362 seconds
[2025-07-18T11:09:55.698+0000] {processor.py:186} INFO - Started process (PID=1066) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:09:55.699+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:09:55.701+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.701+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:09:55.887+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.887+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:55.894+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:09:55.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.986+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:55.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:55.998+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:09:56.018+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.327 seconds
[2025-07-18T11:10:26.614+0000] {processor.py:186} INFO - Started process (PID=1207) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:10:26.615+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:10:26.618+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.617+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:10:26.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.806+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:26.814+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:10:26.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.904+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:26.915+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:26.915+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:10:26.932+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.325 seconds
[2025-07-18T11:10:57.617+0000] {processor.py:186} INFO - Started process (PID=1348) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:10:57.618+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:10:57.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.620+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:10:57.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.809+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:57.818+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:10:57.917+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.917+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:57.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:57.927+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:10:57.945+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.334 seconds
[2025-07-18T11:11:28.137+0000] {processor.py:186} INFO - Started process (PID=1489) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:11:28.138+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:11:28.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.140+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:11:28.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.332+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:28.340+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:11:28.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.442+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:28.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:28.453+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:11:28.474+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.343 seconds
[2025-07-18T11:11:58.948+0000] {processor.py:186} INFO - Started process (PID=1628) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:11:58.949+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:11:58.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:58.951+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:11:59.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.138+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:59.147+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:11:59.241+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.241+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:59.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:59.252+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:11:59.268+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.325 seconds
[2025-07-18T11:12:29.861+0000] {processor.py:186} INFO - Started process (PID=1771) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:12:29.863+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:12:29.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:29.865+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:12:30.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.051+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:30.059+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:12:30.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.148+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:30.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.159+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:12:30.176+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.321 seconds
[2025-07-18T11:13:01.218+0000] {processor.py:186} INFO - Started process (PID=1912) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:13:01.219+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:13:01.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:01.221+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:13:01.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:01.418+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:01.426+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:13:01.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:01.523+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:01.534+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:01.533+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:13:01.553+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.341 seconds
[2025-07-18T11:13:31.737+0000] {processor.py:186} INFO - Started process (PID=2051) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:13:31.738+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:13:31.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:31.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:13:31.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:31.934+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:31.941+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:13:32.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:32.034+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:32.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:32.046+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:13:32.063+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.332 seconds
[2025-07-18T11:14:02.802+0000] {processor.py:186} INFO - Started process (PID=2194) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:14:02.803+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:14:02.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:02.806+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:14:03.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.001+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:03.011+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:14:03.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.115+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:03.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:03.126+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:14:03.146+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.351 seconds
[2025-07-18T11:14:33.386+0000] {processor.py:186} INFO - Started process (PID=2335) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:14:33.387+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:14:33.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:33.389+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:14:33.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:33.580+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:33.589+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:14:33.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:33.714+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:33.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:33.726+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:14:33.744+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.365 seconds
[2025-07-18T11:15:04.459+0000] {processor.py:186} INFO - Started process (PID=2476) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:15:04.460+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:15:04.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.462+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:15:04.660+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.659+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:04.668+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:15:04.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.768+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:04.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:04.778+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:15:04.799+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.347 seconds
[2025-07-18T11:15:35.319+0000] {processor.py:186} INFO - Started process (PID=2617) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:15:35.320+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:15:35.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:35.323+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:15:35.520+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:35.519+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:35.529+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:15:35.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:35.631+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:35.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:35.640+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:15:35.660+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.348 seconds
[2025-07-18T11:16:05.923+0000] {processor.py:186} INFO - Started process (PID=2758) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:16:05.924+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:16:05.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:05.926+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:16:06.136+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.136+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:06.145+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:16:06.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.236+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:06.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.247+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:16:06.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.351 seconds
[2025-07-18T11:16:36.715+0000] {processor.py:186} INFO - Started process (PID=2899) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:16:36.716+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:16:36.718+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.718+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:16:36.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:36.923+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:36.933+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:16:37.035+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.035+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:37.049+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.049+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:16:37.071+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.362 seconds
[2025-07-18T11:17:07.661+0000] {processor.py:186} INFO - Started process (PID=3040) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:17:07.663+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:17:07.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:07.669+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:17:07.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:07.916+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:07.925+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:17:08.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:08.056+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:08.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:08.073+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:17:08.095+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.443 seconds
[2025-07-18T11:17:38.701+0000] {processor.py:186} INFO - Started process (PID=3181) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:17:38.702+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:17:38.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:38.705+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:17:38.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:38.933+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:38.945+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:17:39.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.060+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:39.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.071+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:17:39.093+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.399 seconds
[2025-07-18T11:18:09.369+0000] {processor.py:186} INFO - Started process (PID=3322) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:18:09.369+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:18:09.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:09.372+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:18:09.600+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:09.599+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:09.609+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:18:09.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:09.714+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:09.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:09.726+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:18:09.747+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.384 seconds
[2025-07-18T11:18:40.339+0000] {processor.py:186} INFO - Started process (PID=3463) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:18:40.340+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:18:40.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:40.343+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:18:40.554+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:40.554+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:40.562+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:18:40.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:40.657+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:40.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:40.668+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:18:40.688+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.356 seconds
[2025-07-18T11:19:11.051+0000] {processor.py:186} INFO - Started process (PID=3604) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:19:11.053+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:19:11.055+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.055+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:19:11.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.297+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:11.305+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:19:11.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:11.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.426+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:19:11.450+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.405 seconds
[2025-07-18T11:19:41.636+0000] {processor.py:186} INFO - Started process (PID=3750) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:19:41.637+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:19:41.639+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.639+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:19:41.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.840+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:41.849+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:19:41.949+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.948+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:41.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:41.960+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:19:41.980+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.351 seconds
[2025-07-18T11:20:12.088+0000] {processor.py:186} INFO - Started process (PID=3891) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:20:12.089+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:20:12.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.091+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:20:12.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.315+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:12.324+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:20:12.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.424+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:12.435+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.435+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:20:12.453+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.371 seconds
