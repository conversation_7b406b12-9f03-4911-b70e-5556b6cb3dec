[2025-07-18T10:16:49.846+0000] {processor.py:186} INFO - Started process (PID=221) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:16:49.847+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:16:49.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.850+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:16:49.891+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.888+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:49.892+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:16:49.914+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.075 seconds
[2025-07-18T10:17:20.121+0000] {processor.py:186} INFO - Started process (PID=352) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:17:20.122+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:17:20.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.124+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:17:20.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.154+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.158+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:17:20.175+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.059 seconds
[2025-07-18T10:17:50.585+0000] {processor.py:186} INFO - Started process (PID=485) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:17:50.586+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:17:50.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.587+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:17:50.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.617+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:50.622+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:17:50.639+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.059 seconds
[2025-07-18T10:18:21.242+0000] {processor.py:186} INFO - Started process (PID=616) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:18:21.242+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:18:21.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.243+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:18:21.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.272+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.277+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:18:21.296+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.060 seconds
[2025-07-18T10:18:52.153+0000] {processor.py:186} INFO - Started process (PID=745) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:18:52.154+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:18:52.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.155+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:18:52.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.186+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:52.190+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:18:52.205+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.057 seconds
[2025-07-18T10:19:23.099+0000] {processor.py:186} INFO - Started process (PID=878) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:19:23.101+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:19:23.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.103+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:19:23.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.141+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:23.145+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:19:23.161+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.070 seconds
[2025-07-18T10:19:54.062+0000] {processor.py:186} INFO - Started process (PID=1007) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:19:54.063+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:19:54.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.064+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:19:54.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.093+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:54.097+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:19:54.112+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.055 seconds
[2025-07-18T10:20:25.093+0000] {processor.py:186} INFO - Started process (PID=1138) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:20:25.094+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:20:25.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.095+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:20:25.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.125+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:25.130+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:20:25.148+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.061 seconds
[2025-07-18T10:20:56.048+0000] {processor.py:186} INFO - Started process (PID=1269) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:20:56.049+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:20:56.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.050+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:20:56.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.079+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:56.083+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:20:56.100+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.058 seconds
[2025-07-18T10:21:27.023+0000] {processor.py:186} INFO - Started process (PID=1400) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:21:27.024+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:21:27.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.025+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:21:27.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.059+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:27.064+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:21:27.081+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.064 seconds
[2025-07-18T10:21:57.184+0000] {processor.py:186} INFO - Started process (PID=1531) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:21:57.185+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:21:57.186+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.186+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:21:57.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.214+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:57.219+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:21:57.234+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.055 seconds
[2025-07-18T10:22:28.231+0000] {processor.py:186} INFO - Started process (PID=1662) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:22:28.232+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:22:28.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.233+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:22:28.273+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.269+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:28.274+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:22:28.294+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.070 seconds
[2025-07-18T10:22:59.212+0000] {processor.py:186} INFO - Started process (PID=1793) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:22:59.213+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:22:59.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.214+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:22:59.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.243+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:59.247+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:22:59.262+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.056 seconds
[2025-07-18T10:23:30.153+0000] {processor.py:186} INFO - Started process (PID=1926) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:23:30.154+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:23:30.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.155+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:23:30.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.184+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:30.188+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:23:30.204+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.057 seconds
[2025-07-18T10:24:01.012+0000] {processor.py:186} INFO - Started process (PID=2055) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:24:01.012+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:24:01.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.013+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:24:01.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.048+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:01.053+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:24:01.072+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.066 seconds
[2025-07-18T10:24:31.254+0000] {processor.py:186} INFO - Started process (PID=2186) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:24:31.255+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:24:31.256+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.256+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:24:31.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.287+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_weights_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_weights_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.293+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:24:31.309+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.061 seconds
[2025-07-18T10:25:03.561+0000] {processor.py:186} INFO - Started process (PID=2317) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:25:03.562+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:25:03.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.563+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:25:03.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.738+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:03.748+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:25:03.836+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.835+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:03.846+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.845+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:25:03.865+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.311 seconds
[2025-07-18T10:26:21.045+0000] {processor.py:186} INFO - Started process (PID=221) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:26:21.046+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:26:21.048+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:21.048+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:26:21.382+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:21.382+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:21.389+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:26:21.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:21.479+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:21.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:21.488+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:26:21.510+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.472 seconds
[2025-07-18T10:26:53.442+0000] {processor.py:186} INFO - Started process (PID=352) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:26:53.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:26:53.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:53.446+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:26:53.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:53.995+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:54.001+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:26:54.115+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.114+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:54.131+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.131+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:26:54.154+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.723 seconds
[2025-07-18T10:27:24.634+0000] {processor.py:186} INFO - Started process (PID=485) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:27:24.635+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:27:24.638+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:24.637+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:27:24.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:24.969+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:24.976+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:27:25.076+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:25.076+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:25.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:25.087+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:27:25.106+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.478 seconds
[2025-07-18T10:27:55.236+0000] {processor.py:186} INFO - Started process (PID=614) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:27:55.237+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:27:55.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:55.239+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:27:55.432+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:55.432+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:55.442+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:27:55.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:55.547+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:55.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:55.558+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:27:55.579+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.348 seconds
[2025-07-18T10:28:25.919+0000] {processor.py:186} INFO - Started process (PID=745) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:28:25.919+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:28:25.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:25.922+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:28:26.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:26.121+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:26.131+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:28:26.261+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:26.260+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:26.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:26.272+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:28:26.295+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.383 seconds
[2025-07-18T10:28:56.714+0000] {processor.py:186} INFO - Started process (PID=876) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:28:56.715+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:28:56.717+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:56.717+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:28:56.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:56.912+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:56.920+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:28:57.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:57.024+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:57.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:57.037+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:28:57.058+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.350 seconds
[2025-07-18T10:29:27.380+0000] {processor.py:186} INFO - Started process (PID=1007) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:29:27.381+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:29:27.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:27.383+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:29:27.590+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:27.590+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:27.600+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:29:27.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:27.709+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:27.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:27.720+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:29:27.739+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.365 seconds
[2025-07-18T10:29:57.840+0000] {processor.py:186} INFO - Started process (PID=1138) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:29:57.841+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:29:57.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:57.843+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:29:58.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:58.047+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:58.056+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:29:58.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:58.157+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:58.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:58.167+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:29:58.189+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.354 seconds
[2025-07-18T10:30:28.493+0000] {processor.py:186} INFO - Started process (PID=1269) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:30:28.493+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:30:28.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:28.495+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:30:28.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:28.693+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:28.701+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:30:28.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:28.805+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:28.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:28.817+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:30:28.839+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.353 seconds
[2025-07-18T10:30:59.030+0000] {processor.py:186} INFO - Started process (PID=1400) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:30:59.031+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:30:59.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:59.033+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:30:59.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:59.230+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:59.240+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:30:59.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:59.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:59.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:59.360+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:30:59.381+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.357 seconds
[2025-07-18T10:31:29.559+0000] {processor.py:186} INFO - Started process (PID=1531) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:31:29.560+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:31:29.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:29.562+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:31:29.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:29.761+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:29.770+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:31:29.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:29.872+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:29.883+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:29.883+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:31:29.903+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.349 seconds
[2025-07-18T10:32:00.167+0000] {processor.py:186} INFO - Started process (PID=1662) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:32:00.168+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:32:00.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:00.170+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:32:00.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:00.367+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:00.375+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:32:00.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:00.475+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:00.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:00.487+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:32:00.507+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.345 seconds
[2025-07-18T10:32:30.645+0000] {processor.py:186} INFO - Started process (PID=1793) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:32:30.646+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:32:30.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:30.648+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:32:30.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:30.834+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:30.843+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:32:30.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:30.941+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:30.954+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:30.954+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:32:30.973+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.335 seconds
[2025-07-18T10:33:01.095+0000] {processor.py:186} INFO - Started process (PID=1924) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:33:01.096+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:33:01.098+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:01.098+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:33:01.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:01.287+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:01.296+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:33:01.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:01.395+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:01.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:01.408+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:33:01.427+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.338 seconds
[2025-07-18T10:33:31.737+0000] {processor.py:186} INFO - Started process (PID=2055) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:33:31.738+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:33:31.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:31.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:33:31.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:31.922+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:31.932+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:33:32.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:32.030+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:32.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:32.044+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:33:32.066+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.334 seconds
[2025-07-18T10:34:02.292+0000] {processor.py:186} INFO - Started process (PID=2186) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:34:02.293+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:34:02.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:02.295+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:34:02.488+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:02.488+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:02.496+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:34:02.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:02.597+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:02.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:02.609+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:34:02.631+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.346 seconds
[2025-07-18T10:34:33.411+0000] {processor.py:186} INFO - Started process (PID=2317) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:34:33.411+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:34:33.413+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:33.413+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:34:33.591+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:33.591+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:33.600+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:34:33.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:33.693+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:33.704+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:33.704+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:34:33.723+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.317 seconds
[2025-07-18T10:35:03.910+0000] {processor.py:186} INFO - Started process (PID=2448) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:35:03.911+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:35:03.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:03.914+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:35:04.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:04.120+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:04.128+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:35:04.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:04.220+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:04.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:04.231+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:35:04.249+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.349 seconds
[2025-07-18T10:35:34.903+0000] {processor.py:186} INFO - Started process (PID=2579) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:35:34.904+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:35:34.907+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:34.906+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:35:35.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:35.108+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:35.116+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:35:35.209+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:35.209+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:35.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:35.219+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:35:35.240+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.343 seconds
[2025-07-18T10:36:05.636+0000] {processor.py:186} INFO - Started process (PID=2710) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:36:05.637+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:36:05.640+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:05.640+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:36:05.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:05.849+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:05.859+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:36:05.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:05.974+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:05.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:05.990+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:36:06.019+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.390 seconds
[2025-07-18T10:36:36.278+0000] {processor.py:186} INFO - Started process (PID=2841) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:36:36.279+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:36:36.282+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:36.281+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:36:36.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:36.483+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:36.491+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:36:36.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:36.606+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:36.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:36.621+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:36:36.646+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.374 seconds
[2025-07-18T10:37:07.109+0000] {processor.py:186} INFO - Started process (PID=2972) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:37:07.110+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:37:07.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:07.113+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:37:07.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:07.316+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:07.324+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:37:07.425+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:07.425+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:07.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:07.437+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:37:07.456+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.352 seconds
[2025-07-18T10:37:38.043+0000] {processor.py:186} INFO - Started process (PID=3103) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:37:38.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:37:38.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:38.046+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:37:38.243+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:38.243+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:38.252+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:37:38.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:38.344+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:38.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:38.358+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:37:38.375+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.340 seconds
[2025-07-18T10:38:08.868+0000] {processor.py:186} INFO - Started process (PID=3234) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:38:08.869+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:38:08.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:08.871+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:38:09.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:09.061+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:09.071+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:38:09.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:09.178+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:09.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:09.189+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:38:09.209+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.347 seconds
[2025-07-18T10:38:40.107+0000] {processor.py:186} INFO - Started process (PID=3365) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:38:40.108+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:38:40.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:40.110+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:38:40.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:40.300+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:40.309+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:38:40.411+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:40.410+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:40.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:40.422+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:38:40.444+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.343 seconds
[2025-07-18T10:39:10.765+0000] {processor.py:186} INFO - Started process (PID=3496) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:39:10.766+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:39:10.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:10.767+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:39:10.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:10.961+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:10.969+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:39:11.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:11.065+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:11.077+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:11.077+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:39:11.095+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.336 seconds
[2025-07-18T10:39:41.475+0000] {processor.py:186} INFO - Started process (PID=3627) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:39:41.476+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:39:41.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:41.478+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:39:41.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:41.672+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:41.681+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:39:41.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:41.784+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:41.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:41.795+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:39:41.816+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.347 seconds
[2025-07-18T10:40:12.197+0000] {processor.py:186} INFO - Started process (PID=3758) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:40:12.197+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:40:12.199+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:12.199+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:40:12.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:12.385+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:12.393+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:40:12.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:12.492+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:12.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:12.502+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:40:12.522+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.331 seconds
[2025-07-18T10:40:42.769+0000] {processor.py:186} INFO - Started process (PID=3889) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:40:42.770+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:40:42.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:42.772+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:40:42.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:42.955+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:42.963+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:40:43.059+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:43.059+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:43.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:43.070+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:40:43.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.325 seconds
[2025-07-18T10:41:13.152+0000] {processor.py:186} INFO - Started process (PID=4020) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:41:13.153+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:41:13.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:13.155+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:41:13.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:13.356+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:13.365+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:41:13.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:13.653+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:13.667+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:13.667+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:41:13.689+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.543 seconds
[2025-07-18T10:41:43.831+0000] {processor.py:186} INFO - Started process (PID=4156) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:41:43.832+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:41:43.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:43.833+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:41:44.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:44.041+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:44.049+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:41:44.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:44.363+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:44.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:44.373+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:41:44.389+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.564 seconds
[2025-07-18T10:42:14.798+0000] {processor.py:186} INFO - Started process (PID=4292) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:42:14.799+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:42:14.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:14.801+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:42:15.027+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:15.026+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:15.033+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:42:15.317+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:15.317+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:15.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:15.325+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:42:15.339+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.547 seconds
[2025-07-18T10:42:58.249+0000] {processor.py:186} INFO - Started process (PID=226) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:42:58.250+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:42:58.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.252+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:42:58.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.570+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:58.577+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:42:58.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.672+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:58.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.681+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:42:58.699+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.456 seconds
[2025-07-18T10:43:29.920+0000] {processor.py:186} INFO - Started process (PID=364) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:43:29.922+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:43:29.925+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:29.925+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:43:30.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:30.278+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:30.285+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:43:30.382+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:30.381+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:30.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:30.391+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:43:30.409+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.497 seconds
[2025-07-18T10:44:00.476+0000] {processor.py:186} INFO - Started process (PID=500) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:44:00.477+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:44:00.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:00.479+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:44:00.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:00.794+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:00.800+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:44:00.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:00.881+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:00.891+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:00.891+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:44:00.909+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.438 seconds
[2025-07-18T10:44:31.044+0000] {processor.py:186} INFO - Started process (PID=636) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:44:31.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:44:31.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:31.046+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:44:31.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:31.226+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:31.234+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:44:31.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:31.319+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:31.328+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:31.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:44:31.346+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.308 seconds
[2025-07-18T10:45:02.102+0000] {processor.py:186} INFO - Started process (PID=772) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:45:02.103+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:45:02.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.104+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:45:02.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.313+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:02.323+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:45:02.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.418+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:02.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.429+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:45:02.454+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.358 seconds
[2025-07-18T10:45:32.717+0000] {processor.py:186} INFO - Started process (PID=908) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:45:32.718+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:45:32.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:32.720+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:45:32.917+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:32.917+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:32.927+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:45:33.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:33.024+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:33.035+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:33.034+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:45:33.054+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.342 seconds
[2025-07-18T10:46:03.672+0000] {processor.py:186} INFO - Started process (PID=1042) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:46:03.673+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:46:03.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.675+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:46:03.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.877+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:03.884+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:46:03.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.979+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:03.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:03.990+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:46:04.010+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.344 seconds
[2025-07-18T10:46:34.497+0000] {processor.py:186} INFO - Started process (PID=1178) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:46:34.498+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:46:34.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:34.500+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:46:34.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:34.722+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:34.730+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:46:34.833+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:34.833+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:34.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:34.845+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:46:34.867+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.377 seconds
[2025-07-18T10:47:05.091+0000] {processor.py:186} INFO - Started process (PID=1314) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:47:05.092+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:47:05.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.094+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:47:05.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.293+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:05.301+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:47:05.399+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.398+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:05.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.410+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:47:05.431+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.346 seconds
[2025-07-18T10:48:03.640+0000] {processor.py:186} INFO - Started process (PID=226) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:48:03.641+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:48:03.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.643+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:48:03.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.977+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:03.984+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:48:04.077+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.076+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:04.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.086+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:48:04.106+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.472 seconds
[2025-07-18T10:48:34.738+0000] {processor.py:186} INFO - Started process (PID=362) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:48:34.739+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:48:34.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:34.741+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:48:35.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.078+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:35.086+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:48:35.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.187+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:35.200+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.200+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:48:35.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.490 seconds
[2025-07-18T10:49:05.515+0000] {processor.py:186} INFO - Started process (PID=498) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:49:05.516+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:49:05.519+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.518+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:49:05.885+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.885+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:05.893+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:49:05.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.986+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:05.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.998+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:49:06.017+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.507 seconds
[2025-07-18T10:49:36.792+0000] {processor.py:186} INFO - Started process (PID=636) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:49:36.793+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:49:36.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.796+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:49:36.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:36.973+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:36.981+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:49:37.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.071+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:37.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.081+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:49:37.101+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.313 seconds
[2025-07-18T10:50:07.364+0000] {processor.py:186} INFO - Started process (PID=772) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:50:07.365+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:50:07.367+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:07.367+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:50:07.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:07.573+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:07.584+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:50:07.692+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:07.691+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:07.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:07.703+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:50:07.724+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.366 seconds
[2025-07-18T10:50:38.171+0000] {processor.py:186} INFO - Started process (PID=908) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:50:38.172+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:50:38.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:38.174+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:50:38.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:38.414+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:38.422+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:50:38.514+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:38.514+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:38.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:38.524+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:50:38.543+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.378 seconds
[2025-07-18T10:51:08.905+0000] {processor.py:186} INFO - Started process (PID=1044) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:51:08.906+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:51:08.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:08.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:51:09.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:09.111+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:09.119+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:51:09.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:09.223+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:09.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:09.234+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:51:09.254+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.354 seconds
[2025-07-18T10:51:39.470+0000] {processor.py:186} INFO - Started process (PID=1181) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:51:39.471+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:51:39.473+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:39.473+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:51:39.662+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:39.662+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:39.671+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:51:39.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:39.767+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:39.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:39.777+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:51:39.797+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.332 seconds
[2025-07-18T10:52:09.912+0000] {processor.py:186} INFO - Started process (PID=1317) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:52:09.913+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:52:09.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:09.915+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:52:10.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.109+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:10.118+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:52:10.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.214+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:10.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.224+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:52:10.244+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.337 seconds
[2025-07-18T10:52:40.496+0000] {processor.py:186} INFO - Started process (PID=1453) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:52:40.497+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:52:40.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:40.499+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:52:40.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:40.702+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:40.711+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:52:40.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:40.813+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:40.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:40.824+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:52:40.847+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.357 seconds
[2025-07-18T10:53:11.058+0000] {processor.py:186} INFO - Started process (PID=1589) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:53:11.059+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:53:11.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:11.061+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:53:11.266+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:11.266+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:11.275+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:53:11.382+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:11.382+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:11.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:11.392+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:53:11.413+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.361 seconds
[2025-07-18T10:53:41.474+0000] {processor.py:186} INFO - Started process (PID=1725) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:53:41.475+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:53:41.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:41.478+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:53:41.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:41.659+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:41.666+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:53:41.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:41.756+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:41.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:41.765+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:53:41.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.317 seconds
[2025-07-18T10:54:11.994+0000] {processor.py:186} INFO - Started process (PID=1859) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:54:11.995+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:54:11.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:11.997+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:54:12.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:12.201+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:12.210+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:54:12.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:12.314+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:12.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:12.326+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:54:12.341+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.354 seconds
[2025-07-18T10:54:42.976+0000] {processor.py:186} INFO - Started process (PID=1997) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:54:42.977+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:54:42.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:42.979+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:54:43.172+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:43.171+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:43.179+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:54:43.273+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:43.272+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:43.284+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:43.283+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:54:43.302+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.332 seconds
[2025-07-18T10:55:13.528+0000] {processor.py:186} INFO - Started process (PID=2133) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:55:13.529+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:55:13.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.530+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:55:13.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.706+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:13.715+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:55:13.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.801+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:13.812+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:13.811+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:55:13.828+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.306 seconds
[2025-07-18T10:55:44.375+0000] {processor.py:186} INFO - Started process (PID=2269) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:55:44.376+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:55:44.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:44.378+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:55:44.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:44.548+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:44.556+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:55:44.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:44.641+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:44.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:44.650+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:55:44.666+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.297 seconds
[2025-07-18T10:57:28.361+0000] {processor.py:186} INFO - Started process (PID=226) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:57:28.362+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:57:28.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.364+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:57:28.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.688+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:28.694+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:57:28.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.784+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:28.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.794+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:57:28.812+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.457 seconds
[2025-07-18T10:57:59.666+0000] {processor.py:186} INFO - Started process (PID=362) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:57:59.667+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:57:59.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:59.669+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:58:00.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.064+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:00.072+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:58:00.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.180+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:00.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.191+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:58:00.218+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.559 seconds
[2025-07-18T10:58:30.723+0000] {processor.py:186} INFO - Started process (PID=498) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:58:30.723+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:58:30.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:30.725+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:58:31.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.112+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:31.119+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:58:31.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.226+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:31.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.238+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:58:31.258+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.541 seconds
[2025-07-18T10:59:01.555+0000] {processor.py:186} INFO - Started process (PID=634) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:59:01.556+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:59:01.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:01.558+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:59:01.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:01.748+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:01.757+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:59:01.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:01.854+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:01.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:01.866+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:59:01.883+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.334 seconds
[2025-07-18T10:59:32.437+0000] {processor.py:186} INFO - Started process (PID=770) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:59:32.437+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T10:59:32.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:32.439+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:59:32.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:32.635+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:32.646+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T10:59:32.743+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:32.743+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:32.753+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:32.753+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T10:59:32.772+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.341 seconds
[2025-07-18T11:00:03.069+0000] {processor.py:186} INFO - Started process (PID=906) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:00:03.070+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:00:03.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.072+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:00:03.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.271+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:03.281+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:00:03.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.389+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:03.403+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.402+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:00:03.423+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.359 seconds
[2025-07-18T11:00:33.765+0000] {processor.py:186} INFO - Started process (PID=1042) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:00:33.766+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:00:33.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:33.768+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:00:33.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:33.980+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:33.989+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:00:34.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.093+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:34.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.104+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:00:34.123+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.364 seconds
[2025-07-18T11:01:04.302+0000] {processor.py:186} INFO - Started process (PID=1178) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:01:04.302+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:01:04.305+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:04.305+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:01:04.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:04.511+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:04.521+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:01:04.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:04.642+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:04.656+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:04.655+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:01:04.672+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.376 seconds
[2025-07-18T11:01:34.785+0000] {processor.py:186} INFO - Started process (PID=1314) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:01:34.786+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:01:34.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:34.788+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:01:34.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:34.975+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:34.985+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:01:35.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.082+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:35.093+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.093+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:01:35.109+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.329 seconds
[2025-07-18T11:02:06.084+0000] {processor.py:186} INFO - Started process (PID=1450) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:02:06.085+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:02:06.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:06.087+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:02:06.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:06.278+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:06.287+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:02:06.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:06.386+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:06.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:06.397+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:02:06.420+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.343 seconds
[2025-07-18T11:02:37.001+0000] {processor.py:186} INFO - Started process (PID=1586) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:02:37.002+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:02:37.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:37.004+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:02:37.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:37.202+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:37.213+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:02:37.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:37.325+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:37.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:37.337+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:02:37.358+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.363 seconds
[2025-07-18T11:03:07.532+0000] {processor.py:186} INFO - Started process (PID=1722) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:03:07.533+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:03:07.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:07.535+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:03:07.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:07.740+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:07.748+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:03:07.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:07.847+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:07.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:07.858+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:03:07.880+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.354 seconds
[2025-07-18T11:03:38.179+0000] {processor.py:186} INFO - Started process (PID=1858) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:03:38.180+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:03:38.182+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:38.182+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:03:38.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:38.391+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:38.402+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:03:38.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:38.507+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:38.520+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:38.520+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:03:38.539+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.366 seconds
[2025-07-18T11:04:08.992+0000] {processor.py:186} INFO - Started process (PID=1994) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:04:08.994+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:04:08.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:08.997+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:04:09.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:09.239+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:09.248+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:04:09.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:09.366+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:09.380+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:09.380+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:04:09.402+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.418 seconds
[2025-07-18T11:04:39.592+0000] {processor.py:186} INFO - Started process (PID=2130) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:04:39.593+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:04:39.595+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:39.595+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:04:39.793+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:39.793+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:39.804+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:04:39.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:39.919+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:39.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:39.933+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:04:39.951+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.367 seconds
[2025-07-18T11:05:10.222+0000] {processor.py:186} INFO - Started process (PID=2266) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:05:10.224+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:05:10.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:10.226+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:05:10.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:10.449+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:10.461+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:05:10.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:10.581+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:10.593+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:10.593+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:05:10.620+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.404 seconds
[2025-07-18T11:05:41.676+0000] {processor.py:186} INFO - Started process (PID=2402) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:05:41.677+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:05:41.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:41.679+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:05:41.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:41.945+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:41.955+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:05:42.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:42.115+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:42.132+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:42.131+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:05:42.157+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.487 seconds
[2025-07-18T11:06:50.977+0000] {processor.py:186} INFO - Started process (PID=232) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:06:50.978+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:06:50.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:50.980+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:06:51.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.300+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:51.308+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:06:51.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.396+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:51.405+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.405+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:06:51.424+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.452 seconds
[2025-07-18T11:07:22.217+0000] {processor.py:186} INFO - Started process (PID=373) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:07:22.218+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:07:22.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.221+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:07:22.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.596+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:22.603+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:07:22.697+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.697+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:22.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.706+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:07:22.732+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.522 seconds
[2025-07-18T11:07:53.624+0000] {processor.py:186} INFO - Started process (PID=520) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:07:53.625+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:07:53.627+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.627+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:07:53.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.962+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:53.967+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:07:54.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.057+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:54.069+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.068+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:07:54.090+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.471 seconds
[2025-07-18T11:08:24.556+0000] {processor.py:186} INFO - Started process (PID=661) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:08:24.557+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:08:24.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.559+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:08:24.770+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.770+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:24.780+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:08:24.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.884+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:24.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.897+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:08:24.915+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.365 seconds
[2025-07-18T11:08:55.380+0000] {processor.py:186} INFO - Started process (PID=802) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:08:55.381+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:08:55.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:08:55.583+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.583+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:55.592+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:08:55.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.708+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:55.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.720+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:08:55.749+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.375 seconds
[2025-07-18T11:09:25.996+0000] {processor.py:186} INFO - Started process (PID=945) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:09:25.997+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:09:25.999+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:25.999+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:09:26.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:26.231+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:26.240+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:09:26.339+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:26.338+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:26.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:26.350+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:09:26.372+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.384 seconds
[2025-07-18T11:09:56.824+0000] {processor.py:186} INFO - Started process (PID=1086) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:09:56.825+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:09:56.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:56.828+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:09:57.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.036+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:57.045+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:09:57.160+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.159+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:57.173+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.173+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:09:57.197+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.378 seconds
[2025-07-18T11:10:27.356+0000] {processor.py:186} INFO - Started process (PID=1227) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:10:27.357+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:10:27.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:27.359+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:10:27.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:27.538+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:27.546+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:10:27.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:27.652+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:27.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:27.663+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:10:27.684+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.333 seconds
[2025-07-18T11:10:58.725+0000] {processor.py:186} INFO - Started process (PID=1368) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:10:58.725+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:10:58.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:58.727+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:10:58.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:58.932+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:58.942+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:10:59.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.051+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:59.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.062+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:10:59.082+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.364 seconds
[2025-07-18T11:11:29.329+0000] {processor.py:186} INFO - Started process (PID=1509) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:11:29.330+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:11:29.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:29.331+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:11:29.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:29.532+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:29.543+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:11:29.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:29.644+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:29.656+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:29.656+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:11:29.676+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.353 seconds
[2025-07-18T11:12:00.017+0000] {processor.py:186} INFO - Started process (PID=1650) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:12:00.018+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:12:00.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:00.020+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:12:00.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:00.206+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:00.216+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:12:00.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:00.313+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:00.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:00.324+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:12:00.344+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.333 seconds
[2025-07-18T11:12:30.977+0000] {processor.py:186} INFO - Started process (PID=1791) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:12:30.978+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:12:30.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.980+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:12:31.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.161+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:31.170+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:12:31.261+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.261+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:31.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.271+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:12:31.291+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.320 seconds
[2025-07-18T11:13:02.588+0000] {processor.py:186} INFO - Started process (PID=1932) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:13:02.589+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:13:02.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.592+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:13:02.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.813+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:02.823+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:13:02.945+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.944+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:02.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.960+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:13:02.984+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.403 seconds
[2025-07-18T11:13:33.190+0000] {processor.py:186} INFO - Started process (PID=2073) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:13:33.191+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:13:33.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.192+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:13:33.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.384+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:33.392+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:13:33.482+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.482+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:33.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.491+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:13:33.509+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.325 seconds
[2025-07-18T11:14:04.053+0000] {processor.py:186} INFO - Started process (PID=2214) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:14:04.054+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:14:04.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.057+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:14:04.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.290+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:04.298+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:14:04.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:04.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.429+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:14:04.454+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.407 seconds
[2025-07-18T11:14:34.624+0000] {processor.py:186} INFO - Started process (PID=2355) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:14:34.625+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:14:34.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.628+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:14:34.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.845+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:34.854+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:14:34.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.962+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:34.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.972+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:14:34.995+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.377 seconds
[2025-07-18T11:15:05.286+0000] {processor.py:186} INFO - Started process (PID=2494) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:15:05.287+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:15:05.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.290+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:15:05.520+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.520+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:05.529+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:15:05.646+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.645+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:05.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.657+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:15:05.682+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.403 seconds
[2025-07-18T11:15:36.178+0000] {processor.py:186} INFO - Started process (PID=2635) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:15:36.179+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:15:36.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.181+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:15:36.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.416+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:36.426+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:15:36.528+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.527+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:36.540+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.540+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:15:36.563+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.392 seconds
[2025-07-18T11:16:06.751+0000] {processor.py:186} INFO - Started process (PID=2776) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:16:06.753+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:16:06.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.755+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:16:06.989+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:06.989+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:06.999+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:16:07.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.132+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:07.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.149+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:16:07.171+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.427 seconds
[2025-07-18T11:16:37.500+0000] {processor.py:186} INFO - Started process (PID=2917) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:16:37.500+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:16:37.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.502+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:16:37.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.699+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:37.709+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:16:37.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.810+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:37.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.820+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:16:37.839+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.345 seconds
[2025-07-18T11:17:08.674+0000] {processor.py:186} INFO - Started process (PID=3058) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:17:08.674+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:17:08.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:08.678+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:17:08.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:08.932+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:08.941+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:17:09.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:09.078+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:09.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:09.089+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:17:09.110+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.444 seconds
[2025-07-18T11:17:39.585+0000] {processor.py:186} INFO - Started process (PID=3199) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:17:39.586+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:17:39.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.588+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:17:39.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.810+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:39.818+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:17:39.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.931+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:39.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:39.944+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:17:39.968+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.389 seconds
[2025-07-18T11:18:10.238+0000] {processor.py:186} INFO - Started process (PID=3340) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:18:10.240+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:18:10.243+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:10.243+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:18:10.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:10.472+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:10.482+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:18:10.604+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:10.603+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:10.618+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:10.618+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:18:10.642+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.410 seconds
[2025-07-18T11:18:41.159+0000] {processor.py:186} INFO - Started process (PID=3481) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:18:41.160+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:18:41.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.162+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:18:41.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.369+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:41.378+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:18:41.480+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.480+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:41.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.493+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:18:41.515+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.363 seconds
[2025-07-18T11:19:11.937+0000] {processor.py:186} INFO - Started process (PID=3622) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:19:11.938+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:19:11.942+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:11.941+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:19:12.182+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:12.182+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:12.191+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:19:12.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:12.304+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:12.317+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:12.316+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:19:12.340+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.411 seconds
[2025-07-18T11:19:42.445+0000] {processor.py:186} INFO - Started process (PID=3768) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:19:42.446+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:19:42.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:42.449+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:19:42.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:42.669+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:42.679+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:19:42.791+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:42.790+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:42.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:42.803+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:19:42.824+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.384 seconds
[2025-07-18T11:20:12.915+0000] {processor.py:186} INFO - Started process (PID=3909) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:20:12.916+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:20:12.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:12.918+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:20:13.131+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:13.131+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:13.139+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:20:13.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:13.231+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:13.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:13.242+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:20:13.259+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.352 seconds
