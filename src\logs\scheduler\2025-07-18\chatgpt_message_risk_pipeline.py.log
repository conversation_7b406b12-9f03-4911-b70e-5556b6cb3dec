[2025-07-18T10:16:50.052+0000] {processor.py:186} INFO - Started process (PID=238) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:16:50.053+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:16:50.055+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.054+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:16:50.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.102+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:50.107+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:16:50.124+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.078 seconds
[2025-07-18T10:17:20.398+0000] {processor.py:186} INFO - Started process (PID=369) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:17:20.398+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:17:20.401+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.400+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:17:20.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.429+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.434+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:17:20.451+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.058 seconds
[2025-07-18T10:17:50.902+0000] {processor.py:186} INFO - Started process (PID=498) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:17:50.903+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:17:50.905+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.904+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:17:50.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.937+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:50.942+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:17:51.102+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.206 seconds
[2025-07-18T10:18:21.431+0000] {processor.py:186} INFO - Started process (PID=631) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:18:21.433+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:18:21.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.434+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:18:21.469+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.465+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.470+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:18:21.490+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.063 seconds
[2025-07-18T10:18:52.320+0000] {processor.py:186} INFO - Started process (PID=760) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:18:52.320+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:18:52.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.321+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:18:52.354+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.351+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:52.355+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:18:52.369+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.055 seconds
[2025-07-18T10:19:23.292+0000] {processor.py:186} INFO - Started process (PID=893) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:19:23.293+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:19:23.294+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.294+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:19:23.330+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.327+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:23.331+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:19:23.348+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.063 seconds
[2025-07-18T10:19:54.232+0000] {processor.py:186} INFO - Started process (PID=1022) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:19:54.233+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:19:54.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.234+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:19:54.294+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.290+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:54.296+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:19:54.317+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.090 seconds
[2025-07-18T10:20:25.272+0000] {processor.py:186} INFO - Started process (PID=1153) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:20:25.274+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:20:25.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.275+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:20:25.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.308+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:25.312+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:20:25.328+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.064 seconds
[2025-07-18T10:20:56.224+0000] {processor.py:186} INFO - Started process (PID=1284) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:20:56.225+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:20:56.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.226+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:20:56.259+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.256+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:56.260+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:20:56.275+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.056 seconds
[2025-07-18T10:21:27.202+0000] {processor.py:186} INFO - Started process (PID=1415) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:21:27.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:21:27.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.204+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:21:27.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.235+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:27.239+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:21:27.257+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.060 seconds
[2025-07-18T10:21:57.373+0000] {processor.py:186} INFO - Started process (PID=1546) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:21:57.374+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:21:57.375+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.374+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:21:57.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.403+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:57.407+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:21:57.422+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.055 seconds
[2025-07-18T10:22:28.433+0000] {processor.py:186} INFO - Started process (PID=1677) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:22:28.435+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:22:28.436+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.436+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:22:28.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.468+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:28.472+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:22:28.488+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.061 seconds
[2025-07-18T10:22:59.385+0000] {processor.py:186} INFO - Started process (PID=1808) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:22:59.386+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:22:59.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.387+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:22:59.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.415+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:59.419+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:22:59.434+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.055 seconds
[2025-07-18T10:23:30.332+0000] {processor.py:186} INFO - Started process (PID=1941) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:23:30.333+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:23:30.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.334+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:23:30.367+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.364+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:30.368+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:23:30.383+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.057 seconds
[2025-07-18T10:24:01.206+0000] {processor.py:186} INFO - Started process (PID=2070) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:24:01.207+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:24:01.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.208+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:24:01.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.240+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:01.245+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:24:01.264+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.064 seconds
[2025-07-18T10:24:31.454+0000] {processor.py:186} INFO - Started process (PID=2201) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:24:31.455+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:24:31.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.456+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:24:31.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.487+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_message_risk_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_message_risk_pipeline.py", line 6, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.492+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:24:31.508+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.060 seconds
[2025-07-18T10:25:04.279+0000] {processor.py:186} INFO - Started process (PID=2332) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:25:04.280+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:25:04.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.281+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:25:04.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.468+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:04.478+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:25:04.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.574+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:04.584+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.584+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:25:04.604+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.330 seconds
[2025-07-18T10:26:22.076+0000] {processor.py:186} INFO - Started process (PID=236) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:26:22.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:26:22.080+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:22.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:26:22.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:22.428+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:22.436+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:26:22.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:22.527+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:22.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:22.537+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:26:22.555+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.485 seconds
[2025-07-18T10:26:54.479+0000] {processor.py:186} INFO - Started process (PID=367) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:26:54.480+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:26:54.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.483+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:26:54.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.843+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:54.851+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:26:54.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.957+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:54.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.971+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:26:54.990+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.520 seconds
[2025-07-18T10:27:25.639+0000] {processor.py:186} INFO - Started process (PID=498) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:27:25.640+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:27:25.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:25.643+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:27:26.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.024+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:26.031+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:27:26.141+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.140+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:26.154+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:26.154+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:27:26.174+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.542 seconds
[2025-07-18T10:27:56.634+0000] {processor.py:186} INFO - Started process (PID=629) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:27:56.635+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:27:56.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.637+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:27:56.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.817+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:56.826+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:27:56.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.915+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:56.925+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.925+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:27:56.939+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.312 seconds
[2025-07-18T10:28:27.707+0000] {processor.py:186} INFO - Started process (PID=762) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:28:27.708+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:28:27.710+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:27.710+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:28:27.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:27.901+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:27.909+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:28:28.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.005+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:28.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:28.016+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:28:28.034+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.332 seconds
[2025-07-18T10:28:58.138+0000] {processor.py:186} INFO - Started process (PID=893) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:28:58.139+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:28:58.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.141+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:28:58.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.339+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:58.349+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:28:58.450+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.450+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:58.460+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.460+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:28:58.479+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.348 seconds
[2025-07-18T10:29:28.812+0000] {processor.py:186} INFO - Started process (PID=1024) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:29:28.813+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:29:28.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:28.815+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:29:29.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.014+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:29.023+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:29:29.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.127+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:29.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:29.137+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:29:29.154+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.348 seconds
[2025-07-18T10:29:59.250+0000] {processor.py:186} INFO - Started process (PID=1155) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:29:59.252+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:29:59.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.254+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:29:59.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.446+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:59.455+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:29:59.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.559+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:59.571+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.570+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:29:59.592+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.348 seconds
[2025-07-18T10:30:30.545+0000] {processor.py:186} INFO - Started process (PID=1286) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:30:30.546+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:30:30.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:30.548+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:30:30.734+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:30.733+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:30.743+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:30:30.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:30.837+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:30.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:30.847+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:30:30.868+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.329 seconds
[2025-07-18T10:31:01.101+0000] {processor.py:186} INFO - Started process (PID=1417) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:31:01.102+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:31:01.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:01.104+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:31:01.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:01.287+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:01.296+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:31:01.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:01.390+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:01.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:01.400+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:31:01.419+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.323 seconds
[2025-07-18T10:31:31.632+0000] {processor.py:186} INFO - Started process (PID=1548) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:31:31.632+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:31:31.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:31.634+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:31:31.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:31.818+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:31.828+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:31:31.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:31.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:31.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:31.920+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:31:31.938+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.312 seconds
[2025-07-18T10:32:02.595+0000] {processor.py:186} INFO - Started process (PID=1679) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:32:02.596+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:32:02.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:02.598+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:32:02.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:02.829+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:02.836+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:32:02.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:02.943+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:02.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:02.956+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:32:02.976+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.387 seconds
[2025-07-18T10:32:33.050+0000] {processor.py:186} INFO - Started process (PID=1810) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:32:33.051+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:32:33.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.053+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:32:33.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.243+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:33.253+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:32:33.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:33.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:33.361+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:32:33.379+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.335 seconds
[2025-07-18T10:33:03.529+0000] {processor.py:186} INFO - Started process (PID=1941) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:33:03.530+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:33:03.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:03.531+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:33:03.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:03.719+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:03.728+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:33:03.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:03.849+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:03.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:03.865+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:33:03.883+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.360 seconds
[2025-07-18T10:33:34.160+0000] {processor.py:186} INFO - Started process (PID=2072) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:33:34.161+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:33:34.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.163+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:33:34.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.352+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:34.361+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:33:34.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.455+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:34.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:34.466+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:33:34.487+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.333 seconds
[2025-07-18T10:34:04.740+0000] {processor.py:186} INFO - Started process (PID=2203) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:34:04.741+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:34:04.743+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:04.743+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:34:04.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:04.932+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:04.940+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:34:05.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.042+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:05.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:05.054+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:34:05.075+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.341 seconds
[2025-07-18T10:34:35.818+0000] {processor.py:186} INFO - Started process (PID=2334) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:34:35.819+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:34:35.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:35.821+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:34:36.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.017+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:36.025+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:34:36.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.127+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:36.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:36.140+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:34:36.161+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.349 seconds
[2025-07-18T10:35:06.327+0000] {processor.py:186} INFO - Started process (PID=2465) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:35:06.328+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:35:06.330+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:06.329+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:35:06.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:06.509+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:06.520+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:35:06.616+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:06.616+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:06.626+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:06.625+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:35:06.642+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.321 seconds
[2025-07-18T10:35:37.317+0000] {processor.py:186} INFO - Started process (PID=2596) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:35:37.318+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:35:37.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:37.320+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:35:37.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:37.545+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:37.553+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:35:37.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:37.653+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:37.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:37.663+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:35:37.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.375 seconds
[2025-07-18T10:36:08.099+0000] {processor.py:186} INFO - Started process (PID=2727) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:36:08.101+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:36:08.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.103+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:36:08.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.304+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:08.313+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:36:08.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:08.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:08.427+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:36:08.445+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.352 seconds
[2025-07-18T10:36:38.732+0000] {processor.py:186} INFO - Started process (PID=2858) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:36:38.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:36:38.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:38.735+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:36:38.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:38.924+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:38.934+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:36:39.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.032+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:39.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:39.042+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:36:39.062+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.336 seconds
[2025-07-18T10:37:09.563+0000] {processor.py:186} INFO - Started process (PID=2989) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:37:09.564+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:37:09.566+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:09.566+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:37:09.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:09.766+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:09.775+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:37:09.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:09.880+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:09.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:09.893+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:37:09.914+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.357 seconds
[2025-07-18T10:37:40.476+0000] {processor.py:186} INFO - Started process (PID=3120) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:37:40.477+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:37:40.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:40.479+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:37:40.666+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:40.666+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:40.675+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:37:40.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:40.771+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:40.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:40.783+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:37:40.803+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.332 seconds
[2025-07-18T10:38:10.941+0000] {processor.py:186} INFO - Started process (PID=3251) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:38:10.942+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:38:10.945+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:10.944+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:38:11.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:11.149+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:11.157+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:38:11.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:11.253+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:11.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:11.268+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:38:11.288+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.352 seconds
[2025-07-18T10:38:41.904+0000] {processor.py:186} INFO - Started process (PID=3382) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:38:41.905+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:38:41.907+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:41.907+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:38:42.098+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.098+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:42.108+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:38:42.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.208+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:42.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:42.222+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:38:42.243+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.344 seconds
[2025-07-18T10:39:12.541+0000] {processor.py:186} INFO - Started process (PID=3513) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:39:12.541+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:39:12.544+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:12.543+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:39:12.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:12.750+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:12.758+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:39:12.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:12.865+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:12.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:12.877+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:39:12.894+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.359 seconds
[2025-07-18T10:39:43.267+0000] {processor.py:186} INFO - Started process (PID=3644) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:39:43.268+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:39:43.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:43.270+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:39:43.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:43.459+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:43.468+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:39:43.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:43.581+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:43.593+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:43.593+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:39:43.612+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.350 seconds
[2025-07-18T10:40:13.958+0000] {processor.py:186} INFO - Started process (PID=3775) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:40:13.959+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:40:13.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:13.961+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:40:14.199+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.199+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:14.208+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:40:14.317+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.316+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:14.328+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:14.327+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:40:14.347+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.394 seconds
[2025-07-18T10:40:44.534+0000] {processor.py:186} INFO - Started process (PID=3906) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:40:44.535+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:40:44.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:44.537+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:40:44.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:44.728+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:44.737+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:40:44.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:44.828+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:44.839+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:44.839+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:40:44.858+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.330 seconds
[2025-07-18T10:41:15.226+0000] {processor.py:186} INFO - Started process (PID=4037) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:41:15.227+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:41:15.229+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:15.229+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:41:15.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:15.442+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:15.451+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:41:15.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:15.548+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:15.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:15.559+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:41:15.578+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.357 seconds
[2025-07-18T10:41:45.853+0000] {processor.py:186} INFO - Started process (PID=4173) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:41:45.854+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:41:45.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:45.856+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:41:46.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:46.063+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:46.073+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:41:46.172+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:46.172+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:46.183+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:46.182+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:41:46.202+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.356 seconds
[2025-07-18T10:42:16.894+0000] {processor.py:186} INFO - Started process (PID=4309) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:42:16.895+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:42:16.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:16.898+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:42:17.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:17.136+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:17.148+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:42:17.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:17.451+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:17.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:17.461+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:42:17.480+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.594 seconds
[2025-07-18T10:42:59.022+0000] {processor.py:186} INFO - Started process (PID=241) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:42:59.023+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:42:59.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.025+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:42:59.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.345+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:59.352+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:42:59.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.443+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:59.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:59.451+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:42:59.471+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.455 seconds
[2025-07-18T10:43:31.418+0000] {processor.py:186} INFO - Started process (PID=379) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:43:31.419+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:43:31.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:31.421+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:43:31.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:31.738+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:31.744+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:43:31.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:31.834+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:31.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:31.843+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:43:31.859+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.447 seconds
[2025-07-18T10:44:01.952+0000] {processor.py:186} INFO - Started process (PID=515) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:44:01.953+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:44:01.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:01.955+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:44:02.286+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:02.286+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:02.293+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:44:02.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:02.385+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:02.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:02.396+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:44:02.415+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.468 seconds
[2025-07-18T10:44:33.440+0000] {processor.py:186} INFO - Started process (PID=651) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:44:33.440+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:44:33.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:33.442+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:44:33.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:33.641+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:33.651+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:44:33.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:33.754+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:33.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:33.765+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:44:33.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.350 seconds
[2025-07-18T10:45:03.920+0000] {processor.py:186} INFO - Started process (PID=787) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:45:03.921+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:45:03.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:03.923+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:45:04.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:04.125+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:04.133+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:45:04.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:04.245+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:04.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:04.256+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:45:04.276+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.362 seconds
[2025-07-18T10:45:34.546+0000] {processor.py:186} INFO - Started process (PID=923) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:45:34.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:45:34.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:34.548+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:45:34.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:34.754+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:34.763+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:45:34.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:34.864+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:34.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:34.877+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:45:34.899+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.359 seconds
[2025-07-18T10:46:05.464+0000] {processor.py:186} INFO - Started process (PID=1059) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:46:05.465+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:46:05.467+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:05.467+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:46:05.674+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:05.673+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:05.682+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:46:05.787+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:05.787+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:05.799+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:05.799+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:46:05.821+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.363 seconds
[2025-07-18T10:46:36.383+0000] {processor.py:186} INFO - Started process (PID=1195) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:46:36.384+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:46:36.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:36.387+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:46:36.618+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:36.618+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:36.629+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:46:36.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:36.746+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:36.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:36.759+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:46:36.783+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.407 seconds
[2025-07-18T10:47:06.890+0000] {processor.py:186} INFO - Started process (PID=1331) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:47:06.891+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:47:06.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:06.893+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:47:07.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.110+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:07.119+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:47:07.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.233+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:07.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:07.245+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:47:07.266+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.381 seconds
[2025-07-18T10:48:04.419+0000] {processor.py:186} INFO - Started process (PID=241) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:48:04.420+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:48:04.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.422+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:48:04.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.755+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:04.764+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:48:04.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.856+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:04.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.865+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:48:04.885+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.472 seconds
[2025-07-18T10:48:35.441+0000] {processor.py:186} INFO - Started process (PID=377) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:48:35.442+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:48:35.444+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.444+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:48:35.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.795+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:35.800+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:48:35.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.893+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:35.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.901+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:48:35.920+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.487 seconds
[2025-07-18T10:49:06.091+0000] {processor.py:186} INFO - Started process (PID=513) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:49:06.092+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:49:06.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:06.095+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:49:06.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:06.439+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:06.447+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:49:06.542+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:06.541+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:06.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:06.552+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:49:06.569+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.484 seconds
[2025-07-18T10:49:37.512+0000] {processor.py:186} INFO - Started process (PID=649) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:49:37.513+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:49:37.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.515+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:49:37.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.712+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:37.722+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:49:37.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.824+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:37.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.835+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:49:37.859+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.353 seconds
[2025-07-18T10:50:08.198+0000] {processor.py:186} INFO - Started process (PID=785) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:50:08.199+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:50:08.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.202+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:50:08.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.455+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:08.464+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:50:08.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.574+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:08.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.586+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:50:08.607+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.418 seconds
[2025-07-18T10:50:38.999+0000] {processor.py:186} INFO - Started process (PID=921) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:50:39.000+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:50:39.002+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.002+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:50:39.207+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.207+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:39.216+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:50:39.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.320+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:39.331+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:39.331+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:50:39.352+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.358 seconds
[2025-07-18T10:51:09.702+0000] {processor.py:186} INFO - Started process (PID=1057) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:51:09.703+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:51:09.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:09.705+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:51:09.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:09.901+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:09.909+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:51:10.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.008+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:10.022+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:10.022+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:51:10.048+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.352 seconds
[2025-07-18T10:51:40.232+0000] {processor.py:186} INFO - Started process (PID=1194) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:51:40.233+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:51:40.235+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.235+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:51:40.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.434+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:40.442+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:51:40.534+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.534+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:40.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.545+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:51:40.565+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.339 seconds
[2025-07-18T10:52:10.680+0000] {processor.py:186} INFO - Started process (PID=1330) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:52:10.681+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:52:10.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.683+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:52:10.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.869+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:10.878+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:52:10.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.971+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:10.981+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.981+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:52:10.999+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.326 seconds
[2025-07-18T10:52:41.309+0000] {processor.py:186} INFO - Started process (PID=1466) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:52:41.310+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:52:41.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:41.312+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:52:41.521+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:41.520+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:41.528+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:52:41.625+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:41.625+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:41.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:41.637+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:52:41.658+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.357 seconds
[2025-07-18T10:53:11.857+0000] {processor.py:186} INFO - Started process (PID=1602) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:53:11.858+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:53:11.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:11.860+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:53:12.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.059+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:12.068+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:53:12.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.168+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:12.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:12.180+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:53:12.202+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.350 seconds
[2025-07-18T10:53:42.857+0000] {processor.py:186} INFO - Started process (PID=1740) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:53:42.858+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:53:42.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:42.859+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:53:43.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.034+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:43.042+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:53:43.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.123+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:43.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:43.133+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:53:43.149+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.298 seconds
[2025-07-18T10:54:13.782+0000] {processor.py:186} INFO - Started process (PID=1876) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:54:13.783+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:54:13.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:13.785+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:54:13.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:13.995+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:14.005+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:54:14.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.105+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:14.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:14.116+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:54:14.135+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.359 seconds
[2025-07-18T10:54:44.382+0000] {processor.py:186} INFO - Started process (PID=2012) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:54:44.383+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:54:44.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.385+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:54:44.584+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.584+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:44.593+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:54:44.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.690+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:44.702+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.702+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:54:44.719+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.343 seconds
[2025-07-18T10:55:14.916+0000] {processor.py:186} INFO - Started process (PID=2148) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:55:14.917+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:55:14.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:14.920+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:55:15.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.102+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:15.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:55:15.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.194+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:15.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:15.203+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:55:15.219+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.309 seconds
[2025-07-18T10:55:45.766+0000] {processor.py:186} INFO - Started process (PID=2282) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:55:45.767+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:55:45.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:45.769+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:55:45.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:45.965+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:45.973+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:55:46.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.065+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:46.080+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:46.080+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:55:46.099+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.339 seconds
[2025-07-18T10:57:29.083+0000] {processor.py:186} INFO - Started process (PID=241) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:57:29.084+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:57:29.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.085+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:57:29.409+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.409+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:29.417+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:57:29.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.505+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:29.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.515+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:57:29.534+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.458 seconds
[2025-07-18T10:58:00.723+0000] {processor.py:186} INFO - Started process (PID=377) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:58:00.724+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:58:00.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.725+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:58:01.071+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.071+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:01.077+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:58:01.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.163+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:01.173+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:01.173+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:58:01.192+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.475 seconds
[2025-07-18T10:58:31.799+0000] {processor.py:186} INFO - Started process (PID=513) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:58:31.801+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:58:31.804+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.803+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:58:32.172+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:32.171+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:32.178+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:58:32.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:32.290+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:32.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:32.301+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:58:32.320+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.527 seconds
[2025-07-18T10:59:02.624+0000] {processor.py:186} INFO - Started process (PID=651) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:59:02.624+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:59:02.627+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.626+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:59:02.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.813+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:02.823+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:59:02.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.931+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:02.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.943+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:59:02.963+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.344 seconds
[2025-07-18T10:59:33.171+0000] {processor.py:186} INFO - Started process (PID=785) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:59:33.172+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T10:59:33.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.173+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:59:33.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.357+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:33.366+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T10:59:33.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.453+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:33.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.463+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T10:59:33.481+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.315 seconds
[2025-07-18T11:00:03.887+0000] {processor.py:186} INFO - Started process (PID=921) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:00:03.888+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:00:03.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.890+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:00:04.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.126+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:04.134+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:00:04.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.241+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:04.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:04.253+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:00:04.274+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.393 seconds
[2025-07-18T11:00:34.587+0000] {processor.py:186} INFO - Started process (PID=1057) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:00:34.589+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:00:34.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.592+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:00:34.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.857+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:34.871+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:00:34.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.983+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:34.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.994+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:00:35.017+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.438 seconds
[2025-07-18T11:01:05.132+0000] {processor.py:186} INFO - Started process (PID=1193) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:01:05.133+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:01:05.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.135+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:01:05.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.327+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:05.335+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:01:05.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.426+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:05.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.437+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:01:05.455+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.329 seconds
[2025-07-18T11:01:35.853+0000] {processor.py:186} INFO - Started process (PID=1331) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:01:35.853+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:01:35.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.855+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:01:36.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:36.032+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:36.041+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:01:36.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:36.127+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:36.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:36.137+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:01:36.154+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.307 seconds
[2025-07-18T11:02:07.177+0000] {processor.py:186} INFO - Started process (PID=1467) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:02:07.178+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:02:07.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:07.180+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:02:07.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:07.379+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:07.389+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:02:07.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:07.492+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:07.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:07.503+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:02:07.525+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.353 seconds
[2025-07-18T11:02:37.807+0000] {processor.py:186} INFO - Started process (PID=1601) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:02:37.808+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:02:37.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:37.809+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:02:38.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:38.020+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:38.030+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:02:38.132+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:38.132+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:38.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:38.144+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:02:38.165+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.364 seconds
[2025-07-18T11:03:09.318+0000] {processor.py:186} INFO - Started process (PID=1739) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:03:09.319+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:03:09.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.321+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:03:09.534+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.534+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:09.544+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:03:09.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.681+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:09.696+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:09.696+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:03:09.715+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.404 seconds
[2025-07-18T11:03:40.035+0000] {processor.py:186} INFO - Started process (PID=1875) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:03:40.036+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:03:40.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:40.039+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:03:40.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:40.250+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:40.259+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:03:40.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:40.357+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:40.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:40.369+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:03:40.388+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.361 seconds
[2025-07-18T11:04:10.755+0000] {processor.py:186} INFO - Started process (PID=2011) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:04:10.757+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:04:10.759+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:10.759+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:04:10.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:10.969+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:10.980+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:04:11.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.086+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:11.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:11.097+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:04:11.116+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.367 seconds
[2025-07-18T11:04:41.416+0000] {processor.py:186} INFO - Started process (PID=2147) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:04:41.417+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:04:41.420+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:41.419+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:04:41.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:41.657+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:41.666+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:04:41.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:41.775+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:41.787+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:41.786+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:04:41.805+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.397 seconds
[2025-07-18T11:05:12.141+0000] {processor.py:186} INFO - Started process (PID=2283) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:05:12.142+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:05:12.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:12.144+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:05:12.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:12.366+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:12.377+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:05:12.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:12.483+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:12.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:12.495+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:05:12.514+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.379 seconds
[2025-07-18T11:06:51.617+0000] {processor.py:186} INFO - Started process (PID=247) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:06:51.618+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:06:51.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.620+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:06:51.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.966+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:51.970+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:06:52.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.061+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:52.071+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:52.071+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:06:52.092+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.480 seconds
[2025-07-18T11:07:22.942+0000] {processor.py:186} INFO - Started process (PID=394) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:07:22.943+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:07:22.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.946+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:07:23.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.290+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:23.297+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:07:23.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.406+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:23.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:23.419+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:07:23.437+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.502 seconds
[2025-07-18T11:07:54.315+0000] {processor.py:186} INFO - Started process (PID=535) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:07:54.316+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:07:54.318+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.318+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:07:54.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.658+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:54.664+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:07:54.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.766+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:54.776+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.776+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:07:54.797+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.489 seconds
[2025-07-18T11:08:25.332+0000] {processor.py:186} INFO - Started process (PID=676) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:08:25.333+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:08:25.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.334+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:08:25.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.546+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:25.554+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:08:25.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.664+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:25.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.675+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:08:25.698+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.372 seconds
[2025-07-18T11:08:55.856+0000] {processor.py:186} INFO - Started process (PID=817) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:08:55.857+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:08:55.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.859+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:08:56.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.070+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:56.081+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:08:56.183+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.182+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:56.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:56.194+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:08:56.212+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.362 seconds
[2025-07-18T11:09:26.821+0000] {processor.py:186} INFO - Started process (PID=958) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:09:26.822+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:09:26.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:26.824+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:09:27.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.047+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:27.057+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:09:27.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.163+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:27.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:27.176+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:09:27.197+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.382 seconds
[2025-07-18T11:09:57.255+0000] {processor.py:186} INFO - Started process (PID=1096) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:09:57.256+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:09:57.259+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.258+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:09:57.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.471+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:57.479+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:09:57.594+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.594+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:57.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.607+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:09:57.628+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.380 seconds
[2025-07-18T11:10:28.093+0000] {processor.py:186} INFO - Started process (PID=1237) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:10:28.094+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:10:28.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.096+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:10:28.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.281+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:28.290+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:10:28.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.385+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:28.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.396+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:10:28.415+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.328 seconds
[2025-07-18T11:10:59.121+0000] {processor.py:186} INFO - Started process (PID=1376) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:10:59.122+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:10:59.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.124+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:10:59.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.329+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:59.339+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:10:59.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.442+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:59.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.452+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:10:59.472+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.357 seconds
[2025-07-18T11:11:30.099+0000] {processor.py:186} INFO - Started process (PID=1524) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:11:30.100+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:11:30.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.102+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:11:30.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.290+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:30.300+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:11:30.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.391+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:30.401+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:30.400+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:11:30.420+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.327 seconds
[2025-07-18T11:12:00.808+0000] {processor.py:186} INFO - Started process (PID=1665) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:12:00.809+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:12:00.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:00.811+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:12:01.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.016+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:01.025+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:12:01.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.128+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:01.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:01.138+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:12:01.159+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.357 seconds
[2025-07-18T11:12:31.356+0000] {processor.py:186} INFO - Started process (PID=1804) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:12:31.357+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:12:31.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.359+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:12:31.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.557+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:31.565+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:12:31.662+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.662+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:31.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.673+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:12:31.689+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.339 seconds
[2025-07-18T11:13:03.496+0000] {processor.py:186} INFO - Started process (PID=1947) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:13:03.497+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:13:03.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:03.499+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:13:03.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:03.742+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:03.750+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:13:03.846+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:03.845+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:03.857+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:03.856+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:13:03.873+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.383 seconds
[2025-07-18T11:13:33.931+0000] {processor.py:186} INFO - Started process (PID=2088) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:13:33.932+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:13:33.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.935+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:13:34.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:34.162+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:34.169+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:13:34.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:34.261+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:34.273+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:34.273+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:13:34.291+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.367 seconds
[2025-07-18T11:14:04.945+0000] {processor.py:186} INFO - Started process (PID=2229) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:14:04.946+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:14:04.949+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.949+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:14:05.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.166+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:05.175+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:14:05.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.280+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:05.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:05.291+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:14:05.312+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.374 seconds
[2025-07-18T11:14:35.427+0000] {processor.py:186} INFO - Started process (PID=2370) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:14:35.428+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:14:35.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:35.429+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:14:35.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:35.636+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:35.647+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:14:35.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:35.761+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:35.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:35.771+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:14:35.792+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.371 seconds
[2025-07-18T11:15:06.177+0000] {processor.py:186} INFO - Started process (PID=2511) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:15:06.178+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:15:06.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.181+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:15:06.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.407+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:06.416+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:15:06.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.548+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:06.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:06.561+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:15:06.581+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.411 seconds
[2025-07-18T11:15:37.027+0000] {processor.py:186} INFO - Started process (PID=2650) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:15:37.028+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:15:37.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.032+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:15:37.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.252+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:37.262+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:15:37.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.393+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:37.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:37.408+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:15:37.431+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.411 seconds
[2025-07-18T11:16:07.645+0000] {processor.py:186} INFO - Started process (PID=2791) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:16:07.645+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:16:07.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.648+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:16:07.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.861+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:07.869+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:16:07.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.985+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:08.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:08.000+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:16:08.022+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.385 seconds
[2025-07-18T11:16:38.305+0000] {processor.py:186} INFO - Started process (PID=2932) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:16:38.306+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:16:38.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:38.308+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:16:38.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:38.506+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:38.515+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:16:38.619+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:38.619+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:38.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:38.631+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:16:38.649+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.351 seconds
[2025-07-18T11:17:09.631+0000] {processor.py:186} INFO - Started process (PID=3073) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:17:09.632+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:17:09.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:09.635+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:17:09.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:09.873+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:09.884+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:17:10.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.062+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:10.076+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:10.076+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:17:10.098+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.473 seconds
[2025-07-18T11:17:40.457+0000] {processor.py:186} INFO - Started process (PID=3214) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:17:40.458+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:17:40.460+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:40.460+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:17:40.686+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:40.686+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:40.696+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:17:40.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:40.836+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:40.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:40.850+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:17:40.870+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.420 seconds
[2025-07-18T11:18:11.183+0000] {processor.py:186} INFO - Started process (PID=3355) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:18:11.184+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:18:11.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.186+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:18:11.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.430+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:11.438+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:18:11.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.550+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:11.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.563+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:18:11.585+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.409 seconds
[2025-07-18T11:18:41.962+0000] {processor.py:186} INFO - Started process (PID=3496) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:18:41.963+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:18:41.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.965+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:18:42.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.165+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:42.174+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:18:42.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.269+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:42.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:42.282+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:18:42.300+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.344 seconds
[2025-07-18T11:19:12.870+0000] {processor.py:186} INFO - Started process (PID=3637) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:19:12.872+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:19:12.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:12.874+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:19:13.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:13.120+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:13.130+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:19:13.261+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:13.260+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:13.274+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:13.274+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:19:13.297+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.435 seconds
[2025-07-18T11:19:43.921+0000] {processor.py:186} INFO - Started process (PID=3783) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:19:43.922+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:19:43.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:43.925+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:19:44.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.165+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:44.175+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:19:44.286+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.285+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:44.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:44.299+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:19:44.326+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.412 seconds
[2025-07-18T11:20:14.768+0000] {processor.py:186} INFO - Started process (PID=3924) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:20:14.769+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:20:14.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:14.771+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:20:14.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:14.983+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:14.992+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:20:15.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.097+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:15.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:15.109+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:20:15.132+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.371 seconds
