[2025-07-18T10:16:50.908+0000] {processor.py:186} INFO - Started process (PID=291) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:16:50.909+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:16:50.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.912+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:16:50.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.955+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:50.960+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:16:50.979+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.076 seconds
[2025-07-18T10:17:21.108+0000] {processor.py:186} INFO - Started process (PID=424) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:17:21.109+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:17:21.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:21.111+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:17:21.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:21.142+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:21.146+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:17:21.163+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.061 seconds
[2025-07-18T10:17:52.163+0000] {processor.py:186} INFO - Started process (PID=555) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:17:52.164+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:17:52.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:52.165+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:17:52.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:52.198+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:52.203+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:17:52.218+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.061 seconds
[2025-07-18T10:18:23.022+0000] {processor.py:186} INFO - Started process (PID=686) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:18:23.023+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:18:23.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:23.024+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:18:23.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:23.062+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:23.066+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:18:23.081+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.065 seconds
[2025-07-18T10:18:53.984+0000] {processor.py:186} INFO - Started process (PID=817) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:18:53.985+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:18:53.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:53.986+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:18:54.018+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:54.014+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:54.019+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:18:54.035+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.056 seconds
[2025-07-18T10:19:24.922+0000] {processor.py:186} INFO - Started process (PID=948) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:19:24.923+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:19:24.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:24.924+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:19:24.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:24.954+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:24.958+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:19:24.973+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.056 seconds
[2025-07-18T10:19:55.937+0000] {processor.py:186} INFO - Started process (PID=1079) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:19:55.939+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:19:55.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:55.940+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:19:55.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:55.973+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:55.977+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:19:55.993+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.061 seconds
[2025-07-18T10:20:26.929+0000] {processor.py:186} INFO - Started process (PID=1210) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:20:26.930+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:20:26.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:26.931+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:20:26.964+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:26.961+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:26.965+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:20:26.981+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.057 seconds
[2025-07-18T10:20:57.875+0000] {processor.py:186} INFO - Started process (PID=1341) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:20:57.876+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:20:57.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.877+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:20:57.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.909+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:57.912+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:20:57.927+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.057 seconds
[2025-07-18T10:21:28.891+0000] {processor.py:186} INFO - Started process (PID=1472) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:21:28.892+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:21:28.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.893+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:21:28.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.924+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:28.928+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:21:28.944+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.059 seconds
[2025-07-18T10:21:59.054+0000] {processor.py:186} INFO - Started process (PID=1603) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:21:59.055+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:21:59.056+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:59.056+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:21:59.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:59.085+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:59.088+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:21:59.104+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.055 seconds
[2025-07-18T10:22:30.070+0000] {processor.py:186} INFO - Started process (PID=1734) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:22:30.070+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:22:30.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:30.071+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:22:30.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:30.101+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:30.104+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:22:30.121+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.057 seconds
[2025-07-18T10:23:01.036+0000] {processor.py:186} INFO - Started process (PID=1865) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:23:01.037+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:23:01.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:01.038+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:23:01.075+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:01.072+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:01.076+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:23:01.091+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.061 seconds
[2025-07-18T10:23:31.877+0000] {processor.py:186} INFO - Started process (PID=1996) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:23:31.878+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:23:31.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.879+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:23:31.913+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.910+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:31.914+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:23:31.929+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.057 seconds
[2025-07-18T10:24:02.111+0000] {processor.py:186} INFO - Started process (PID=2125) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:24:02.112+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:24:02.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:02.113+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:24:02.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:02.146+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:02.151+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:24:02.168+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.063 seconds
[2025-07-18T10:24:33.086+0000] {processor.py:186} INFO - Started process (PID=2256) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:24:33.088+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:24:33.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:33.089+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:24:33.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:33.122+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_analyze_food_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:33.125+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:24:33.140+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.061 seconds
[2025-07-18T10:25:06.806+0000] {processor.py:186} INFO - Started process (PID=2389) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:25:06.806+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:25:06.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.807+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:25:06.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.988+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:06.997+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:25:07.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:07.085+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:07.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:07.095+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:25:07.114+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.314 seconds
[2025-07-18T10:26:25.365+0000] {processor.py:186} INFO - Started process (PID=293) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:26:25.366+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:26:25.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.367+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:26:25.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.722+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:25.728+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:26:25.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.821+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:25.831+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.831+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:26:25.850+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.491 seconds
[2025-07-18T10:26:57.260+0000] {processor.py:186} INFO - Started process (PID=427) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:26:57.261+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:26:57.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.262+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:26:57.601+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.601+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:57.608+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:26:57.710+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.709+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:57.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.723+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:26:57.749+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.495 seconds
[2025-07-18T10:27:28.463+0000] {processor.py:186} INFO - Started process (PID=558) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:27:28.464+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:27:28.467+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.467+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:27:28.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.675+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:28.684+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:27:28.787+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.786+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:28.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.797+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:27:28.817+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.360 seconds
[2025-07-18T10:27:59.757+0000] {processor.py:186} INFO - Started process (PID=691) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:27:59.758+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:27:59.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.760+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:27:59.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.952+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:59.961+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:28:00.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:00.061+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:00.071+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:00.070+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:28:00.089+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.339 seconds
[2025-07-18T10:28:30.279+0000] {processor.py:186} INFO - Started process (PID=820) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:28:30.280+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:28:30.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.282+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:28:30.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.500+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:30.507+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:28:30.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.607+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:30.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.620+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:28:30.638+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.367 seconds
[2025-07-18T10:29:01.036+0000] {processor.py:186} INFO - Started process (PID=953) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:29:01.037+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:29:01.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:01.040+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:29:01.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:01.244+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:01.252+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:29:01.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:01.350+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:01.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:01.362+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:29:01.387+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.358 seconds
[2025-07-18T10:29:31.681+0000] {processor.py:186} INFO - Started process (PID=1084) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:29:31.682+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:29:31.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.684+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:29:31.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.884+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:31.893+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:29:32.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:32.006+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:32.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:32.019+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:29:32.038+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.362 seconds
[2025-07-18T10:30:02.454+0000] {processor.py:186} INFO - Started process (PID=1218) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:30:02.456+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:30:02.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.458+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:30:02.674+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.674+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:02.684+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:30:02.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.783+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:02.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.795+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:30:02.816+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.368 seconds
[2025-07-18T10:30:32.890+0000] {processor.py:186} INFO - Started process (PID=1349) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:30:32.891+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:30:32.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.894+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:30:33.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:33.090+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:33.100+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:30:33.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:33.194+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:33.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:33.204+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:30:33.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.338 seconds
[2025-07-18T10:31:03.720+0000] {processor.py:186} INFO - Started process (PID=1480) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:31:03.721+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:31:03.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.724+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:31:03.963+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.963+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:03.972+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:31:04.077+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:04.077+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:04.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:04.091+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:31:04.111+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.400 seconds
[2025-07-18T10:31:34.423+0000] {processor.py:186} INFO - Started process (PID=1613) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:31:34.424+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:31:34.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.426+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:31:34.616+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.616+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:34.627+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:31:34.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.723+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:34.734+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.733+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:31:34.754+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.336 seconds
[2025-07-18T10:32:04.907+0000] {processor.py:186} INFO - Started process (PID=1742) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:32:04.910+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:32:04.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.917+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:32:05.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:05.141+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:05.152+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:32:05.315+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:05.314+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:05.328+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:05.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:32:05.355+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.457 seconds
[2025-07-18T10:32:35.469+0000] {processor.py:186} INFO - Started process (PID=1873) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:32:35.470+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:32:35.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.472+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:32:35.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.671+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:35.679+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:32:35.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.772+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:35.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.781+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:32:35.798+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.334 seconds
[2025-07-18T10:33:06.034+0000] {processor.py:186} INFO - Started process (PID=2004) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:33:06.035+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:33:06.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:06.037+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:33:06.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:06.231+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:06.240+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:33:06.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:06.329+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:06.338+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:06.338+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:33:06.355+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.328 seconds
[2025-07-18T10:33:36.529+0000] {processor.py:186} INFO - Started process (PID=2130) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:33:36.530+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:33:36.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.532+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:33:36.735+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.734+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:36.742+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:33:36.832+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.832+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:36.842+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.842+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:33:36.861+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.338 seconds
[2025-07-18T10:34:07.176+0000] {processor.py:186} INFO - Started process (PID=2261) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:34:07.177+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:34:07.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.179+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:34:07.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.384+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:07.392+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:34:07.480+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.480+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:07.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.489+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:34:07.511+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.339 seconds
[2025-07-18T10:34:38.448+0000] {processor.py:186} INFO - Started process (PID=2394) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:34:38.449+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:34:38.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.451+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:34:38.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.657+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:38.666+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:34:38.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.767+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:38.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.777+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:34:38.798+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.355 seconds
[2025-07-18T10:35:09.026+0000] {processor.py:186} INFO - Started process (PID=2525) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:35:09.028+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:35:09.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:09.030+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:35:09.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:09.251+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:09.262+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:35:09.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:09.359+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:09.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:09.370+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:35:09.391+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.371 seconds
[2025-07-18T10:35:39.858+0000] {processor.py:186} INFO - Started process (PID=2656) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:35:39.858+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:35:39.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.861+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:35:40.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:40.073+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:40.084+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:35:40.182+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:40.182+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:40.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:40.193+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:35:40.214+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.363 seconds
[2025-07-18T10:36:10.637+0000] {processor.py:186} INFO - Started process (PID=2785) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:36:10.638+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:36:10.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.640+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:36:10.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.850+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:10.857+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:36:10.963+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.963+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:10.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.975+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:36:10.999+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.369 seconds
[2025-07-18T10:36:41.481+0000] {processor.py:186} INFO - Started process (PID=2918) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:36:41.481+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:36:41.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:41.483+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:36:41.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:41.675+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:41.684+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:36:41.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:41.790+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:41.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:41.802+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:36:41.825+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.350 seconds
[2025-07-18T10:37:12.221+0000] {processor.py:186} INFO - Started process (PID=3049) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:37:12.222+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:37:12.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:12.224+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:37:12.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:12.425+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:12.436+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:37:12.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:12.545+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:12.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:12.559+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:37:12.580+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.365 seconds
[2025-07-18T10:37:43.211+0000] {processor.py:186} INFO - Started process (PID=3180) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:37:43.212+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:37:43.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.213+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:37:43.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.396+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:43.405+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:37:43.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.496+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:43.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.506+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:37:43.524+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.319 seconds
[2025-07-18T10:38:13.866+0000] {processor.py:186} INFO - Started process (PID=3311) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:38:13.867+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:38:13.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.870+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:38:14.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:14.117+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:14.124+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:38:14.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:14.219+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:14.229+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:14.229+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:38:14.253+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.393 seconds
[2025-07-18T10:38:44.486+0000] {processor.py:186} INFO - Started process (PID=3442) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:38:44.487+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:38:44.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.489+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:38:44.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.671+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:44.681+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:38:44.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.769+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:44.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.778+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:38:44.796+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.315 seconds
[2025-07-18T10:39:15.157+0000] {processor.py:186} INFO - Started process (PID=3566) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:39:15.158+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:39:15.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.161+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:39:15.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.390+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:15.401+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:39:15.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.515+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:15.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.527+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:39:15.547+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.400 seconds
[2025-07-18T10:39:45.654+0000] {processor.py:186} INFO - Started process (PID=3697) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:39:45.654+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:39:45.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.656+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:39:45.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.861+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:45.871+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:39:45.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.971+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:45.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.982+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:39:46.008+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.360 seconds
[2025-07-18T10:40:16.569+0000] {processor.py:186} INFO - Started process (PID=3828) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:40:16.570+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:40:16.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.572+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:40:16.787+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.786+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:16.797+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:40:16.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:16.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.943+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:40:16.963+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.402 seconds
[2025-07-18T10:40:47.501+0000] {processor.py:186} INFO - Started process (PID=3959) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:40:47.502+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:40:47.504+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.504+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:40:47.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.688+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:47.698+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:40:47.807+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.807+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:47.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.820+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:40:47.845+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.349 seconds
[2025-07-18T10:41:18.075+0000] {processor.py:186} INFO - Started process (PID=4090) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:41:18.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:41:18.080+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:18.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:41:18.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:18.304+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:18.315+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:41:18.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:18.421+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:18.432+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:18.432+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:41:18.593+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.527 seconds
[2025-07-18T10:41:48.821+0000] {processor.py:186} INFO - Started process (PID=4226) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:41:48.822+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:41:48.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:48.824+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:41:49.018+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:49.018+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:49.028+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:41:49.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:49.121+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:49.131+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:49.131+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:41:49.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.452 seconds
[2025-07-18T10:42:19.444+0000] {processor.py:186} INFO - Started process (PID=4362) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:42:19.445+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:42:19.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.447+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:42:19.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.665+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:19.674+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:42:19.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.813+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:19.993+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.993+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:42:20.009+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.571 seconds
[2025-07-18T10:43:01.549+0000] {processor.py:186} INFO - Started process (PID=296) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:43:01.551+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:43:01.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.553+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:43:01.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.874+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:01.882+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:43:01.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.965+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:01.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.974+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:43:01.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.448 seconds
[2025-07-18T10:43:33.995+0000] {processor.py:186} INFO - Started process (PID=432) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:43:33.996+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:43:33.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.998+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:43:34.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:34.363+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:34.368+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:43:34.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:34.455+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:34.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:34.462+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:43:34.479+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.489 seconds
[2025-07-18T10:44:05.171+0000] {processor.py:186} INFO - Started process (PID=568) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:44:05.172+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:44:05.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.174+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:44:05.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.349+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:05.356+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:44:05.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.452+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:05.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.462+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:44:05.481+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.316 seconds
[2025-07-18T10:44:36.446+0000] {processor.py:186} INFO - Started process (PID=704) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:44:36.447+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:44:36.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:36.448+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:44:36.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:36.631+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:36.639+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:44:36.730+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:36.729+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:36.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:36.739+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:44:36.757+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.317 seconds
[2025-07-18T10:45:07.513+0000] {processor.py:186} INFO - Started process (PID=840) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:45:07.514+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:45:07.517+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:07.516+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:45:07.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:07.733+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:07.743+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:45:07.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:07.865+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:07.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:07.879+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:45:07.899+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.392 seconds
[2025-07-18T10:45:37.988+0000] {processor.py:186} INFO - Started process (PID=976) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:45:37.989+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:45:37.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:37.991+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:45:38.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:38.180+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:38.191+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:45:38.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:38.298+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:38.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:38.310+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:45:38.333+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.351 seconds
[2025-07-18T10:46:08.732+0000] {processor.py:186} INFO - Started process (PID=1112) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:46:08.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:46:08.735+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:08.735+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:46:08.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:08.935+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:08.943+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:46:09.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:09.051+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:09.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:09.063+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:46:09.085+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.358 seconds
[2025-07-18T10:46:40.049+0000] {processor.py:186} INFO - Started process (PID=1248) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:46:40.050+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:46:40.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.053+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:46:40.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.280+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:40.291+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:46:40.409+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.409+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:40.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.429+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:46:40.454+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.412 seconds
[2025-07-18T10:48:07.087+0000] {processor.py:186} INFO - Started process (PID=296) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:48:07.088+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:48:07.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.089+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:48:07.413+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.413+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:07.421+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:48:07.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.523+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:07.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.532+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:48:07.552+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.471 seconds
[2025-07-18T10:48:38.271+0000] {processor.py:186} INFO - Started process (PID=437) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:48:38.272+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:48:38.274+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.274+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:48:38.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.585+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:38.593+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:48:38.690+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.689+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:38.698+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.698+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:48:38.716+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.451 seconds
[2025-07-18T10:49:10.076+0000] {processor.py:186} INFO - Started process (PID=575) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:49:10.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:49:10.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:10.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:49:10.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:10.292+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:10.303+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:49:10.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:10.399+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:10.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:10.410+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:49:10.428+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.358 seconds
[2025-07-18T10:49:40.510+0000] {processor.py:186} INFO - Started process (PID=711) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:49:40.510+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:49:40.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.512+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:49:40.696+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.696+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:40.707+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:49:40.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.823+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:40.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.838+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:49:40.862+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.357 seconds
[2025-07-18T10:50:11.242+0000] {processor.py:186} INFO - Started process (PID=847) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:50:11.242+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:50:11.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.244+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:50:11.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.452+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:11.463+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:50:11.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.569+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:11.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.579+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:50:11.602+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.366 seconds
[2025-07-18T10:50:41.986+0000] {processor.py:186} INFO - Started process (PID=983) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:50:41.987+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:50:41.989+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.988+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:50:42.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:42.189+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:42.197+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:50:42.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:42.301+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:42.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:42.313+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:50:42.333+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.354 seconds
[2025-07-18T10:51:12.680+0000] {processor.py:186} INFO - Started process (PID=1119) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:51:12.681+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:51:12.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.683+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:51:12.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.863+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:12.874+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:51:12.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.971+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:12.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.982+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:51:13.000+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.326 seconds
[2025-07-18T10:51:43.982+0000] {processor.py:186} INFO - Started process (PID=1256) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:51:43.983+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:51:43.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:43.985+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:51:44.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:44.179+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:44.187+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:51:44.282+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:44.282+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:44.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:44.292+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:51:44.309+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.333 seconds
[2025-07-18T10:52:14.438+0000] {processor.py:186} INFO - Started process (PID=1392) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:52:14.439+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:52:14.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:14.441+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:52:14.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:14.632+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:14.640+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:52:14.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:14.731+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:14.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:14.741+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:52:14.759+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.327 seconds
[2025-07-18T10:52:45.159+0000] {processor.py:186} INFO - Started process (PID=1528) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:52:45.160+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:52:45.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:45.162+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:52:45.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:45.361+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:45.370+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:52:45.465+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:45.465+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:45.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:45.475+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:52:45.495+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.342 seconds
[2025-07-18T10:53:15.717+0000] {processor.py:186} INFO - Started process (PID=1664) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:53:15.718+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:53:15.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:15.721+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:53:15.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:15.924+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:15.934+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:53:16.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:16.046+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:16.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:16.058+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:53:16.080+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.369 seconds
[2025-07-18T10:53:46.418+0000] {processor.py:186} INFO - Started process (PID=1800) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:53:46.419+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:53:46.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:46.422+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:53:46.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:46.606+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:46.615+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:53:46.702+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:46.702+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:46.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:46.712+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:53:46.731+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.321 seconds
[2025-07-18T10:54:17.735+0000] {processor.py:186} INFO - Started process (PID=1936) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:54:17.736+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:54:17.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:17.738+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:54:17.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:17.979+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:17.987+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:54:18.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:18.088+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:18.098+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:18.098+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:54:18.119+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.391 seconds
[2025-07-18T10:54:48.896+0000] {processor.py:186} INFO - Started process (PID=2072) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:54:48.897+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:54:48.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:48.899+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:54:49.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:49.102+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:49.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:54:49.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:49.210+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:49.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:49.221+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:54:49.241+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.351 seconds
[2025-07-18T10:55:19.820+0000] {processor.py:186} INFO - Started process (PID=2208) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:55:19.821+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:55:19.823+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:19.823+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:55:20.006+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:20.005+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:20.015+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:55:20.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:20.104+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:20.115+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:20.115+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:55:20.136+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.321 seconds
[2025-07-18T10:57:31.706+0000] {processor.py:186} INFO - Started process (PID=296) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:57:31.707+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:57:31.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.709+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:57:32.027+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:32.027+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:32.034+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:57:32.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:32.130+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:32.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:32.139+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:57:32.159+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.459 seconds
[2025-07-18T10:58:03.163+0000] {processor.py:186} INFO - Started process (PID=432) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:58:03.164+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:58:03.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.166+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:58:03.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.476+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:03.483+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:58:03.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.570+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:03.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.579+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:58:03.599+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.441 seconds
[2025-07-18T10:58:34.920+0000] {processor.py:186} INFO - Started process (PID=568) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:58:34.921+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:58:34.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.923+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:58:35.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:35.109+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:35.118+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:58:35.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:35.209+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:35.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:35.219+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:58:35.235+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.320 seconds
[2025-07-18T10:59:05.313+0000] {processor.py:186} INFO - Started process (PID=704) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:59:05.314+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:59:05.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.316+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:59:05.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.503+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:05.512+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:59:05.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.611+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:05.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.621+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:59:05.642+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.335 seconds
[2025-07-18T10:59:35.840+0000] {processor.py:186} INFO - Started process (PID=840) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:59:35.841+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T10:59:35.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.843+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:59:36.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:36.037+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:36.046+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T10:59:36.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:36.148+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:36.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:36.159+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T10:59:36.177+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.343 seconds
[2025-07-18T11:00:06.256+0000] {processor.py:186} INFO - Started process (PID=976) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:00:06.257+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:00:06.259+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.259+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:00:06.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.448+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:06.456+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:00:06.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.547+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:06.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.557+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:00:06.576+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.327 seconds
[2025-07-18T11:00:37.025+0000] {processor.py:186} INFO - Started process (PID=1112) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:00:37.026+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:00:37.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:37.028+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:00:37.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:37.215+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:37.225+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:00:37.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:37.323+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:37.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:37.334+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:00:37.351+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.332 seconds
[2025-07-18T11:01:07.786+0000] {processor.py:186} INFO - Started process (PID=1248) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:01:07.787+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:01:07.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.789+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:01:07.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.982+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:07.990+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:01:08.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:08.087+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:08.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:08.098+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:01:08.116+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.336 seconds
[2025-07-18T11:01:38.843+0000] {processor.py:186} INFO - Started process (PID=1389) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:01:38.844+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:01:38.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.847+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:01:39.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:39.046+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:39.056+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:01:39.160+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:39.159+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:39.172+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:39.172+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:01:39.195+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.358 seconds
[2025-07-18T11:02:10.559+0000] {processor.py:186} INFO - Started process (PID=1522) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:02:10.560+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:02:10.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.561+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:02:10.753+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.753+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:10.762+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:02:10.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.848+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:10.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.857+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:02:10.875+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.322 seconds
[2025-07-18T11:02:41.234+0000] {processor.py:186} INFO - Started process (PID=1658) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:02:41.235+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:02:41.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.237+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:02:41.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.430+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:41.440+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:02:41.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.536+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:41.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.546+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:02:41.566+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.339 seconds
[2025-07-18T11:03:11.909+0000] {processor.py:186} INFO - Started process (PID=1794) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:03:11.910+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:03:11.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.912+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:03:12.101+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:12.101+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:12.109+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:03:12.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:12.205+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:12.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:12.216+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:03:12.237+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.334 seconds
[2025-07-18T11:03:42.958+0000] {processor.py:186} INFO - Started process (PID=1930) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:03:42.959+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:03:42.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.961+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:03:43.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:43.149+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:43.158+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:03:43.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:43.253+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:43.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:43.263+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:03:43.282+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.330 seconds
[2025-07-18T11:04:13.941+0000] {processor.py:186} INFO - Started process (PID=2066) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:04:13.942+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:04:13.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.945+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:04:14.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:14.155+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:14.164+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:04:14.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:14.269+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:14.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:14.278+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:04:14.296+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.363 seconds
[2025-07-18T11:04:44.448+0000] {processor.py:186} INFO - Started process (PID=2202) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:04:44.448+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:04:44.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.450+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:04:44.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.641+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:44.650+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:04:44.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.802+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:44.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.815+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:04:44.836+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.395 seconds
[2025-07-18T11:05:15.321+0000] {processor.py:186} INFO - Started process (PID=2343) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:05:15.322+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:05:15.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.324+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:05:15.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.530+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:15.538+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:05:15.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.642+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:15.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.654+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:05:15.674+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.362 seconds
[2025-07-18T11:06:54.389+0000] {processor.py:186} INFO - Started process (PID=308) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:06:54.389+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:06:54.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.391+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:06:54.730+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.729+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:54.736+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:06:54.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.828+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:54.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.837+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:06:54.856+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.473 seconds
[2025-07-18T11:07:25.763+0000] {processor.py:186} INFO - Started process (PID=449) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:07:25.764+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:07:25.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.766+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:07:26.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:26.086+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:26.093+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:07:26.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:26.191+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:26.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:26.205+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:07:26.231+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.473 seconds
[2025-07-18T11:07:56.535+0000] {processor.py:186} INFO - Started process (PID=590) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:07:56.536+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:07:56.539+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.539+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:07:56.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.739+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:56.747+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:07:56.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.852+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:56.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.863+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:07:56.881+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.353 seconds
[2025-07-18T11:08:27.439+0000] {processor.py:186} INFO - Started process (PID=731) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:08:27.440+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:08:27.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.442+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:08:27.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.647+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:27.657+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:08:27.759+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.758+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:27.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.771+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:08:27.789+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.356 seconds
[2025-07-18T11:08:58.699+0000] {processor.py:186} INFO - Started process (PID=872) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:08:58.701+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:08:58.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.703+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:08:58.919+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.919+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:58.926+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:08:59.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:59.036+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:59.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:59.047+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:08:59.067+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.374 seconds
[2025-07-18T11:09:29.332+0000] {processor.py:186} INFO - Started process (PID=1013) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:09:29.333+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:09:29.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.335+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:09:29.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.533+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:29.545+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:09:29.652+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.651+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:29.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.663+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:09:29.683+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.357 seconds
[2025-07-18T11:10:00.679+0000] {processor.py:186} INFO - Started process (PID=1154) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:10:00.680+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:10:00.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.682+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:10:00.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.881+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:00.889+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:10:00.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.986+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:00.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.997+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:10:01.017+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.344 seconds
[2025-07-18T11:10:31.542+0000] {processor.py:186} INFO - Started process (PID=1302) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:10:31.544+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:10:31.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:31.547+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:10:31.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:31.767+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:31.774+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:10:31.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:31.877+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:31.886+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:31.886+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:10:31.905+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.373 seconds
[2025-07-18T11:11:02.025+0000] {processor.py:186} INFO - Started process (PID=1438) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:11:02.026+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:11:02.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:02.028+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:11:02.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:02.213+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:02.221+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:11:02.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:02.310+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:02.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:02.320+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:11:02.339+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.321 seconds
[2025-07-18T11:11:32.892+0000] {processor.py:186} INFO - Started process (PID=1587) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:11:32.893+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:11:32.896+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.895+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:11:33.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:33.128+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:33.138+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:11:33.284+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:33.283+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:33.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:33.301+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:11:33.324+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.439 seconds
[2025-07-18T11:12:03.661+0000] {processor.py:186} INFO - Started process (PID=1728) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:12:03.662+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:12:03.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.664+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:12:03.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.851+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:03.859+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:12:03.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.952+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:03.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.961+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:12:03.979+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.324 seconds
[2025-07-18T11:12:34.093+0000] {processor.py:186} INFO - Started process (PID=1861) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:12:34.094+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:12:34.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:34.096+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:12:34.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:34.285+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:34.294+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:12:34.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:34.385+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:34.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:34.394+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:12:34.411+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.324 seconds
[2025-07-18T11:13:05.825+0000] {processor.py:186} INFO - Started process (PID=2000) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:13:05.826+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:13:05.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.828+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:13:06.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:06.019+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:06.029+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:13:06.125+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:06.125+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:06.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:06.135+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:13:06.151+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.332 seconds
[2025-07-18T11:13:36.674+0000] {processor.py:186} INFO - Started process (PID=2143) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:13:36.675+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:13:36.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.677+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:13:36.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.880+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:36.889+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:13:36.981+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.980+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:36.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.990+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:13:37.008+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.340 seconds
[2025-07-18T11:14:08.132+0000] {processor.py:186} INFO - Started process (PID=2289) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:14:08.134+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:14:08.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:08.136+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:14:08.399+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:08.399+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:08.410+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:14:08.519+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:08.519+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:08.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:08.532+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:14:08.557+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.431 seconds
[2025-07-18T11:14:39.497+0000] {processor.py:186} INFO - Started process (PID=2430) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:14:39.498+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:14:39.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:39.500+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:14:39.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:39.733+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:39.743+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:14:39.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:39.860+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:39.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:39.876+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:14:39.900+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.409 seconds
[2025-07-18T11:15:10.154+0000] {processor.py:186} INFO - Started process (PID=2571) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:15:10.155+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:15:10.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:10.157+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:15:10.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:10.385+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:10.396+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:15:10.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:10.508+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:10.521+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:10.521+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:15:10.543+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.395 seconds
[2025-07-18T11:15:40.843+0000] {processor.py:186} INFO - Started process (PID=2712) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:15:40.844+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:15:40.846+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:40.846+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:15:41.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:41.074+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:41.085+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:15:41.200+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:41.199+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:41.211+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:41.211+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:15:41.233+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.396 seconds
[2025-07-18T11:16:11.634+0000] {processor.py:186} INFO - Started process (PID=2853) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:16:11.636+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:16:11.638+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:11.638+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:16:11.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:11.843+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:11.853+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:16:11.964+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:11.963+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:11.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:11.976+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:16:11.998+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.369 seconds
[2025-07-18T11:16:42.201+0000] {processor.py:186} INFO - Started process (PID=2994) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:16:42.202+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:16:42.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:42.205+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:16:42.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:42.414+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:42.423+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:16:42.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:42.528+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:42.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:42.541+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:16:42.564+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.368 seconds
[2025-07-18T11:17:12.958+0000] {processor.py:186} INFO - Started process (PID=3135) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:17:12.959+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:17:12.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.962+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:17:13.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:13.188+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:13.200+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:17:13.343+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:13.343+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:13.354+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:13.353+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:17:13.376+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.425 seconds
[2025-07-18T11:17:43.587+0000] {processor.py:186} INFO - Started process (PID=3276) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:17:43.588+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:17:43.590+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.590+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:17:43.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.843+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:43.852+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:17:43.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.975+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:43.996+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.995+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:17:44.020+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.439 seconds
[2025-07-18T11:18:14.507+0000] {processor.py:186} INFO - Started process (PID=3417) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:18:14.508+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:18:14.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.511+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:18:14.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.765+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:14.772+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:18:14.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.873+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:14.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.884+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:18:14.904+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.404 seconds
[2025-07-18T11:18:45.897+0000] {processor.py:186} INFO - Started process (PID=3558) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:18:45.899+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:18:45.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:45.902+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:18:46.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:46.127+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:46.137+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:18:46.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:46.257+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:46.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:46.271+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:18:46.294+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.406 seconds
[2025-07-18T11:19:16.598+0000] {processor.py:186} INFO - Started process (PID=3704) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:19:16.600+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:19:16.604+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:16.603+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:19:16.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:16.863+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:16.870+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:19:16.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:16.993+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:17.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:17.009+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:19:17.028+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.439 seconds
[2025-07-18T11:19:47.264+0000] {processor.py:186} INFO - Started process (PID=3845) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:19:47.265+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:19:47.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:47.267+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:19:47.473+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:47.473+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:47.483+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:19:47.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:47.597+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:47.610+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:47.610+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:19:47.632+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.375 seconds
[2025-07-18T11:20:17.991+0000] {processor.py:186} INFO - Started process (PID=3986) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:20:17.992+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:20:17.996+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.995+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:20:18.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:18.206+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:18.217+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:20:18.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:18.332+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:18.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:18.345+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:20:18.507+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.522 seconds
[2025-07-18T12:38:11.542+0000] {processor.py:186} INFO - Started process (PID=297) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:38:11.543+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T12:38:11.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.546+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:38:11.625+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.625+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:11.632+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:38:11.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.890+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_analyze_food_pipeline
[2025-07-18T12:38:11.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.899+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_analyze_food_pipeline
[2025-07-18T12:38:11.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.906+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_analyze_food_pipeline
[2025-07-18T12:38:11.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.914+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_analyze_food_pipeline
[2025-07-18T12:38:11.921+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.920+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_analyze_food_pipeline
[2025-07-18T12:38:11.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.927+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_analyze_food_pipeline
[2025-07-18T12:38:11.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.933+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_analyze_food_pipeline
[2025-07-18T12:38:11.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.934+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:11.944+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T12:38:11.945+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.945+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_analyze_food_pipeline
[2025-07-18T12:38:11.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.946+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T12:38:11.963+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.428 seconds
[2025-07-18T12:38:42.087+0000] {processor.py:186} INFO - Started process (PID=438) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:38:42.088+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T12:38:42.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:42.090+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:38:42.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:42.302+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:42.308+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:38:42.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:42.394+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:42.402+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:42.402+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T12:38:42.421+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.341 seconds
[2025-07-18T12:39:13.044+0000] {processor.py:186} INFO - Started process (PID=576) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:39:13.045+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T12:39:13.048+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:13.047+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:39:13.134+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:13.133+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:13.141+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:39:13.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:13.280+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:13.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:13.291+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T12:39:13.314+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.276 seconds
[2025-07-18T12:39:43.865+0000] {processor.py:186} INFO - Started process (PID=707) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:39:43.866+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T12:39:43.868+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:43.868+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:39:43.936+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:43.935+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:43.941+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:39:44.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:44.034+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:44.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:44.044+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T12:39:44.063+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.202 seconds
[2025-07-18T12:40:14.932+0000] {processor.py:186} INFO - Started process (PID=844) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:40:14.932+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T12:40:14.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:14.934+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:40:15.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:15.004+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:15.012+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:40:15.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:15.108+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:15.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:15.119+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T12:40:15.138+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.212 seconds
[2025-07-18T12:40:45.487+0000] {processor.py:186} INFO - Started process (PID=985) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:40:45.488+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T12:40:45.490+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:45.490+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:40:45.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:45.555+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:45.563+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:40:45.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:45.649+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:45.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:45.659+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T12:40:45.676+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.194 seconds
[2025-07-18T12:41:16.101+0000] {processor.py:186} INFO - Started process (PID=1121) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:41:16.102+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T12:41:16.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:16.104+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:41:16.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:16.189+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:41:16.198+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T12:41:16.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:16.312+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:41:16.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:16.324+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T12:41:16.343+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.249 seconds
