[2025-07-18T10:16:50.903+0000] {processor.py:186} INFO - Started process (PID=288) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:16:50.905+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:16:50.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:16:50.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.922+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:16:50.925+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:16:50.948+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.053 seconds
[2025-07-18T10:17:21.018+0000] {processor.py:186} INFO - Started process (PID=417) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:17:21.019+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:17:21.021+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:21.021+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:17:21.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:21.033+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:17:21.034+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:17:21.054+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-18T10:17:52.092+0000] {processor.py:186} INFO - Started process (PID=548) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:17:52.093+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:17:52.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:52.094+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:17:52.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:52.106+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:17:52.108+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:17:52.125+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T10:18:22.953+0000] {processor.py:186} INFO - Started process (PID=679) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:18:22.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:18:22.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:22.954+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:18:22.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:22.965+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:18:22.966+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:18:22.985+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.038 seconds
[2025-07-18T10:18:53.921+0000] {processor.py:186} INFO - Started process (PID=810) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:18:53.922+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:18:53.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:53.923+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:18:53.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:53.933+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:18:53.935+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:18:53.951+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.036 seconds
[2025-07-18T10:19:24.856+0000] {processor.py:186} INFO - Started process (PID=941) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:19:24.857+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:19:24.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:24.858+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:19:24.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:24.868+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:19:24.870+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:19:24.891+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T10:19:55.862+0000] {processor.py:186} INFO - Started process (PID=1072) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:19:55.863+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:19:55.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:55.864+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:19:55.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:55.875+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:19:55.877+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:19:55.896+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T10:20:26.859+0000] {processor.py:186} INFO - Started process (PID=1203) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:20:26.860+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:20:26.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:26.861+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:20:26.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:26.872+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:20:26.873+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:20:26.890+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.037 seconds
[2025-07-18T10:20:57.803+0000] {processor.py:186} INFO - Started process (PID=1334) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:20:57.804+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:20:57.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.804+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:20:57.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.818+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:20:57.820+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:20:57.839+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T10:21:28.817+0000] {processor.py:186} INFO - Started process (PID=1465) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:21:28.818+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:21:28.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.819+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:21:28.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.833+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:21:28.835+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:21:28.855+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T10:21:58.978+0000] {processor.py:186} INFO - Started process (PID=1596) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:21:58.978+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:21:58.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:58.979+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:21:58.993+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:58.991+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:21:58.993+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:21:59.011+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-18T10:22:29.995+0000] {processor.py:186} INFO - Started process (PID=1727) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:22:29.997+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:22:29.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:29.998+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:22:30.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:30.009+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:22:30.011+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:22:30.033+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.044 seconds
[2025-07-18T10:23:00.965+0000] {processor.py:186} INFO - Started process (PID=1858) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:23:00.966+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:23:00.967+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:00.967+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:23:00.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:00.977+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:23:00.979+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:23:01.000+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T10:23:31.815+0000] {processor.py:186} INFO - Started process (PID=1989) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:23:31.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:23:31.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.817+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:23:31.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.826+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:23:31.828+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:23:31.844+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.034 seconds
[2025-07-18T10:24:02.103+0000] {processor.py:186} INFO - Started process (PID=2122) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:24:02.104+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:24:02.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:02.104+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:24:02.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:02.116+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:24:02.118+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:24:02.135+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T10:24:33.077+0000] {processor.py:186} INFO - Started process (PID=2253) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:24:33.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:24:33.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:33.078+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:24:33.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:33.089+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:24:33.091+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:24:33.108+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.038 seconds
[2025-07-18T10:25:06.732+0000] {processor.py:186} INFO - Started process (PID=2384) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:25:06.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:25:06.735+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.734+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:25:06.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.747+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:25:06.748+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:25:06.766+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-18T10:26:25.289+0000] {processor.py:186} INFO - Started process (PID=288) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:26:25.290+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:26:25.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.292+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:26:25.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.306+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:26:25.308+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:26:25.326+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-18T10:26:57.109+0000] {processor.py:186} INFO - Started process (PID=417) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:26:57.110+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:26:57.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.112+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:26:57.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.123+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:26:57.124+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:26:57.142+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.038 seconds
[2025-07-18T10:27:28.370+0000] {processor.py:186} INFO - Started process (PID=548) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:27:28.371+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:27:28.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.373+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:27:28.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.389+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:27:28.390+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:27:28.410+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.047 seconds
[2025-07-18T10:27:59.669+0000] {processor.py:186} INFO - Started process (PID=681) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:27:59.672+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:27:59.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.674+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:27:59.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.686+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:27:59.688+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:27:59.706+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-18T10:28:30.097+0000] {processor.py:186} INFO - Started process (PID=810) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:28:30.098+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:28:30.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.100+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:28:30.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.112+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:28:30.114+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:28:30.133+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T10:29:00.937+0000] {processor.py:186} INFO - Started process (PID=943) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:29:00.938+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:29:00.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.940+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:29:00.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.955+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:29:00.957+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:29:00.976+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T10:29:31.534+0000] {processor.py:186} INFO - Started process (PID=1074) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:29:31.535+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:29:31.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.537+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:29:31.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.547+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:29:31.549+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:29:31.567+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T10:30:02.293+0000] {processor.py:186} INFO - Started process (PID=1205) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:30:02.294+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:30:02.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.296+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:30:02.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.309+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:30:02.311+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:30:02.330+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-18T10:30:32.779+0000] {processor.py:186} INFO - Started process (PID=1334) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:30:32.780+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:30:32.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.781+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:30:32.792+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.791+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:30:32.793+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:30:32.809+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.037 seconds
[2025-07-18T10:31:03.377+0000] {processor.py:186} INFO - Started process (PID=1465) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:31:03.378+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:31:03.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.381+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:31:03.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.390+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:31:03.392+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:31:03.410+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T10:31:33.799+0000] {processor.py:186} INFO - Started process (PID=1591) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:31:33.800+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:31:33.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.802+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:31:33.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:33.816+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:31:33.818+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:31:33.837+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.044 seconds
[2025-07-18T10:32:04.622+0000] {processor.py:186} INFO - Started process (PID=1722) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:32:04.624+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:32:04.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.627+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:32:04.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.644+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:32:04.646+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:32:04.672+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.057 seconds
[2025-07-18T10:32:35.268+0000] {processor.py:186} INFO - Started process (PID=1855) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:32:35.269+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:32:35.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.271+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:32:35.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.284+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:32:35.286+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:32:35.304+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-18T10:33:05.723+0000] {processor.py:186} INFO - Started process (PID=1986) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:33:05.724+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:33:05.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.726+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:33:05.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.738+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:33:05.740+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:33:05.758+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-18T10:33:36.357+0000] {processor.py:186} INFO - Started process (PID=2117) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:33:36.358+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:33:36.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.360+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:33:36.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.371+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:33:36.372+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:33:36.393+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T10:34:07.016+0000] {processor.py:186} INFO - Started process (PID=2248) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:34:07.017+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:34:07.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.019+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:34:07.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.031+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:34:07.033+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:34:07.050+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T10:34:38.025+0000] {processor.py:186} INFO - Started process (PID=2379) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:34:38.026+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:34:38.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.028+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:34:38.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.038+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:34:38.040+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:34:38.057+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.037 seconds
[2025-07-18T10:35:08.576+0000] {processor.py:186} INFO - Started process (PID=2510) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:35:08.576+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:35:08.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.578+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:35:08.590+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.589+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:35:08.591+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:35:08.608+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.037 seconds
[2025-07-18T10:35:39.345+0000] {processor.py:186} INFO - Started process (PID=2639) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:35:39.346+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:35:39.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.349+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:35:39.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.361+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:35:39.363+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:35:39.381+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.044 seconds
[2025-07-18T10:36:10.447+0000] {processor.py:186} INFO - Started process (PID=2775) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:36:10.448+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:36:10.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.450+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:36:10.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.465+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:36:10.467+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:36:10.488+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.048 seconds
[2025-07-18T10:36:41.037+0000] {processor.py:186} INFO - Started process (PID=2903) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:36:41.039+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:36:41.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:41.041+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:36:41.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:41.052+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:36:41.054+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:36:41.072+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-18T10:37:11.613+0000] {processor.py:186} INFO - Started process (PID=3034) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:37:11.615+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:37:11.617+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.617+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:37:11.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.632+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:37:11.634+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:37:11.655+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.048 seconds
[2025-07-18T10:37:43.121+0000] {processor.py:186} INFO - Started process (PID=3170) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:37:43.121+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:37:43.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.123+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:37:43.136+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.135+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:37:43.136+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:37:43.153+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T10:38:13.770+0000] {processor.py:186} INFO - Started process (PID=3301) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:38:13.772+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:38:13.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.774+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:38:13.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.788+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:38:13.789+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:38:13.809+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T10:38:44.124+0000] {processor.py:186} INFO - Started process (PID=3430) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:38:44.125+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:38:44.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.127+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:38:44.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.138+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:38:44.141+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:38:44.159+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T10:39:15.068+0000] {processor.py:186} INFO - Started process (PID=3561) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:39:15.068+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:39:15.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.071+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:39:15.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.084+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:39:15.086+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:39:15.107+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.046 seconds
[2025-07-18T10:39:45.284+0000] {processor.py:186} INFO - Started process (PID=3687) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:39:45.285+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:39:45.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.287+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:39:45.298+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.297+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:39:45.299+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:39:45.318+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T10:40:16.457+0000] {processor.py:186} INFO - Started process (PID=3820) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:40:16.458+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:40:16.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.462+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:40:16.482+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.480+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:40:16.483+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:40:16.505+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.057 seconds
[2025-07-18T10:40:47.411+0000] {processor.py:186} INFO - Started process (PID=3951) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:40:47.412+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:40:47.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.413+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:40:47.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.426+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:40:47.428+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:40:47.447+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T10:41:17.987+0000] {processor.py:186} INFO - Started process (PID=4085) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:41:17.988+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:41:17.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.990+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:41:18.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:18.004+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:41:18.006+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:41:18.028+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.047 seconds
[2025-07-18T10:41:48.751+0000] {processor.py:186} INFO - Started process (PID=4221) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:41:48.752+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:41:48.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:48.753+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:41:48.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:48.764+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:41:48.766+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:41:48.783+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.038 seconds
[2025-07-18T10:42:19.360+0000] {processor.py:186} INFO - Started process (PID=4352) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:42:19.362+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:42:19.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.364+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:42:19.380+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.379+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:42:19.381+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:42:19.402+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.049 seconds
[2025-07-18T10:43:01.478+0000] {processor.py:186} INFO - Started process (PID=291) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:43:01.479+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:43:01.481+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.480+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:43:01.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.493+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:43:01.495+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:43:01.514+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T10:43:33.923+0000] {processor.py:186} INFO - Started process (PID=427) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:43:33.925+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:43:33.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.927+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:43:33.939+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.938+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:43:33.940+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:43:33.958+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-18T10:44:04.860+0000] {processor.py:186} INFO - Started process (PID=560) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:44:04.861+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:44:04.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:04.862+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:44:04.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:04.873+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:44:04.875+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:44:05.001+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.147 seconds
[2025-07-18T10:44:35.415+0000] {processor.py:186} INFO - Started process (PID=696) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:44:35.416+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:44:35.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:35.418+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:44:35.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:35.432+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:44:35.435+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:44:35.455+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.047 seconds
[2025-07-18T10:45:06.454+0000] {processor.py:186} INFO - Started process (PID=832) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:45:06.455+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:45:06.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:06.457+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:45:06.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:06.470+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:45:06.471+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:45:06.493+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.047 seconds
[2025-07-18T10:45:36.948+0000] {processor.py:186} INFO - Started process (PID=968) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:45:36.949+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:45:36.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.951+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:45:36.964+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:36.963+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:45:36.965+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:45:36.984+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T10:46:07.647+0000] {processor.py:186} INFO - Started process (PID=1102) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:46:07.648+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:46:07.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.652+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:46:07.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:07.669+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:46:07.671+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:46:07.692+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.053 seconds
[2025-07-18T10:46:39.004+0000] {processor.py:186} INFO - Started process (PID=1240) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:46:39.005+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:46:39.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:39.007+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:46:39.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:39.019+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:46:39.021+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:46:39.040+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-18T10:48:07.007+0000] {processor.py:186} INFO - Started process (PID=291) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:48:07.008+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:48:07.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.010+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:48:07.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.024+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:48:07.025+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:48:07.044+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T10:48:38.069+0000] {processor.py:186} INFO - Started process (PID=427) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:48:38.071+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:48:38.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.073+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:48:38.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.084+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:48:38.086+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:48:38.109+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.047 seconds
[2025-07-18T10:49:09.034+0000] {processor.py:186} INFO - Started process (PID=558) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:49:09.035+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:49:09.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:09.038+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:49:09.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:09.053+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:49:09.055+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:49:09.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.194 seconds
[2025-07-18T10:49:39.925+0000] {processor.py:186} INFO - Started process (PID=694) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:49:39.926+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:49:39.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.928+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:49:39.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:39.939+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:49:39.941+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:49:39.960+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T10:50:10.621+0000] {processor.py:186} INFO - Started process (PID=827) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:50:10.622+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:50:10.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.624+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:50:10.638+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.637+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:50:10.639+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:50:10.657+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T10:50:41.416+0000] {processor.py:186} INFO - Started process (PID=963) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:50:41.417+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:50:41.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.419+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:50:41.432+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.431+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:50:41.432+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:50:41.451+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T10:51:12.123+0000] {processor.py:186} INFO - Started process (PID=1099) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:51:12.124+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:51:12.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.126+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:51:12.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.138+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:51:12.140+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:51:12.158+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T10:51:42.541+0000] {processor.py:186} INFO - Started process (PID=1236) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:51:42.542+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:51:42.544+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.544+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:51:42.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:42.556+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:51:42.557+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:51:42.575+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T10:52:13.006+0000] {processor.py:186} INFO - Started process (PID=1372) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:52:13.007+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:52:13.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:13.009+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:52:13.022+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:13.021+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:52:13.022+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:52:13.043+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-18T10:52:43.673+0000] {processor.py:186} INFO - Started process (PID=1508) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:52:43.674+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:52:43.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.675+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:52:43.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:43.687+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:52:43.689+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:52:43.708+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T10:53:14.211+0000] {processor.py:186} INFO - Started process (PID=1644) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:53:14.212+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:53:14.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:14.214+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:53:14.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:14.225+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:53:14.227+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:53:14.244+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-18T10:53:44.994+0000] {processor.py:186} INFO - Started process (PID=1780) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:53:44.995+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:53:44.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:44.996+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:53:45.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:45.005+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:53:45.007+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:53:45.024+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.036 seconds
[2025-07-18T10:54:16.253+0000] {processor.py:186} INFO - Started process (PID=1916) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:54:16.254+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:54:16.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:16.256+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:54:16.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:16.269+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:54:16.271+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:54:16.290+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-18T10:54:46.724+0000] {processor.py:186} INFO - Started process (PID=2052) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:54:46.726+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:54:46.729+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:46.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:54:46.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:46.749+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:54:46.751+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:54:46.774+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.058 seconds
[2025-07-18T10:55:17.378+0000] {processor.py:186} INFO - Started process (PID=2186) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:55:17.378+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:55:17.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:17.380+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:55:17.395+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:17.394+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:55:17.396+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:55:17.418+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.046 seconds
[2025-07-18T10:57:31.629+0000] {processor.py:186} INFO - Started process (PID=291) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:57:31.631+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:57:31.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.633+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:57:31.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.646+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:57:31.649+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:57:31.668+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T10:58:02.795+0000] {processor.py:186} INFO - Started process (PID=422) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:58:02.796+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:58:02.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.798+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:58:02.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.810+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:58:02.812+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:58:02.830+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T10:58:34.852+0000] {processor.py:186} INFO - Started process (PID=563) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:58:34.853+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:58:34.855+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.855+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:58:34.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.865+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:58:34.867+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:58:34.884+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.037 seconds
[2025-07-18T10:59:04.953+0000] {processor.py:186} INFO - Started process (PID=694) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:59:04.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:59:04.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.955+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:59:04.967+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:04.966+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:59:04.968+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:59:04.986+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T10:59:35.749+0000] {processor.py:186} INFO - Started process (PID=832) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:59:35.750+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T10:59:35.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.752+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:59:35.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.765+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T10:59:35.767+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T10:59:35.785+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T11:00:05.884+0000] {processor.py:186} INFO - Started process (PID=966) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:00:05.885+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:00:05.887+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.887+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:00:05.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:05.897+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:00:05.898+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:00:05.917+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.038 seconds
[2025-07-18T11:00:36.534+0000] {processor.py:186} INFO - Started process (PID=1097) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:00:36.535+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:00:36.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.537+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:00:36.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.548+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:00:36.550+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:00:36.571+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:01:07.025+0000] {processor.py:186} INFO - Started process (PID=1228) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:01:07.026+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:01:07.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.028+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:01:07.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.038+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:01:07.040+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:01:07.057+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.037 seconds
[2025-07-18T11:01:37.966+0000] {processor.py:186} INFO - Started process (PID=1361) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:01:37.967+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:01:37.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.968+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:01:37.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:37.979+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:01:37.981+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:01:37.997+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.036 seconds
[2025-07-18T11:02:10.059+0000] {processor.py:186} INFO - Started process (PID=1507) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:02:10.060+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:02:10.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.063+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:02:10.077+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.076+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:02:10.078+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:02:10.097+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.044 seconds
[2025-07-18T11:02:40.421+0000] {processor.py:186} INFO - Started process (PID=1641) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:02:40.423+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:02:40.425+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.425+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:02:40.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.436+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:02:40.438+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:02:40.457+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-18T11:03:11.032+0000] {processor.py:186} INFO - Started process (PID=1774) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:03:11.033+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:03:11.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.036+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:03:11.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.048+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:03:11.051+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:03:11.071+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.047 seconds
[2025-07-18T11:03:42.460+0000] {processor.py:186} INFO - Started process (PID=1915) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:03:42.461+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:03:42.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.462+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:03:42.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.473+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:03:42.475+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:03:42.492+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T11:04:13.830+0000] {processor.py:186} INFO - Started process (PID=2061) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:04:13.831+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:04:13.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.834+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:04:13.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.852+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:04:13.855+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:04:13.886+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.064 seconds
[2025-07-18T11:04:44.364+0000] {processor.py:186} INFO - Started process (PID=2197) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:04:44.365+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:04:44.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.367+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:04:44.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.378+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:04:44.380+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:04:44.399+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T11:05:15.211+0000] {processor.py:186} INFO - Started process (PID=2333) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:05:15.212+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:05:15.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.214+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:05:15.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.233+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:05:15.236+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:05:15.258+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.054 seconds
[2025-07-18T11:06:54.318+0000] {processor.py:186} INFO - Started process (PID=303) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:06:54.319+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:06:54.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.321+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:06:54.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.332+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:06:54.334+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:06:54.355+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-18T11:07:25.688+0000] {processor.py:186} INFO - Started process (PID=444) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:07:25.688+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:07:25.690+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.690+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:07:25.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.702+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:07:25.704+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:07:25.724+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:07:56.447+0000] {processor.py:186} INFO - Started process (PID=582) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:07:56.448+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:07:56.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.451+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:07:56.469+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.467+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:07:56.469+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:07:56.490+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.051 seconds
[2025-07-18T11:08:27.364+0000] {processor.py:186} INFO - Started process (PID=726) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:08:27.365+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:08:27.367+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.367+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:08:27.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.380+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:08:27.382+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:08:27.401+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-18T11:08:58.694+0000] {processor.py:186} INFO - Started process (PID=869) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:08:58.695+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:08:58.697+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.697+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:08:58.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.711+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:08:58.714+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:08:58.734+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.046 seconds
[2025-07-18T11:09:29.247+0000] {processor.py:186} INFO - Started process (PID=1008) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:09:29.248+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:09:29.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.250+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:09:29.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.261+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:09:29.263+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:09:29.282+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T11:10:00.670+0000] {processor.py:186} INFO - Started process (PID=1151) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:10:00.671+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:10:00.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.673+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:10:00.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.684+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:10:00.686+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:10:00.706+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:10:31.424+0000] {processor.py:186} INFO - Started process (PID=1292) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:10:31.425+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:10:31.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:31.427+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:10:31.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:31.444+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:10:31.446+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:10:31.471+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.054 seconds
[2025-07-18T11:11:01.921+0000] {processor.py:186} INFO - Started process (PID=1433) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:11:01.922+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:11:01.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.924+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:11:01.938+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.936+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:11:01.939+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:11:01.962+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.047 seconds
[2025-07-18T11:11:32.723+0000] {processor.py:186} INFO - Started process (PID=1574) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:11:32.724+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:11:32.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.726+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:11:32.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.740+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:11:32.742+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:11:32.761+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.044 seconds
[2025-07-18T11:12:03.514+0000] {processor.py:186} INFO - Started process (PID=1715) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:12:03.515+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:12:03.517+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.517+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:12:03.528+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.527+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:12:03.529+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:12:03.547+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T11:12:34.005+0000] {processor.py:186} INFO - Started process (PID=1856) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:12:34.007+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:12:34.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:34.009+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:12:34.023+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:34.021+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:12:34.023+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:12:34.043+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-18T11:13:05.752+0000] {processor.py:186} INFO - Started process (PID=1995) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:13:05.753+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:13:05.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.755+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:13:05.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.765+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:13:05.767+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:13:05.786+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-18T11:13:36.602+0000] {processor.py:186} INFO - Started process (PID=2138) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:13:36.603+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:13:36.605+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.604+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:13:36.616+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.615+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:13:36.617+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:13:36.635+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T11:14:08.032+0000] {processor.py:186} INFO - Started process (PID=2279) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:14:08.033+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:14:08.035+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:08.035+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:14:08.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:08.052+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:14:08.054+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:14:08.077+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.052 seconds
[2025-07-18T11:14:38.456+0000] {processor.py:186} INFO - Started process (PID=2420) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:14:38.457+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:14:38.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:38.459+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:14:38.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:38.470+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:14:38.471+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:14:38.490+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T11:15:08.686+0000] {processor.py:186} INFO - Started process (PID=2559) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:15:08.687+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:15:08.690+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.689+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:15:08.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.704+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:15:08.706+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:15:08.725+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T11:15:39.638+0000] {processor.py:186} INFO - Started process (PID=2697) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:15:39.639+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:15:39.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.641+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:15:39.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.652+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:15:39.654+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:15:39.673+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T11:16:10.228+0000] {processor.py:186} INFO - Started process (PID=2836) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:16:10.229+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:16:10.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.231+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:16:10.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.246+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:16:10.248+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:16:10.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T11:16:41.083+0000] {processor.py:186} INFO - Started process (PID=2979) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:16:41.084+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:16:41.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:41.086+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:16:41.098+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:41.097+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:16:41.099+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:16:41.118+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T11:17:12.775+0000] {processor.py:186} INFO - Started process (PID=3125) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:17:12.776+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:17:12.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.778+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:17:12.793+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.792+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:17:12.794+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:17:12.815+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.047 seconds
[2025-07-18T11:17:43.419+0000] {processor.py:186} INFO - Started process (PID=3266) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:17:43.420+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:17:43.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:17:43.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.436+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:17:43.437+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:17:43.457+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.044 seconds
[2025-07-18T11:18:14.319+0000] {processor.py:186} INFO - Started process (PID=3407) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:18:14.320+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:18:14.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.323+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:18:14.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.338+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:18:14.340+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:18:14.365+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.054 seconds
[2025-07-18T11:18:44.770+0000] {processor.py:186} INFO - Started process (PID=3548) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:18:44.771+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:18:44.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.773+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:18:44.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.788+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:18:44.790+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:18:44.809+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T11:19:16.476+0000] {processor.py:186} INFO - Started process (PID=3694) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:19:16.477+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:19:16.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:16.479+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:19:16.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:16.495+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:19:16.499+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:19:16.530+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.062 seconds
[2025-07-18T11:19:46.822+0000] {processor.py:186} INFO - Started process (PID=3833) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:19:46.823+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:19:46.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:46.825+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:19:46.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:46.836+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:19:46.838+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:19:46.857+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:20:17.812+0000] {processor.py:186} INFO - Started process (PID=3976) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:20:17.813+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:20:17.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.817+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:20:17.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.835+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:20:17.837+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:20:17.857+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.054 seconds
