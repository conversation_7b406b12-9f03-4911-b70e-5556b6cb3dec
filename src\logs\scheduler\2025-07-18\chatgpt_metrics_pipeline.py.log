[2025-07-18T10:16:49.941+0000] {processor.py:186} INFO - Started process (PID=228) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:16:49.942+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:16:49.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.944+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:16:49.992+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:49.989+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:49.993+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:16:50.010+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.075 seconds
[2025-07-18T10:17:20.210+0000] {processor.py:186} INFO - Started process (PID=359) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:17:20.211+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:17:20.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.213+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:17:20.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:20.242+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:20.246+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:17:20.263+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.058 seconds
[2025-07-18T10:17:50.594+0000] {processor.py:186} INFO - Started process (PID=488) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:17:50.595+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:17:50.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.596+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:17:50.630+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:50.627+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:50.631+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:17:50.845+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.258 seconds
[2025-07-18T10:18:21.250+0000] {processor.py:186} INFO - Started process (PID=619) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:18:21.250+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:18:21.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.252+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:18:21.286+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:21.282+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:21.287+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:18:21.304+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.060 seconds
[2025-07-18T10:18:52.227+0000] {processor.py:186} INFO - Started process (PID=752) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:18:52.228+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:18:52.229+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.229+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:18:52.261+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:52.258+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:52.262+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:18:52.278+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.057 seconds
[2025-07-18T10:19:23.198+0000] {processor.py:186} INFO - Started process (PID=883) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:19:23.199+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:19:23.200+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.200+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:19:23.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:23.233+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:23.238+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:19:23.254+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.063 seconds
[2025-07-18T10:19:54.137+0000] {processor.py:186} INFO - Started process (PID=1014) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:19:54.138+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:19:54.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.139+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:19:54.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:54.173+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:54.176+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:19:54.191+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.060 seconds
[2025-07-18T10:20:25.174+0000] {processor.py:186} INFO - Started process (PID=1145) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:20:25.175+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:20:25.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.175+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:20:25.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:25.207+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:25.211+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:20:25.226+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.059 seconds
[2025-07-18T10:20:56.126+0000] {processor.py:186} INFO - Started process (PID=1276) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:20:56.128+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:20:56.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.129+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:20:56.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:56.163+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:56.167+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:20:56.184+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.063 seconds
[2025-07-18T10:21:27.106+0000] {processor.py:186} INFO - Started process (PID=1407) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:21:27.107+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:21:27.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.108+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:21:27.143+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:27.140+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:27.144+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:21:27.159+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.059 seconds
[2025-07-18T10:21:57.269+0000] {processor.py:186} INFO - Started process (PID=1538) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:21:57.270+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:21:57.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.270+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:21:57.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:57.301+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:57.305+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:21:57.320+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.056 seconds
[2025-07-18T10:22:28.325+0000] {processor.py:186} INFO - Started process (PID=1669) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:22:28.326+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:22:28.328+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.328+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:22:28.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:28.364+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:28.369+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:22:28.386+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.067 seconds
[2025-07-18T10:22:59.293+0000] {processor.py:186} INFO - Started process (PID=1800) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:22:59.294+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:22:59.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.295+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:22:59.330+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:59.327+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:59.331+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:22:59.346+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.058 seconds
[2025-07-18T10:23:30.241+0000] {processor.py:186} INFO - Started process (PID=1931) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:23:30.242+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:23:30.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.243+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:23:30.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:30.276+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:30.280+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:23:30.297+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.063 seconds
[2025-07-18T10:24:01.099+0000] {processor.py:186} INFO - Started process (PID=2062) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:24:01.100+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:24:01.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.102+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:24:01.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:01.136+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:01.139+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:24:01.156+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.063 seconds
[2025-07-18T10:24:31.341+0000] {processor.py:186} INFO - Started process (PID=2193) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:24:31.342+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:24:31.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.343+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:24:31.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:31.375+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_metrics_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_metrics_pipeline.py", line 8, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:31.379+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:24:31.396+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.061 seconds
[2025-07-18T10:25:03.899+0000] {processor.py:186} INFO - Started process (PID=2324) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:25:03.899+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:25:03.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:03.900+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:25:04.098+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.098+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:04.109+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:25:04.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.208+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:04.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:04.217+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:25:04.234+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.341 seconds
[2025-07-18T10:26:21.559+0000] {processor.py:186} INFO - Started process (PID=228) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:26:21.560+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:26:21.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:21.562+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:26:21.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:21.902+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:21.910+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:26:21.999+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:21.998+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:22.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:22.008+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:26:22.027+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.474 seconds
[2025-07-18T10:26:53.867+0000] {processor.py:186} INFO - Started process (PID=357) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:26:53.868+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:26:53.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:53.870+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:26:54.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.288+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:54.294+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:26:54.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.392+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:54.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:54.405+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:26:54.425+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.567 seconds
[2025-07-18T10:27:25.093+0000] {processor.py:186} INFO - Started process (PID=488) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:27:25.094+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:27:25.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:25.097+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:27:25.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:25.439+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:25.447+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:27:25.550+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:25.549+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:25.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:25.562+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:27:25.584+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.499 seconds
[2025-07-18T10:27:56.269+0000] {processor.py:186} INFO - Started process (PID=621) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:27:56.270+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:27:56.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.272+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:27:56.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.457+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:56.466+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:27:56.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.563+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:56.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:56.573+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:27:56.594+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.330 seconds
[2025-07-18T10:28:27.314+0000] {processor.py:186} INFO - Started process (PID=752) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:28:27.315+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:28:27.317+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:27.317+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:28:27.519+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:27.519+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:27.528+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:28:27.627+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:27.626+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:27.638+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:27.638+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:28:27.660+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.351 seconds
[2025-07-18T10:28:57.743+0000] {processor.py:186} INFO - Started process (PID=883) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:28:57.744+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:28:57.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:57.746+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:28:57.949+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:57.949+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:57.959+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:28:58.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.064+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:58.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:58.073+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:28:58.094+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.356 seconds
[2025-07-18T10:29:28.416+0000] {processor.py:186} INFO - Started process (PID=1014) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:29:28.417+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:29:28.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:28.419+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:29:28.618+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:28.618+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:28.627+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:29:28.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:28.727+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:28.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:28.738+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:29:28.758+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.349 seconds
[2025-07-18T10:29:58.869+0000] {processor.py:186} INFO - Started process (PID=1145) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:29:58.870+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:29:58.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:58.871+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:29:59.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.067+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:59.076+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:29:59.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.168+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:59.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:59.179+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:29:59.201+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.337 seconds
[2025-07-18T10:30:29.521+0000] {processor.py:186} INFO - Started process (PID=1276) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:30:29.522+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:30:29.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:29.524+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:30:29.717+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:29.717+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:29.728+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:30:29.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:29.828+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:29.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:29.840+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:30:29.862+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.347 seconds
[2025-07-18T10:31:00.063+0000] {processor.py:186} INFO - Started process (PID=1407) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:31:00.064+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:31:00.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:00.066+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:31:00.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:00.272+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:00.282+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:31:00.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:00.380+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:00.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:00.391+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:31:00.412+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.355 seconds
[2025-07-18T10:31:30.592+0000] {processor.py:186} INFO - Started process (PID=1538) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:31:30.593+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:31:30.595+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:30.595+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:31:30.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:30.784+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:30.793+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:31:30.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:30.889+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:30.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:30.899+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:31:30.920+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.334 seconds
[2025-07-18T10:32:01.197+0000] {processor.py:186} INFO - Started process (PID=1669) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:32:01.197+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:32:01.200+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:01.199+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:32:01.403+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:01.403+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:01.411+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:32:01.521+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:01.521+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:01.535+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:01.535+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:32:01.557+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.366 seconds
[2025-07-18T10:32:31.668+0000] {processor.py:186} INFO - Started process (PID=1800) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:32:31.668+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:32:31.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:31.670+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:32:31.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:31.852+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:31.862+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:32:31.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:31.955+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:31.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:31.966+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:32:31.986+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.325 seconds
[2025-07-18T10:33:02.470+0000] {processor.py:186} INFO - Started process (PID=1931) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:33:02.471+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:33:02.473+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:02.473+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:33:02.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:02.680+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:02.690+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:33:02.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:02.784+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:02.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:02.796+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:33:02.816+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.351 seconds
[2025-07-18T10:33:33.122+0000] {processor.py:186} INFO - Started process (PID=2062) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:33:33.123+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:33:33.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:33.125+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:33:33.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:33.307+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:33.319+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:33:33.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:33.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:33.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:33.426+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:33:33.446+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.329 seconds
[2025-07-18T10:34:03.686+0000] {processor.py:186} INFO - Started process (PID=2193) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:34:03.687+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:34:03.689+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:03.689+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:34:03.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:03.934+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:03.944+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:34:04.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:04.038+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:04.049+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:04.049+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:34:04.067+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.388 seconds
[2025-07-18T10:34:34.455+0000] {processor.py:186} INFO - Started process (PID=2324) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:34:34.456+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:34:34.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:34.458+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:34:34.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:34.643+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:34.653+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:34:34.753+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:34.753+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:34.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:34.766+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:34:34.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.334 seconds
[2025-07-18T10:35:05.234+0000] {processor.py:186} INFO - Started process (PID=2455) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:35:05.235+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:35:05.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:05.237+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:35:05.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:05.415+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:05.424+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:35:05.519+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:05.519+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:05.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:05.530+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:35:05.550+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.321 seconds
[2025-07-18T10:35:36.023+0000] {processor.py:186} INFO - Started process (PID=2586) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:35:36.024+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:35:36.027+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:36.026+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:35:36.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:36.218+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:36.227+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:35:36.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:36.322+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:36.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:36.334+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:35:36.354+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.337 seconds
[2025-07-18T10:36:06.685+0000] {processor.py:186} INFO - Started process (PID=2717) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:36:06.686+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:36:06.690+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:06.689+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:36:06.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:06.897+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:06.905+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:36:07.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:07.000+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:07.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:07.011+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:36:07.030+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.352 seconds
[2025-07-18T10:36:37.669+0000] {processor.py:186} INFO - Started process (PID=2848) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:36:37.670+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:36:37.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:37.671+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:36:37.888+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:37.888+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:37.899+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:36:38.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:38.025+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:38.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:38.038+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:36:38.060+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.397 seconds
[2025-07-18T10:37:08.502+0000] {processor.py:186} INFO - Started process (PID=2979) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:37:08.503+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:37:08.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:08.505+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:37:08.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:08.719+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:08.728+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:37:08.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:08.828+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:08.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:08.840+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:37:08.861+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.365 seconds
[2025-07-18T10:37:39.412+0000] {processor.py:186} INFO - Started process (PID=3110) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:37:39.413+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:37:39.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:39.421+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:37:39.597+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:39.597+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:39.606+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:37:39.699+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:39.699+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:39.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:39.709+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:37:39.728+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.321 seconds
[2025-07-18T10:38:09.899+0000] {processor.py:186} INFO - Started process (PID=3241) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:38:09.900+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:38:09.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:09.901+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:38:10.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:10.087+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:10.094+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:38:10.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:10.194+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:10.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:10.205+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:38:10.223+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.330 seconds
[2025-07-18T10:38:40.494+0000] {processor.py:186} INFO - Started process (PID=3372) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:38:40.495+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:38:40.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:40.497+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:38:40.699+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:40.699+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:40.709+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:38:40.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:40.808+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:40.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:40.819+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:38:40.838+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.350 seconds
[2025-07-18T10:39:11.143+0000] {processor.py:186} INFO - Started process (PID=3503) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:39:11.144+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:39:11.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:11.146+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:39:11.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:11.337+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:11.347+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:39:11.436+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:11.436+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:11.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:11.445+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:39:11.463+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.327 seconds
[2025-07-18T10:39:41.861+0000] {processor.py:186} INFO - Started process (PID=3634) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:39:41.862+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:39:41.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:41.864+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:39:42.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:42.051+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:42.060+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:39:42.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:42.152+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:42.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:42.163+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:39:42.184+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.330 seconds
[2025-07-18T10:40:12.555+0000] {processor.py:186} INFO - Started process (PID=3765) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:40:12.556+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:40:12.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:12.558+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:40:12.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:12.751+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:12.760+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:40:12.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:12.850+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:12.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:12.859+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:40:12.879+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.330 seconds
[2025-07-18T10:40:43.118+0000] {processor.py:186} INFO - Started process (PID=3896) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:40:43.119+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:40:43.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:43.121+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:40:43.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:43.326+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:43.335+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:40:43.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:43.426+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:43.436+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:43.436+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:40:43.454+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.342 seconds
[2025-07-18T10:41:13.752+0000] {processor.py:186} INFO - Started process (PID=4027) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:41:13.753+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:41:13.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:13.755+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:41:13.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:13.973+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:13.984+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:41:14.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:14.101+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:14.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:14.112+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:41:14.141+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.396 seconds
[2025-07-18T10:41:44.429+0000] {processor.py:186} INFO - Started process (PID=4163) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:41:44.430+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:41:44.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:44.433+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:41:44.629+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:44.629+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:44.638+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:41:44.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:44.731+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:44.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:44.741+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:41:44.762+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.339 seconds
[2025-07-18T10:42:15.340+0000] {processor.py:186} INFO - Started process (PID=4297) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:42:15.341+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:42:15.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:15.343+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:42:15.544+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:15.544+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:15.553+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:42:15.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:15.774+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:15.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:15.783+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:42:15.805+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.470 seconds
[2025-07-18T10:42:58.508+0000] {processor.py:186} INFO - Started process (PID=231) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:42:58.509+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:42:58.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.511+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:42:58.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.852+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:58.859+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:42:58.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.946+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:58.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:58.956+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:42:58.977+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.475 seconds
[2025-07-18T10:43:30.442+0000] {processor.py:186} INFO - Started process (PID=369) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:43:30.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:43:30.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:30.447+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:43:30.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:30.779+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:30.787+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:43:30.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:30.871+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:30.881+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:30.881+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:43:30.898+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.462 seconds
[2025-07-18T10:44:00.966+0000] {processor.py:186} INFO - Started process (PID=505) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:44:00.967+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:44:00.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:00.969+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:44:01.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:01.276+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:01.285+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:44:01.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:01.369+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:01.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:01.378+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:44:01.395+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.434 seconds
[2025-07-18T10:44:32.073+0000] {processor.py:186} INFO - Started process (PID=641) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:44:32.073+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:44:32.075+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:32.075+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:44:32.261+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:32.261+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:32.270+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:44:32.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:32.368+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:32.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:32.379+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:44:32.398+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.331 seconds
[2025-07-18T10:45:02.507+0000] {processor.py:186} INFO - Started process (PID=777) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:45:02.509+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:45:02.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.511+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:45:02.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.700+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:02.710+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:45:02.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.815+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:02.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:02.826+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:45:02.847+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.345 seconds
[2025-07-18T10:45:33.109+0000] {processor.py:186} INFO - Started process (PID=913) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:45:33.110+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:45:33.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:33.111+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:45:33.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:33.323+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:33.336+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:45:33.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:33.441+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:33.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:33.452+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:45:33.472+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.369 seconds
[2025-07-18T10:46:04.048+0000] {processor.py:186} INFO - Started process (PID=1049) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:46:04.049+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:46:04.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:04.051+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:46:04.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:04.250+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:04.258+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:46:04.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:04.360+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:04.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:04.372+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:46:04.396+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.354 seconds
[2025-07-18T10:46:34.913+0000] {processor.py:186} INFO - Started process (PID=1185) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:46:34.915+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:46:34.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:34.917+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:46:35.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:35.138+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:35.147+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:46:35.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:35.252+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:35.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:35.263+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:46:35.283+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.377 seconds
[2025-07-18T10:47:05.478+0000] {processor.py:186} INFO - Started process (PID=1321) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:47:05.479+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:47:05.481+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.481+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:47:05.681+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.681+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:47:05.691+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:47:05.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.785+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:47:05.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:47:05.794+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:47:05.811+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.339 seconds
[2025-07-18T10:48:03.892+0000] {processor.py:186} INFO - Started process (PID=231) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:48:03.893+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:48:03.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:03.895+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:48:04.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.239+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:04.247+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:48:04.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.342+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:04.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:04.351+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:48:04.375+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.489 seconds
[2025-07-18T10:48:34.862+0000] {processor.py:186} INFO - Started process (PID=367) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:48:34.863+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:48:34.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:34.866+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:48:35.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.245+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:35.252+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:48:35.347+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.347+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:35.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:35.359+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:48:35.380+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.524 seconds
[2025-07-18T10:49:05.568+0000] {processor.py:186} INFO - Started process (PID=503) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:49:05.569+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:49:05.571+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.571+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:49:05.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:05.904+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:05.910+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:49:06.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:06.023+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:06.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:06.034+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:49:06.052+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.489 seconds
[2025-07-18T10:49:37.139+0000] {processor.py:186} INFO - Started process (PID=641) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:49:37.140+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:49:37.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.142+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:49:37.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.324+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:37.334+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:49:37.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.427+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:37.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:37.437+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:49:37.458+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.325 seconds
[2025-07-18T10:50:07.777+0000] {processor.py:186} INFO - Started process (PID=777) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:50:07.778+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:50:07.780+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:07.780+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:50:07.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:07.983+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:07.994+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:50:08.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.094+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:08.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:08.106+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:50:08.128+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.357 seconds
[2025-07-18T10:50:38.598+0000] {processor.py:186} INFO - Started process (PID=913) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:50:38.599+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:50:38.602+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:38.601+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:50:38.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:38.797+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:38.807+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:50:38.905+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:38.904+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:38.915+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:38.915+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:50:38.934+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.342 seconds
[2025-07-18T10:51:09.305+0000] {processor.py:186} INFO - Started process (PID=1049) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:51:09.306+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:51:09.309+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:09.308+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:51:09.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:09.506+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:09.514+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:51:09.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:09.613+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:09.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:09.623+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:51:09.642+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.343 seconds
[2025-07-18T10:51:39.846+0000] {processor.py:186} INFO - Started process (PID=1186) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:51:39.848+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:51:39.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:39.850+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:51:40.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.041+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:40.050+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:51:40.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.142+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:40.152+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:40.152+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:51:40.173+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.332 seconds
[2025-07-18T10:52:10.296+0000] {processor.py:186} INFO - Started process (PID=1322) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:52:10.297+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:52:10.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.299+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:52:10.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.485+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:10.494+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:52:10.594+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.593+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:10.605+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:10.604+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:52:10.624+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.333 seconds
[2025-07-18T10:52:40.896+0000] {processor.py:186} INFO - Started process (PID=1458) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:52:40.897+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:52:40.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:40.899+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:52:41.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:41.099+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:41.109+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:52:41.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:41.213+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:41.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:41.223+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:52:41.245+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.356 seconds
[2025-07-18T10:53:11.469+0000] {processor.py:186} INFO - Started process (PID=1594) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:53:11.470+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:53:11.473+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:11.472+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:53:11.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:11.668+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:11.678+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:53:11.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:11.772+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:11.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:11.781+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:53:11.799+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.336 seconds
[2025-07-18T10:53:42.503+0000] {processor.py:186} INFO - Started process (PID=1730) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:53:42.504+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:53:42.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:42.506+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:53:42.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:42.695+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:42.705+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:53:42.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:42.793+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:42.804+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:42.804+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:53:42.821+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.324 seconds
[2025-07-18T10:54:13.382+0000] {processor.py:186} INFO - Started process (PID=1866) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:54:13.382+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:54:13.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:13.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:54:13.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:13.596+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:13.604+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:54:13.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:13.709+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:13.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:13.720+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:54:13.743+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.367 seconds
[2025-07-18T10:54:44.007+0000] {processor.py:186} INFO - Started process (PID=2002) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:54:44.008+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:54:44.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.010+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:54:44.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.204+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:44.213+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:54:44.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.313+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:44.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:44.326+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:54:44.347+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.346 seconds
[2025-07-18T10:55:14.559+0000] {processor.py:186} INFO - Started process (PID=2138) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:55:14.560+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:55:14.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:14.562+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:55:14.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:14.744+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:14.753+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:55:14.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:14.847+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:14.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:14.858+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:55:14.878+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.325 seconds
[2025-07-18T10:55:45.404+0000] {processor.py:186} INFO - Started process (PID=2274) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:55:45.405+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:55:45.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:45.407+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:55:45.594+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:45.594+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:45.602+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:55:45.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:45.695+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:45.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:45.706+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:55:45.727+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.329 seconds
[2025-07-18T10:57:28.598+0000] {processor.py:186} INFO - Started process (PID=231) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:57:28.604+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:57:28.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.606+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:57:28.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:28.907+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:28.915+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:57:29.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.009+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:29.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:29.017+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:57:29.035+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.443 seconds
[2025-07-18T10:58:00.219+0000] {processor.py:186} INFO - Started process (PID=367) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:58:00.220+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:58:00.228+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.227+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:58:00.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.562+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:00.569+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:58:00.655+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.654+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:00.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:00.663+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:58:00.679+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.466 seconds
[2025-07-18T10:58:31.263+0000] {processor.py:186} INFO - Started process (PID=503) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:58:31.264+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:58:31.267+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.266+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:58:31.622+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.622+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:31.629+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:58:31.715+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.715+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:31.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:31.725+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:58:31.742+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.487 seconds
[2025-07-18T10:59:01.909+0000] {processor.py:186} INFO - Started process (PID=641) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:59:01.910+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:59:01.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:01.912+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:59:02.092+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.092+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:02.101+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:59:02.190+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.190+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:02.199+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:02.199+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:59:02.217+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.314 seconds
[2025-07-18T10:59:32.806+0000] {processor.py:186} INFO - Started process (PID=777) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:59:32.807+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T10:59:32.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:32.809+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:59:32.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:32.988+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:32.997+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T10:59:33.084+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.084+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:33.093+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:33.093+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T10:59:33.110+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.309 seconds
[2025-07-18T11:00:03.472+0000] {processor.py:186} INFO - Started process (PID=913) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:00:03.473+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:00:03.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.475+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:00:03.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.677+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:03.686+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:00:03.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.794+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:03.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:03.806+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:00:03.829+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.362 seconds
[2025-07-18T11:00:34.161+0000] {processor.py:186} INFO - Started process (PID=1049) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:00:34.161+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:00:34.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.163+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:00:34.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.376+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:34.386+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:00:34.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.485+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:34.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:34.495+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:00:34.515+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.360 seconds
[2025-07-18T11:01:04.746+0000] {processor.py:186} INFO - Started process (PID=1185) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:01:04.748+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:01:04.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:04.751+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:01:04.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:04.944+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:04.953+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:01:05.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.046+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:05.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:05.056+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:01:05.077+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.338 seconds
[2025-07-18T11:01:35.153+0000] {processor.py:186} INFO - Started process (PID=1321) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:01:35.154+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:01:35.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.156+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:01:35.336+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.336+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:35.345+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:01:35.431+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.431+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:35.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:35.440+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:01:35.456+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.308 seconds
[2025-07-18T11:02:06.461+0000] {processor.py:186} INFO - Started process (PID=1457) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:02:06.462+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:02:06.464+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:06.464+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:02:06.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:06.651+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:06.660+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:02:06.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:06.748+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:06.757+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:06.757+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:02:06.774+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.319 seconds
[2025-07-18T11:02:37.388+0000] {processor.py:186} INFO - Started process (PID=1593) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:02:37.389+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:02:37.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:37.390+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:02:37.594+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:37.594+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:37.605+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:02:37.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:37.707+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:37.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:37.720+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:02:37.744+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.362 seconds
[2025-07-18T11:03:08.553+0000] {processor.py:186} INFO - Started process (PID=1729) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:03:08.554+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:03:08.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:08.556+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:03:08.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:08.760+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:08.769+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:03:08.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:08.873+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:08.886+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:08.886+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:03:08.908+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.362 seconds
[2025-07-18T11:03:39.582+0000] {processor.py:186} INFO - Started process (PID=1865) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:03:39.585+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:03:39.588+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:39.587+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:03:39.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:39.823+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:39.835+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:03:39.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:39.947+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:39.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:39.959+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:03:39.981+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.406 seconds
[2025-07-18T11:04:10.373+0000] {processor.py:186} INFO - Started process (PID=2001) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:04:10.374+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:04:10.377+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:10.377+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:04:10.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:10.570+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:10.579+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:04:10.681+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:10.681+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:10.692+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:10.692+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:04:10.711+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.344 seconds
[2025-07-18T11:04:40.975+0000] {processor.py:186} INFO - Started process (PID=2137) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:04:40.976+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:04:40.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:40.978+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:04:41.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:41.193+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:41.204+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:04:41.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:41.325+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:41.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:41.341+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:04:41.363+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.395 seconds
[2025-07-18T11:05:11.669+0000] {processor.py:186} INFO - Started process (PID=2273) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:05:11.670+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:05:11.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:11.673+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:05:11.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:11.927+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:11.936+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:05:12.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:12.050+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:12.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:12.060+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:05:12.081+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.419 seconds
[2025-07-18T11:05:42.239+0000] {processor.py:186} INFO - Started process (PID=2408) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:05:42.241+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:05:42.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:42.245+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:05:42.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:42.740+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:42.771+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:05:43.283+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:43.283+0000] {process_utils.py:266} INFO - Waiting up to 5 seconds for processes to exit...
[2025-07-18T11:06:51.122+0000] {processor.py:186} INFO - Started process (PID=237) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:06:51.123+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:06:51.125+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.125+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:06:51.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.452+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:51.458+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:06:51.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.546+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:51.560+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:51.560+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:06:51.579+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.463 seconds
[2025-07-18T11:07:22.357+0000] {processor.py:186} INFO - Started process (PID=384) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:07:22.358+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:07:22.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.360+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:07:22.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.749+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:22.761+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:07:22.862+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.862+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:22.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:22.870+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:07:22.891+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.541 seconds
[2025-07-18T11:07:53.814+0000] {processor.py:186} INFO - Started process (PID=525) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:07:53.815+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:07:53.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:53.817+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:07:54.132+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.131+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:54.138+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:07:54.241+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.240+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:54.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:54.250+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:07:54.271+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.462 seconds
[2025-07-18T11:08:24.944+0000] {processor.py:186} INFO - Started process (PID=668) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:08:24.945+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:08:24.948+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:24.947+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:08:25.141+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.141+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:25.150+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:08:25.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.237+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:25.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:25.248+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:08:25.276+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.337 seconds
[2025-07-18T11:08:55.453+0000] {processor.py:186} INFO - Started process (PID=807) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:08:55.454+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:08:55.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.456+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:08:55.662+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.662+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:55.672+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:08:55.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.781+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:55.792+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:55.792+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:08:55.811+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.363 seconds
[2025-07-18T11:09:26.415+0000] {processor.py:186} INFO - Started process (PID=950) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:09:26.417+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:09:26.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:26.419+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:09:26.619+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:26.619+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:26.629+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:09:26.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:26.728+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:26.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:26.739+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:09:26.758+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.348 seconds
[2025-07-18T11:09:56.833+0000] {processor.py:186} INFO - Started process (PID=1089) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:09:56.834+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:09:56.836+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:56.836+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:09:57.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.032+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:57.042+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:09:57.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.162+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:57.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:57.177+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:09:57.200+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.373 seconds
[2025-07-18T11:10:27.724+0000] {processor.py:186} INFO - Started process (PID=1232) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:10:27.725+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:10:27.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:27.726+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:10:27.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:27.908+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:27.917+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:10:28.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.013+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:28.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:28.024+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:10:28.043+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.326 seconds
[2025-07-18T11:10:58.733+0000] {processor.py:186} INFO - Started process (PID=1371) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:10:58.734+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:10:58.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:58.736+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:10:58.954+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:58.954+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:58.965+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:10:59.080+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.079+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:59.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:59.091+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:10:59.110+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.382 seconds
[2025-07-18T11:11:29.337+0000] {processor.py:186} INFO - Started process (PID=1512) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:11:29.338+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:11:29.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:29.339+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:11:29.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:29.540+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:29.549+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:11:29.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:29.657+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:29.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:29.669+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:11:29.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.355 seconds
[2025-07-18T11:12:00.026+0000] {processor.py:186} INFO - Started process (PID=1653) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:12:00.027+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:12:00.029+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:00.029+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:12:00.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:00.215+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:00.224+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:12:00.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:00.325+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:00.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:00.336+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:12:00.357+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.338 seconds
[2025-07-18T11:12:30.985+0000] {processor.py:186} INFO - Started process (PID=1794) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:12:30.986+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:12:30.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:30.988+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:12:31.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.180+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:31.189+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:12:31.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.279+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:31.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:31.291+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:12:31.308+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.329 seconds
[2025-07-18T11:13:02.598+0000] {processor.py:186} INFO - Started process (PID=1935) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:13:02.599+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:13:02.602+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.602+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:13:02.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.824+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:02.835+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:13:02.954+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.954+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:02.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:02.971+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:13:02.996+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.405 seconds
[2025-07-18T11:13:33.199+0000] {processor.py:186} INFO - Started process (PID=2076) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:13:33.201+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:13:33.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.203+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:13:33.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.388+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:33.396+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:13:33.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.484+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:33.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:33.493+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:13:33.512+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.319 seconds
[2025-07-18T11:14:04.062+0000] {processor.py:186} INFO - Started process (PID=2217) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:14:04.062+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:14:04.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.065+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:14:04.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.291+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:04.301+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:14:04.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:04.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:04.429+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:14:04.456+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.401 seconds
[2025-07-18T11:14:34.634+0000] {processor.py:186} INFO - Started process (PID=2358) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:14:34.635+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:14:34.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.637+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:14:34.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.845+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:34.854+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:14:34.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.959+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:34.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:34.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:14:34.993+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.365 seconds
[2025-07-18T11:15:05.330+0000] {processor.py:186} INFO - Started process (PID=2499) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:15:05.331+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:15:05.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.333+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:15:05.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.548+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:05.559+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:15:05.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.674+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:05.686+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:05.686+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:15:05.708+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.386 seconds
[2025-07-18T11:15:36.604+0000] {processor.py:186} INFO - Started process (PID=2642) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:15:36.605+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:15:36.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.608+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:15:36.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.818+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:36.827+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:15:36.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.931+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:36.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:36.943+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:15:36.962+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.364 seconds
[2025-07-18T11:16:07.206+0000] {processor.py:186} INFO - Started process (PID=2783) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:16:07.207+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:16:07.209+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.209+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:16:07.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.423+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:07.432+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:16:07.542+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.541+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:07.554+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:07.554+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:16:07.573+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.374 seconds
[2025-07-18T11:16:37.892+0000] {processor.py:186} INFO - Started process (PID=2924) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:16:37.893+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:16:37.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:37.895+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:16:38.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:38.102+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:38.111+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:16:38.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:38.209+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:38.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:38.220+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:16:38.240+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.354 seconds
[2025-07-18T11:17:09.141+0000] {processor.py:186} INFO - Started process (PID=3065) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:17:09.143+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:17:09.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:09.145+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:17:09.382+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:09.381+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:09.391+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:17:09.521+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:09.521+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:09.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:09.533+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:17:09.557+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.422 seconds
[2025-07-18T11:17:40.040+0000] {processor.py:186} INFO - Started process (PID=3206) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:17:40.041+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:17:40.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:40.043+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:17:40.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:40.246+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:40.255+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:17:40.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:40.359+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:40.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:40.369+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:17:40.393+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.361 seconds
[2025-07-18T11:18:10.704+0000] {processor.py:186} INFO - Started process (PID=3347) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:18:10.706+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:18:10.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:10.708+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:18:10.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:10.942+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:10.953+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:18:11.077+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.076+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:11.092+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:11.092+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:18:11.112+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.414 seconds
[2025-07-18T11:18:41.572+0000] {processor.py:186} INFO - Started process (PID=3488) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:18:41.573+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:18:41.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.575+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:18:41.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.767+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:41.776+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:18:41.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.869+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:41.881+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:41.880+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:18:41.897+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.331 seconds
[2025-07-18T11:19:12.396+0000] {processor.py:186} INFO - Started process (PID=3629) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:19:12.397+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:19:12.401+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:12.400+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:19:12.646+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:12.645+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:12.657+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:19:12.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:12.773+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:12.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:12.786+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:19:12.811+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.422 seconds
[2025-07-18T11:19:42.880+0000] {processor.py:186} INFO - Started process (PID=3775) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:19:42.881+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:19:42.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:42.884+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:19:43.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:43.088+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:43.098+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:19:43.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:43.202+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:43.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:43.213+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:19:43.234+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.362 seconds
[2025-07-18T11:20:13.317+0000] {processor.py:186} INFO - Started process (PID=3916) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:20:13.318+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:20:13.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:13.320+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:20:13.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:13.528+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:13.537+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:20:13.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:13.648+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:13.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:13.659+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:20:13.681+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.371 seconds
[2025-07-18T12:38:08.627+0000] {processor.py:186} INFO - Started process (PID=232) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:38:08.629+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T12:38:08.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.631+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:38:08.702+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.702+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:08.710+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:38:08.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.801+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_metrics_pipeline
[2025-07-18T12:38:08.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.810+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_metrics_pipeline
[2025-07-18T12:38:08.967+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.966+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_metrics_pipeline
[2025-07-18T12:38:08.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.976+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_metrics_pipeline
[2025-07-18T12:38:08.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.983+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_metrics_pipeline
[2025-07-18T12:38:08.992+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:08.991+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_metrics_pipeline
[2025-07-18T12:38:09.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.000+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_metrics_pipeline
[2025-07-18T12:38:09.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.001+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:09.015+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T12:38:09.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.016+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_metrics_pipeline
[2025-07-18T12:38:09.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:09.017+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T12:38:09.038+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.416 seconds
[2025-07-18T12:38:39.336+0000] {processor.py:186} INFO - Started process (PID=368) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:38:39.337+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T12:38:39.339+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:39.338+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:38:39.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:39.415+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:39.429+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:38:39.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:39.672+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:39.686+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:39.686+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T12:38:39.704+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.374 seconds
[2025-07-18T12:39:09.796+0000] {processor.py:186} INFO - Started process (PID=504) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:39:09.796+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T12:39:09.799+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:09.798+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:39:09.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:09.987+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:09.992+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:39:10.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:10.099+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:10.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:10.107+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T12:39:10.128+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.338 seconds
[2025-07-18T12:39:40.256+0000] {processor.py:186} INFO - Started process (PID=642) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:39:40.257+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T12:39:40.259+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:40.259+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:39:40.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:40.325+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:40.333+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:39:40.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:40.416+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:40.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:40.426+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T12:39:40.459+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.209 seconds
[2025-07-18T12:40:10.748+0000] {processor.py:186} INFO - Started process (PID=777) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:40:10.749+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T12:40:10.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:10.751+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:40:10.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:10.821+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:10.828+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:40:10.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:10.914+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:10.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:10.923+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T12:40:10.942+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.199 seconds
[2025-07-18T12:40:41.716+0000] {processor.py:186} INFO - Started process (PID=913) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:40:41.717+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T12:40:41.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:41.719+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:40:41.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:41.790+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:41.798+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:40:41.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:41.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:41.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:41.922+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T12:40:41.945+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.234 seconds
[2025-07-18T12:41:12.271+0000] {processor.py:186} INFO - Started process (PID=1049) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:41:12.272+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T12:41:12.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:12.275+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:41:12.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:12.345+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:41:12.353+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T12:41:12.450+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:12.450+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:41:12.460+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:12.460+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T12:41:12.479+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.214 seconds
