[2025-07-18T10:16:50.785+0000] {processor.py:186} INFO - Started process (PID=283) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:16:50.786+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:16:50.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.789+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:16:50.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:50.834+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:50.839+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:16:50.860+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.084 seconds
[2025-07-18T10:17:21.009+0000] {processor.py:186} INFO - Started process (PID=414) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:17:21.009+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:17:21.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:21.011+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:17:21.048+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:21.044+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:21.049+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:17:21.068+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.065 seconds
[2025-07-18T10:17:52.084+0000] {processor.py:186} INFO - Started process (PID=545) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:17:52.085+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:17:52.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:52.086+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:17:52.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:52.118+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:52.123+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:17:52.141+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.063 seconds
[2025-07-18T10:18:22.944+0000] {processor.py:186} INFO - Started process (PID=676) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:18:22.945+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:18:22.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:22.946+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:18:22.981+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:22.978+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:22.982+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:18:22.999+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.061 seconds
[2025-07-18T10:18:53.917+0000] {processor.py:186} INFO - Started process (PID=807) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:18:53.918+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:18:53.919+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:53.919+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:18:53.950+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:53.947+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:53.951+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:18:53.965+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.054 seconds
[2025-07-18T10:19:24.848+0000] {processor.py:186} INFO - Started process (PID=938) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:19:24.849+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:19:24.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:24.850+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:19:24.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:24.880+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:24.886+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:19:24.902+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.059 seconds
[2025-07-18T10:19:55.853+0000] {processor.py:186} INFO - Started process (PID=1069) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:19:55.854+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:19:55.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:55.856+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:19:55.891+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:55.888+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:55.892+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:19:55.915+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.067 seconds
[2025-07-18T10:20:26.851+0000] {processor.py:186} INFO - Started process (PID=1200) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:20:26.852+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:20:26.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:26.853+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:20:26.886+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:26.883+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:26.887+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:20:26.903+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.058 seconds
[2025-07-18T10:20:57.795+0000] {processor.py:186} INFO - Started process (PID=1331) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:20:57.795+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:20:57.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.797+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:20:57.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.831+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:57.835+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:20:57.851+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.062 seconds
[2025-07-18T10:21:28.807+0000] {processor.py:186} INFO - Started process (PID=1462) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:21:28.808+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:21:28.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.809+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:21:28.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.840+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:28.845+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:21:28.861+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.059 seconds
[2025-07-18T10:21:58.969+0000] {processor.py:186} INFO - Started process (PID=1593) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:21:58.970+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:21:58.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:58.971+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:21:59.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:59.002+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:59.006+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:21:59.022+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.058 seconds
[2025-07-18T10:22:29.987+0000] {processor.py:186} INFO - Started process (PID=1724) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:22:29.988+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:22:29.989+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:29.989+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:22:30.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:30.021+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:30.027+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:22:30.045+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.064 seconds
[2025-07-18T10:23:00.959+0000] {processor.py:186} INFO - Started process (PID=1855) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:23:00.960+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:23:00.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:00.961+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:23:00.993+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:00.990+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:00.994+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:23:01.012+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.058 seconds
[2025-07-18T10:23:31.807+0000] {processor.py:186} INFO - Started process (PID=1986) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:23:31.807+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:23:31.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.808+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:23:31.839+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.837+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:31.840+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:23:31.855+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.054 seconds
[2025-07-18T10:24:02.006+0000] {processor.py:186} INFO - Started process (PID=2117) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:24:02.007+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:24:02.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:02.008+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:24:02.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:02.041+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:02.045+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:24:02.062+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.062 seconds
[2025-07-18T10:24:32.979+0000] {processor.py:186} INFO - Started process (PID=2248) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:24:32.980+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:24:32.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:32.981+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:24:33.022+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:33.020+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_cost
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:33.024+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:24:33.042+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.070 seconds
[2025-07-18T10:25:06.388+0000] {processor.py:186} INFO - Started process (PID=2377) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:25:06.389+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:25:06.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.390+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:25:06.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.575+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:06.584+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:25:06.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.674+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:06.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.687+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:25:06.706+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.323 seconds
[2025-07-18T10:26:24.784+0000] {processor.py:186} INFO - Started process (PID=283) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:26:24.785+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:26:24.787+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:24.787+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:26:25.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.117+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:25.125+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:26:25.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.215+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:25.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.225+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:26:25.247+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.468 seconds
[2025-07-18T10:26:56.881+0000] {processor.py:186} INFO - Started process (PID=412) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:26:56.882+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:26:56.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:56.883+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:26:57.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.214+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:57.220+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:26:57.309+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.308+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:57.318+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.318+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:26:57.337+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.462 seconds
[2025-07-18T10:27:28.021+0000] {processor.py:186} INFO - Started process (PID=543) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:27:28.023+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:27:28.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.025+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:27:28.256+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.256+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:28.265+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:27:28.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.365+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:28.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.376+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:27:28.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.377 seconds
[2025-07-18T10:27:59.240+0000] {processor.py:186} INFO - Started process (PID=674) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:27:59.241+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:27:59.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.244+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:27:59.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.471+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:59.480+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:27:59.601+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.600+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:59.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:59.613+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:27:59.642+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.408 seconds
[2025-07-18T10:28:30.087+0000] {processor.py:186} INFO - Started process (PID=807) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:28:30.088+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:28:30.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.090+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:28:30.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.329+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:30.338+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:28:30.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.440+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:30.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.453+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:28:30.471+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.390 seconds
[2025-07-18T10:29:00.556+0000] {processor.py:186} INFO - Started process (PID=936) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:29:00.558+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:29:00.560+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.560+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:29:00.758+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.758+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:00.767+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:29:00.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.877+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:00.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:00.888+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:29:00.911+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.361 seconds
[2025-07-18T10:29:31.192+0000] {processor.py:186} INFO - Started process (PID=1067) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:29:31.193+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:29:31.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.194+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:29:31.377+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.377+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:31.386+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:29:31.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.474+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:31.486+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:31.485+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:29:31.506+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.320 seconds
[2025-07-18T10:30:01.919+0000] {processor.py:186} INFO - Started process (PID=1198) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:30:01.919+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:30:01.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:01.921+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:30:02.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.103+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:02.112+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:30:02.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.216+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:02.228+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.228+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:30:02.249+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.336 seconds
[2025-07-18T10:30:32.437+0000] {processor.py:186} INFO - Started process (PID=1329) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:30:32.439+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:30:32.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.441+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:30:32.632+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.631+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:32.639+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:30:32.734+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.734+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:32.745+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.744+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:30:32.763+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.332 seconds
[2025-07-18T10:31:03.369+0000] {processor.py:186} INFO - Started process (PID=1462) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:31:03.370+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:31:03.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.372+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:31:03.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.586+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:03.595+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:31:03.758+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.757+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:03.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.775+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:31:03.800+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.436 seconds
[2025-07-18T10:31:34.064+0000] {processor.py:186} INFO - Started process (PID=1606) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:31:34.065+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:31:34.067+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.067+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:31:34.256+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.256+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:34.264+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:31:34.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:34.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.357+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:31:34.373+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.314 seconds
[2025-07-18T10:32:04.775+0000] {processor.py:186} INFO - Started process (PID=1737) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:32:04.777+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:32:04.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:04.779+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:32:05.033+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:05.033+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:05.041+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:32:05.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:05.156+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:05.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:05.170+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:32:05.195+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.425 seconds
[2025-07-18T10:32:35.420+0000] {processor.py:186} INFO - Started process (PID=1868) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:32:35.421+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:32:35.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:32:35.619+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.618+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:35.627+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:32:35.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.719+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:35.730+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.729+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:32:35.746+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.331 seconds
[2025-07-18T10:33:05.802+0000] {processor.py:186} INFO - Started process (PID=1991) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:33:05.803+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:33:05.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:05.805+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:33:06.021+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:06.021+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:06.028+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:33:06.132+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:06.131+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:06.141+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:06.141+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:33:06.158+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.361 seconds
[2025-07-18T10:33:36.522+0000] {processor.py:186} INFO - Started process (PID=2127) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:33:36.523+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:33:36.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.524+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:33:36.718+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.718+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:36.727+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:33:36.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.816+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:36.825+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:36.825+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:33:36.844+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.327 seconds
[2025-07-18T10:34:07.168+0000] {processor.py:186} INFO - Started process (PID=2258) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:34:07.169+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:34:07.171+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.171+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:34:07.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.378+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:07.387+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:34:07.480+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.480+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:07.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.489+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:34:07.509+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.346 seconds
[2025-07-18T10:34:38.105+0000] {processor.py:186} INFO - Started process (PID=2389) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:34:38.106+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:34:38.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.108+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:34:38.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.285+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:38.292+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:34:38.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.381+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:38.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.390+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:34:38.409+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.310 seconds
[2025-07-18T10:35:08.654+0000] {processor.py:186} INFO - Started process (PID=2520) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:35:08.655+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:35:08.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.657+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:35:08.839+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.839+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:08.848+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:35:08.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.944+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:08.954+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:08.954+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:35:08.974+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.325 seconds
[2025-07-18T10:35:39.440+0000] {processor.py:186} INFO - Started process (PID=2651) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:35:39.441+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:35:39.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.444+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:35:39.647+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.647+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:39.658+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:35:39.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.765+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:39.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.775+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:35:39.798+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.364 seconds
[2025-07-18T10:36:10.422+0000] {processor.py:186} INFO - Started process (PID=2770) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:36:10.423+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:36:10.425+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.425+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:36:10.666+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.666+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:10.673+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:36:10.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.782+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:10.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.795+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:36:10.814+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.399 seconds
[2025-07-18T10:36:41.119+0000] {processor.py:186} INFO - Started process (PID=2913) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:36:41.120+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:36:41.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:41.122+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:36:41.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:41.320+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:41.329+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:36:41.412+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:41.412+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:41.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:41.422+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:36:41.440+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.327 seconds
[2025-07-18T10:37:11.796+0000] {processor.py:186} INFO - Started process (PID=3044) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:37:11.797+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:37:11.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:11.800+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:37:12.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:12.025+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:12.036+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:37:12.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:12.138+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:12.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:12.149+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:37:12.169+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.379 seconds
[2025-07-18T10:37:42.737+0000] {processor.py:186} INFO - Started process (PID=3165) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:37:42.738+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:37:42.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:42.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:37:42.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:42.947+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:42.956+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:37:43.049+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.049+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:43.059+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.058+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:37:43.076+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.346 seconds
[2025-07-18T10:38:13.366+0000] {processor.py:186} INFO - Started process (PID=3296) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:38:13.367+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:38:13.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.370+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:38:13.577+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.577+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:13.586+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:38:13.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.693+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:13.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:13.705+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:38:13.726+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.367 seconds
[2025-07-18T10:38:44.117+0000] {processor.py:186} INFO - Started process (PID=3427) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:38:44.118+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:38:44.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.120+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:38:44.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.310+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:44.322+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:38:44.411+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.410+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:44.420+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.420+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:38:44.436+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.329 seconds
[2025-07-18T10:39:15.058+0000] {processor.py:186} INFO - Started process (PID=3558) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:39:15.059+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:39:15.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.061+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:39:15.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.300+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:15.309+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:39:15.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.427+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:15.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.440+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:39:15.462+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.410 seconds
[2025-07-18T10:39:45.646+0000] {processor.py:186} INFO - Started process (PID=3694) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:39:45.647+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:39:45.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.649+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:39:45.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.864+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:45.871+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:39:45.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.967+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:45.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:45.978+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:39:46.003+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.362 seconds
[2025-07-18T10:40:16.560+0000] {processor.py:186} INFO - Started process (PID=3825) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:40:16.561+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:40:16.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.564+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:40:16.780+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.780+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:16.792+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:40:16.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.936+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:16.949+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:16.949+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:40:16.971+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.418 seconds
[2025-07-18T10:40:47.494+0000] {processor.py:186} INFO - Started process (PID=3956) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:40:47.495+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:40:47.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.497+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:40:47.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.684+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:47.695+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:40:47.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.802+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:47.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:47.813+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:40:47.837+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.348 seconds
[2025-07-18T10:41:17.938+0000] {processor.py:186} INFO - Started process (PID=4080) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:41:17.939+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:41:17.942+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:17.941+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:41:18.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:18.173+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:18.184+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:41:18.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:18.303+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:18.315+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:18.314+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:41:18.335+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.404 seconds
[2025-07-18T10:41:48.744+0000] {processor.py:186} INFO - Started process (PID=4218) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:41:48.745+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:41:48.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:48.746+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:41:48.938+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:48.937+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:48.946+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:41:49.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:49.035+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:49.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:49.045+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:41:49.065+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.327 seconds
[2025-07-18T10:42:19.403+0000] {processor.py:186} INFO - Started process (PID=4357) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:42:19.404+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:42:19.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.406+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:42:19.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.621+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:19.630+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:42:19.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.748+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:19.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:19.773+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:42:19.971+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.575 seconds
[2025-07-18T10:43:01.328+0000] {processor.py:186} INFO - Started process (PID=286) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:43:01.329+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:43:01.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.332+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:43:01.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.659+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:01.666+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:43:01.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.760+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:01.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.769+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:43:01.788+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.467 seconds
[2025-07-18T10:43:33.915+0000] {processor.py:186} INFO - Started process (PID=424) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:43:33.916+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:43:33.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:33.917+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:43:34.255+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:34.255+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:34.264+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:43:34.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:34.379+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:34.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:34.389+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:43:34.403+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.494 seconds
[2025-07-18T10:44:05.162+0000] {processor.py:186} INFO - Started process (PID=565) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:44:05.163+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:44:05.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.165+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:44:05.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.345+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:05.351+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:44:05.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.445+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:05.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.454+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:44:05.472+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.315 seconds
[2025-07-18T10:44:36.439+0000] {processor.py:186} INFO - Started process (PID=701) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:44:36.440+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:44:36.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:36.441+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:44:36.630+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:36.630+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:36.638+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:44:36.729+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:36.728+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:36.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:36.738+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:44:36.756+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.322 seconds
[2025-07-18T10:45:07.505+0000] {processor.py:186} INFO - Started process (PID=837) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:45:07.506+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:45:07.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:07.507+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:45:07.717+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:07.717+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:07.727+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:45:07.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:07.846+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:07.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:07.859+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:45:07.879+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.380 seconds
[2025-07-18T10:45:37.979+0000] {processor.py:186} INFO - Started process (PID=973) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:45:37.980+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:45:37.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:37.982+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:45:38.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:38.180+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:38.191+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:45:38.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:38.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:38.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:38.310+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:45:38.334+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.361 seconds
[2025-07-18T10:46:08.723+0000] {processor.py:186} INFO - Started process (PID=1109) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:46:08.724+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:46:08.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:08.726+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:46:08.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:08.927+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:08.934+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:46:09.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:09.040+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:09.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:09.052+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:46:09.075+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.358 seconds
[2025-07-18T10:46:40.040+0000] {processor.py:186} INFO - Started process (PID=1245) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:46:40.041+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:46:40.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.043+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:46:40.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.278+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:40.289+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:46:40.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.408+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:40.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.423+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:46:40.448+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.416 seconds
[2025-07-18T10:48:06.685+0000] {processor.py:186} INFO - Started process (PID=286) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:48:06.686+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:48:06.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:06.688+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:48:07.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.031+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:07.037+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:48:07.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.129+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:07.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.138+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:48:07.157+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.478 seconds
[2025-07-18T10:48:37.896+0000] {processor.py:186} INFO - Started process (PID=422) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:48:37.897+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:48:37.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:37.899+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:48:38.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.280+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:38.285+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:48:38.380+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.380+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:38.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.389+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:48:38.409+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.519 seconds
[2025-07-18T10:49:09.666+0000] {processor.py:186} INFO - Started process (PID=570) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:49:09.667+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:49:09.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:09.670+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:49:09.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:09.893+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:09.902+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:49:10.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:10.000+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:10.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:10.010+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:49:10.030+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.372 seconds
[2025-07-18T10:49:40.090+0000] {processor.py:186} INFO - Started process (PID=704) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:49:40.091+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:49:40.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.093+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:49:40.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.319+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:40.326+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:49:40.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.421+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:40.432+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.432+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:49:40.455+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.371 seconds
[2025-07-18T10:50:10.796+0000] {processor.py:186} INFO - Started process (PID=840) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:50:10.797+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:50:10.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:10.799+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:50:11.048+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.047+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:11.055+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:50:11.154+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.154+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:11.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.165+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:50:11.185+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.397 seconds
[2025-07-18T10:50:41.585+0000] {processor.py:186} INFO - Started process (PID=976) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:50:41.585+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:50:41.588+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.588+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:50:41.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.801+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:41.810+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:50:41.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.906+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:41.917+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:41.917+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:50:41.938+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.360 seconds
[2025-07-18T10:51:12.284+0000] {processor.py:186} INFO - Started process (PID=1112) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:51:12.285+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:51:12.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.287+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:51:12.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.487+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:12.494+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:51:12.597+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.597+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:12.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:12.608+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:51:12.629+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.351 seconds
[2025-07-18T10:51:43.586+0000] {processor.py:186} INFO - Started process (PID=1249) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:51:43.587+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:51:43.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:43.589+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:51:43.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:43.784+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:43.793+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:51:43.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:43.912+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:43.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:43.925+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:51:43.947+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.366 seconds
[2025-07-18T10:52:14.053+0000] {processor.py:186} INFO - Started process (PID=1385) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:52:14.054+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:52:14.056+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:14.056+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:52:14.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:14.252+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:14.261+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:52:14.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:14.358+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:14.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:14.369+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:52:14.391+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.343 seconds
[2025-07-18T10:52:44.781+0000] {processor.py:186} INFO - Started process (PID=1521) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:52:44.782+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:52:44.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:44.784+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:52:44.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:44.975+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:44.983+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:52:45.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:45.088+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:45.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:45.099+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:52:45.120+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.344 seconds
[2025-07-18T10:53:15.326+0000] {processor.py:186} INFO - Started process (PID=1657) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:53:15.327+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:53:15.330+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:15.329+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:53:15.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:15.522+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:15.531+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:53:15.640+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:15.640+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:15.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:15.654+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:53:15.677+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.358 seconds
[2025-07-18T10:53:46.042+0000] {processor.py:186} INFO - Started process (PID=1793) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:53:46.042+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:53:46.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:46.044+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:53:46.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:46.234+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:46.243+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:53:46.339+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:46.338+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:46.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:46.349+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:53:46.369+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.333 seconds
[2025-07-18T10:54:17.303+0000] {processor.py:186} INFO - Started process (PID=1929) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:54:17.304+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:54:17.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:17.306+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:54:17.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:17.525+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:17.534+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:54:17.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:17.663+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:17.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:17.675+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:54:17.700+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.404 seconds
[2025-07-18T10:54:47.773+0000] {processor.py:186} INFO - Started process (PID=2065) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:54:47.774+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:54:47.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:47.777+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:54:47.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:47.982+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:47.991+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:54:48.098+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:48.097+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:48.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:48.109+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:54:48.127+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.362 seconds
[2025-07-18T10:55:18.595+0000] {processor.py:186} INFO - Started process (PID=2201) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:55:18.596+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:55:18.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:18.598+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:55:18.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:18.794+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:18.802+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:55:18.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:18.901+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:18.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:18.911+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:55:18.932+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.343 seconds
[2025-07-18T10:57:31.415+0000] {processor.py:186} INFO - Started process (PID=286) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:57:31.416+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:57:31.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.418+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:57:31.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.771+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:31.777+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:57:31.868+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.867+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:31.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:31.877+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:57:31.897+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.489 seconds
[2025-07-18T10:58:02.877+0000] {processor.py:186} INFO - Started process (PID=427) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:58:02.878+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:58:02.881+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:02.881+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:58:03.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.216+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:03.223+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:58:03.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.311+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:03.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.320+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:58:03.340+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.468 seconds
[2025-07-18T10:58:34.843+0000] {processor.py:186} INFO - Started process (PID=560) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:58:34.844+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:58:34.846+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:34.846+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:58:35.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:35.038+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:35.047+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:58:35.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:35.142+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:35.152+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:35.152+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:58:35.167+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.330 seconds
[2025-07-18T10:59:05.305+0000] {processor.py:186} INFO - Started process (PID=701) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:59:05.306+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:59:05.309+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.308+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:59:05.502+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.502+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:05.510+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:59:05.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.608+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:05.619+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.619+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:59:05.639+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.339 seconds
[2025-07-18T10:59:35.830+0000] {processor.py:186} INFO - Started process (PID=837) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:59:35.831+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T10:59:35.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:35.833+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:59:36.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:36.025+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:36.033+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T10:59:36.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:36.132+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:36.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:36.144+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T10:59:36.163+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.338 seconds
[2025-07-18T11:00:06.246+0000] {processor.py:186} INFO - Started process (PID=973) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:00:06.247+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:00:06.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.249+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:00:06.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.437+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:06.446+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:00:06.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.536+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:06.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.546+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:00:06.563+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.323 seconds
[2025-07-18T11:00:36.885+0000] {processor.py:186} INFO - Started process (PID=1107) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:00:36.886+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:00:36.888+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:36.888+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:00:37.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:37.095+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:37.104+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:00:37.197+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:37.196+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:37.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:37.206+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:00:37.225+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.346 seconds
[2025-07-18T11:01:07.502+0000] {processor.py:186} INFO - Started process (PID=1243) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:01:07.503+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:01:07.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.505+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:01:07.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.713+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:07.722+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:01:07.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.815+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:07.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:07.826+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:01:07.844+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.349 seconds
[2025-07-18T11:01:38.434+0000] {processor.py:186} INFO - Started process (PID=1379) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:01:38.435+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:01:38.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.438+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:01:38.638+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.638+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:38.647+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:01:38.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.753+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:38.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.766+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:01:38.789+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.360 seconds
[2025-07-18T11:02:10.156+0000] {processor.py:186} INFO - Started process (PID=1515) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:02:10.157+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:02:10.160+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.159+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:02:10.353+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.353+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:10.361+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:02:10.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.485+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:10.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.500+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:02:10.527+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.377 seconds
[2025-07-18T11:02:40.828+0000] {processor.py:186} INFO - Started process (PID=1651) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:02:40.829+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:02:40.831+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:40.830+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:02:41.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.051+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:41.059+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:02:41.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.153+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:41.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.163+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:02:41.184+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.363 seconds
[2025-07-18T11:03:11.509+0000] {processor.py:186} INFO - Started process (PID=1787) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:03:11.509+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:03:11.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.511+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:03:11.715+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.715+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:11.724+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:03:11.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.821+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:11.832+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:11.832+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:03:11.853+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.350 seconds
[2025-07-18T11:03:42.548+0000] {processor.py:186} INFO - Started process (PID=1923) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:03:42.550+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:03:42.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.552+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:03:42.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.762+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:42.772+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:03:42.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.880+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:42.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:42.894+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:03:42.916+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.373 seconds
[2025-07-18T11:04:13.380+0000] {processor.py:186} INFO - Started process (PID=2054) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:04:13.381+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:04:13.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.383+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:04:13.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.609+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:13.619+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:04:13.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.753+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:13.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:13.767+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:04:13.790+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.416 seconds
[2025-07-18T11:04:43.972+0000] {processor.py:186} INFO - Started process (PID=2190) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:04:43.973+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:04:43.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:43.974+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:04:44.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.191+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:44.200+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:04:44.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:44.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.310+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:04:44.331+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.365 seconds
[2025-07-18T11:05:14.769+0000] {processor.py:186} INFO - Started process (PID=2326) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:05:14.770+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:05:14.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:14.773+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:05:15.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.011+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:15.021+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:05:15.136+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.136+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:15.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.149+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:05:15.175+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.414 seconds
[2025-07-18T11:06:54.189+0000] {processor.py:186} INFO - Started process (PID=298) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:06:54.190+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:06:54.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.192+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:06:54.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.545+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:54.553+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:06:54.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.648+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:54.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.657+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:06:54.673+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.491 seconds
[2025-07-18T11:07:25.642+0000] {processor.py:186} INFO - Started process (PID=439) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:07:25.643+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:07:25.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.645+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:07:25.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:25.973+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:25.980+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:07:26.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:26.073+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:26.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:26.082+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:07:26.101+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.465 seconds
[2025-07-18T11:07:56.493+0000] {processor.py:186} INFO - Started process (PID=585) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:07:56.494+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:07:56.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.498+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:07:56.711+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.711+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:56.718+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:07:56.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.812+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:56.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.823+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:07:56.845+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.360 seconds
[2025-07-18T11:08:27.317+0000] {processor.py:186} INFO - Started process (PID=721) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:08:27.318+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:08:27.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.321+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:08:27.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.524+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:27.532+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:08:27.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.634+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:27.647+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.646+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:08:27.667+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.355 seconds
[2025-07-18T11:08:58.258+0000] {processor.py:186} INFO - Started process (PID=864) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:08:58.259+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:08:58.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.261+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:08:58.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.482+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:58.493+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:08:58.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.592+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:58.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.608+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:08:58.647+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.395 seconds
[2025-07-18T11:09:29.220+0000] {processor.py:186} INFO - Started process (PID=1003) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:09:29.222+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:09:29.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.224+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:09:29.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.433+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:29.442+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:09:29.544+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.544+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:29.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.555+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:09:29.588+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.374 seconds
[2025-07-18T11:10:00.276+0000] {processor.py:186} INFO - Started process (PID=1146) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:10:00.277+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:10:00.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.279+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:10:00.482+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.482+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:00.493+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:10:00.597+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.596+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:00.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.608+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:10:00.629+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.359 seconds
[2025-07-18T11:10:30.955+0000] {processor.py:186} INFO - Started process (PID=1287) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:10:30.957+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:10:30.964+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:30.963+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:10:31.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:31.207+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:31.215+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:10:31.330+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:31.329+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:31.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:31.347+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:10:31.375+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.427 seconds
[2025-07-18T11:11:01.531+0000] {processor.py:186} INFO - Started process (PID=1428) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:11:01.532+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:11:01.535+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.534+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:11:01.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.743+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:01.754+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:11:01.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.860+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:01.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:01.871+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:11:01.885+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.360 seconds
[2025-07-18T11:11:32.337+0000] {processor.py:186} INFO - Started process (PID=1567) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:11:32.339+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:11:32.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.342+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:11:32.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.552+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:32.560+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:11:32.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.657+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:32.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.668+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:11:32.685+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.356 seconds
[2025-07-18T11:12:03.123+0000] {processor.py:186} INFO - Started process (PID=1708) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:12:03.124+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:12:03.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.127+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:12:03.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.336+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:03.346+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:12:03.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.445+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:03.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.458+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:12:03.477+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.362 seconds
[2025-07-18T11:12:33.639+0000] {processor.py:186} INFO - Started process (PID=1849) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:12:33.640+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:12:33.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.643+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:12:33.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.837+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:33.845+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:12:33.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.943+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:33.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:33.956+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:12:33.976+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.345 seconds
[2025-07-18T11:13:05.743+0000] {processor.py:186} INFO - Started process (PID=1992) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:13:05.744+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:13:05.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.746+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:13:05.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:05.940+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:05.948+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:13:06.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:06.044+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:06.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:06.054+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:13:06.074+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.336 seconds
[2025-07-18T11:13:36.244+0000] {processor.py:186} INFO - Started process (PID=2131) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:13:36.245+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:13:36.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.247+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:13:36.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.430+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:36.439+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:13:36.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.532+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:36.544+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.543+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:13:36.563+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.324 seconds
[2025-07-18T11:14:07.551+0000] {processor.py:186} INFO - Started process (PID=2274) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:14:07.553+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:14:07.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:07.554+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:14:07.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:07.819+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:07.828+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:14:07.949+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:07.949+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:07.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:07.961+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:14:07.983+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.438 seconds
[2025-07-18T11:14:38.044+0000] {processor.py:186} INFO - Started process (PID=2415) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:14:38.045+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:14:38.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:38.047+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:14:38.259+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:38.259+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:38.269+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:14:38.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:38.369+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:38.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:38.381+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:14:38.402+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.364 seconds
[2025-07-18T11:15:08.663+0000] {processor.py:186} INFO - Started process (PID=2556) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:15:08.664+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:15:08.666+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.666+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:15:08.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:08.908+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:08.919+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:15:09.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:09.052+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:09.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:09.062+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:15:09.080+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.424 seconds
[2025-07-18T11:15:39.723+0000] {processor.py:186} INFO - Started process (PID=2702) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:15:39.725+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:15:39.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.727+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:15:39.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:39.946+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:39.956+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:15:40.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:40.113+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:40.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:40.128+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:15:40.152+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.434 seconds
[2025-07-18T11:16:10.318+0000] {processor.py:186} INFO - Started process (PID=2841) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:16:10.319+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:16:10.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.321+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:16:10.514+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.513+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:10.522+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:16:10.625+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.624+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:10.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:10.634+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:16:10.651+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.340 seconds
[2025-07-18T11:16:41.173+0000] {processor.py:186} INFO - Started process (PID=2989) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:16:41.174+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:16:41.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:41.177+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:16:41.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:41.389+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:41.400+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:16:41.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:41.503+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:41.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:41.515+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:16:41.536+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.371 seconds
[2025-07-18T11:17:12.319+0000] {processor.py:186} INFO - Started process (PID=3118) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:17:12.320+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:17:12.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.322+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:17:12.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.560+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:12.572+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:17:12.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.690+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:12.704+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.704+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:17:12.728+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.416 seconds
[2025-07-18T11:17:43.004+0000] {processor.py:186} INFO - Started process (PID=3259) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:17:43.005+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:17:43.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.007+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:17:43.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.227+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:43.236+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:17:43.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.340+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:43.353+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.352+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:17:43.373+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.376 seconds
[2025-07-18T11:18:13.847+0000] {processor.py:186} INFO - Started process (PID=3400) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:18:13.848+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:18:13.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:13.851+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:18:14.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.063+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:14.074+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:18:14.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.231+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:14.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.249+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:18:14.272+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.430 seconds
[2025-07-18T11:18:44.354+0000] {processor.py:186} INFO - Started process (PID=3541) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:18:44.355+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:18:44.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.359+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:18:44.569+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.569+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:44.578+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:18:44.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.684+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:44.698+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:44.698+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:18:44.719+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.371 seconds
[2025-07-18T11:19:16.009+0000] {processor.py:186} INFO - Started process (PID=3689) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:19:16.011+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:19:16.013+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:16.013+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:19:16.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:16.248+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:16.258+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:19:16.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:16.383+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:16.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:16.395+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:19:16.420+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.418 seconds
[2025-07-18T11:19:46.812+0000] {processor.py:186} INFO - Started process (PID=3830) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:19:46.813+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:19:46.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:46.816+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:19:47.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:47.026+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:47.035+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:19:47.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:47.132+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:47.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:47.145+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:19:47.187+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.381 seconds
[2025-07-18T11:20:17.389+0000] {processor.py:186} INFO - Started process (PID=3969) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:20:17.390+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:20:17.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.392+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:20:17.610+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.609+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:17.619+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:20:17.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.731+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:17.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:17.745+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:20:17.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.388 seconds
