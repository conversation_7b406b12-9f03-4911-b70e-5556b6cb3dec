[2025-07-18T10:16:51.022+0000] {processor.py:186} INFO - Started process (PID=301) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:16:51.023+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:16:51.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:51.025+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:16:51.069+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:16:51.066+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:16:51.071+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:16:51.091+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.077 seconds
[2025-07-18T10:17:21.289+0000] {processor.py:186} INFO - Started process (PID=434) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:17:21.290+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:17:21.292+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:21.292+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:17:21.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:21.323+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:21.328+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:17:21.347+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.063 seconds
[2025-07-18T10:17:52.253+0000] {processor.py:186} INFO - Started process (PID=565) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:17:52.254+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:17:52.255+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:52.255+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:17:52.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:17:52.296+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:17:52.301+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:17:52.316+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.069 seconds
[2025-07-18T10:18:23.112+0000] {processor.py:186} INFO - Started process (PID=696) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:18:23.113+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:18:23.115+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:23.115+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:18:23.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:23.145+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:23.149+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:18:23.164+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.057 seconds
[2025-07-18T10:18:54.064+0000] {processor.py:186} INFO - Started process (PID=827) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:18:54.065+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:18:54.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:54.066+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:18:54.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:18:54.097+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:18:54.101+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:18:54.116+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.057 seconds
[2025-07-18T10:19:25.017+0000] {processor.py:186} INFO - Started process (PID=956) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:19:25.018+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:19:25.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:25.019+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:19:25.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:25.051+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:25.055+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:19:25.071+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.060 seconds
[2025-07-18T10:19:56.039+0000] {processor.py:186} INFO - Started process (PID=1087) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:19:56.040+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:19:56.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:56.041+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:19:56.075+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:19:56.073+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:19:56.076+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:19:56.091+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.058 seconds
[2025-07-18T10:20:27.027+0000] {processor.py:186} INFO - Started process (PID=1218) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:20:27.028+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:20:27.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:27.029+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:20:27.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:27.063+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:27.067+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:20:27.083+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.062 seconds
[2025-07-18T10:20:57.973+0000] {processor.py:186} INFO - Started process (PID=1349) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:20:57.974+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:20:57.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:57.975+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:20:58.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:20:58.008+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:20:58.012+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:20:58.026+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.059 seconds
[2025-07-18T10:21:28.990+0000] {processor.py:186} INFO - Started process (PID=1480) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:21:28.991+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:21:28.992+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:28.992+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:21:29.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:29.023+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:29.026+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:21:29.041+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.057 seconds
[2025-07-18T10:21:59.213+0000] {processor.py:186} INFO - Started process (PID=1613) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:21:59.214+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:21:59.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:59.215+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:21:59.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:21:59.243+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:21:59.247+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:21:59.264+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.056 seconds
[2025-07-18T10:22:30.165+0000] {processor.py:186} INFO - Started process (PID=1742) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:22:30.166+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:22:30.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:30.167+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:22:30.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:22:30.199+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:22:30.203+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:22:30.218+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.059 seconds
[2025-07-18T10:23:01.138+0000] {processor.py:186} INFO - Started process (PID=1873) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:23:01.139+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:23:01.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:01.140+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:23:01.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:01.172+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:01.178+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:23:01.194+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.062 seconds
[2025-07-18T10:23:31.971+0000] {processor.py:186} INFO - Started process (PID=2004) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:23:31.972+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:23:31.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:31.973+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:23:32.006+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:23:32.003+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:23:32.007+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:23:32.023+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.058 seconds
[2025-07-18T10:24:02.210+0000] {processor.py:186} INFO - Started process (PID=2135) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:24:02.211+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:24:02.212+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:02.212+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:24:02.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:02.244+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:02.249+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:24:02.264+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.062 seconds
[2025-07-18T10:24:33.177+0000] {processor.py:186} INFO - Started process (PID=2266) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:24:33.178+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:24:33.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:33.179+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:24:33.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:24:33.210+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/perplexity_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/perplexity_pipeline.py", line 7, in <module>
    from cost_tracking import track_openai_request, OperationType
  File "/opt/airflow/dags/cost_tracking.py", line 32
    import csv
    ^^^^^^
IndentationError: expected an indented block after class definition on line 31
[2025-07-18T10:24:33.214+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:24:33.230+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.059 seconds
[2025-07-18T10:25:06.876+0000] {processor.py:186} INFO - Started process (PID=2397) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:25:06.877+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:25:06.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:06.878+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:25:07.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:07.065+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:25:07.074+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:25:07.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:07.169+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:25:07.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:25:07.178+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:25:07.197+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.326 seconds
[2025-07-18T10:26:25.444+0000] {processor.py:186} INFO - Started process (PID=301) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:26:25.445+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:26:25.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.447+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:26:25.776+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.776+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:25.784+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:26:25.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.876+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:25.886+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:25.885+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:26:25.902+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.465 seconds
[2025-07-18T10:26:57.381+0000] {processor.py:186} INFO - Started process (PID=432) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:26:57.383+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:26:57.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:26:57.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.728+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:26:57.737+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:26:57.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.850+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:26:57.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:26:57.859+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:26:57.878+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.502 seconds
[2025-07-18T10:27:28.510+0000] {processor.py:186} INFO - Started process (PID=563) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:27:28.511+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:27:28.514+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.513+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:27:28.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.725+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:27:28.734+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:27:28.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.844+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:27:28.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:27:28.855+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:27:28.876+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.373 seconds
[2025-07-18T10:28:00.130+0000] {processor.py:186} INFO - Started process (PID=696) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:28:00.131+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:28:00.134+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:00.133+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:28:00.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:00.345+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:00.355+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:28:00.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:00.456+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:00.467+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:00.467+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:28:00.489+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.365 seconds
[2025-07-18T10:28:30.669+0000] {processor.py:186} INFO - Started process (PID=832) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:28:30.670+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:28:30.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.672+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:28:30.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.864+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:28:30.874+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:28:30.969+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.969+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:28:30.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:28:30.980+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:28:31.002+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.339 seconds
[2025-07-18T10:29:01.577+0000] {processor.py:186} INFO - Started process (PID=963) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:29:01.578+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:29:01.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:01.580+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:29:01.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:01.788+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:01.798+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:29:01.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:01.902+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:01.913+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:01.913+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:29:01.937+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.367 seconds
[2025-07-18T10:29:32.086+0000] {processor.py:186} INFO - Started process (PID=1094) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:29:32.088+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:29:32.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:32.089+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:29:32.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:32.275+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:29:32.284+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:29:32.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:32.375+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:29:32.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:29:32.388+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:29:32.409+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.328 seconds
[2025-07-18T10:30:02.526+0000] {processor.py:186} INFO - Started process (PID=1223) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:30:02.528+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:30:02.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.530+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:30:02.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.731+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:02.741+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:30:02.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.844+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:02.855+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:02.854+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:30:02.873+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.354 seconds
[2025-07-18T10:30:32.974+0000] {processor.py:186} INFO - Started process (PID=1354) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:30:32.975+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:30:32.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:32.978+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:30:33.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:33.170+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:30:33.179+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:30:33.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:33.268+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:30:33.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:30:33.278+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:30:33.294+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.326 seconds
[2025-07-18T10:31:03.841+0000] {processor.py:186} INFO - Started process (PID=1485) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:31:03.842+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:31:03.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:03.844+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:31:04.049+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:04.049+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:04.058+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:31:04.173+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:04.173+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:04.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:04.184+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:31:04.203+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.368 seconds
[2025-07-18T10:31:34.432+0000] {processor.py:186} INFO - Started process (PID=1616) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:31:34.433+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:31:34.435+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.435+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:31:34.625+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.625+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:31:34.636+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:31:34.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.737+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:31:34.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:31:34.749+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:31:34.770+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.344 seconds
[2025-07-18T10:32:05.260+0000] {processor.py:186} INFO - Started process (PID=1747) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:32:05.263+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:32:05.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:05.267+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:32:05.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:05.522+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:05.532+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:32:05.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:05.653+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:05.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:05.668+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:32:05.691+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.441 seconds
[2025-07-18T10:32:35.797+0000] {processor.py:186} INFO - Started process (PID=1878) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:32:35.798+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:32:35.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.800+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:32:35.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:35.985+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:32:35.993+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:32:36.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:36.082+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:32:36.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:32:36.091+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:32:36.108+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.316 seconds
[2025-07-18T10:33:06.208+0000] {processor.py:186} INFO - Started process (PID=2009) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:33:06.209+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:33:06.211+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:06.211+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:33:06.411+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:06.411+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:06.421+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:33:06.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:06.525+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:06.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:06.535+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:33:06.555+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.353 seconds
[2025-07-18T10:33:37.017+0000] {processor.py:186} INFO - Started process (PID=2142) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:33:37.018+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:33:37.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:37.020+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:33:37.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:37.214+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:33:37.223+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:33:37.309+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:37.309+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:33:37.318+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:33:37.318+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:33:37.336+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.325 seconds
[2025-07-18T10:34:07.685+0000] {processor.py:186} INFO - Started process (PID=2273) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:34:07.686+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:34:07.689+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.688+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:34:07.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.895+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:07.904+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:34:07.999+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:07.998+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:08.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:08.009+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:34:08.029+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.350 seconds
[2025-07-18T10:34:38.457+0000] {processor.py:186} INFO - Started process (PID=2397) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:34:38.458+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:34:38.460+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.460+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:34:38.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.657+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:34:38.666+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:34:38.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.766+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:34:38.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:34:38.778+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:34:38.798+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.347 seconds
[2025-07-18T10:35:09.035+0000] {processor.py:186} INFO - Started process (PID=2528) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:35:09.036+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:35:09.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:09.039+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:35:09.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:09.254+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:09.263+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:35:09.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:09.359+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:09.371+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:09.371+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:35:09.390+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.361 seconds
[2025-07-18T10:35:39.865+0000] {processor.py:186} INFO - Started process (PID=2659) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:35:39.866+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:35:39.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:39.869+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:35:40.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:40.078+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:35:40.088+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:35:40.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:40.185+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:35:40.197+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:35:40.197+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:35:40.218+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.360 seconds
[2025-07-18T10:36:10.856+0000] {processor.py:186} INFO - Started process (PID=2790) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:36:10.857+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:36:10.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:10.859+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:36:11.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:11.072+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:11.081+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:36:11.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:11.181+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:11.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:11.191+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:36:11.210+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.360 seconds
[2025-07-18T10:36:41.883+0000] {processor.py:186} INFO - Started process (PID=2923) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:36:41.884+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:36:41.887+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:41.886+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:36:42.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:42.067+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:36:42.077+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:36:42.167+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:42.167+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:36:42.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:36:42.176+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:36:42.193+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.316 seconds
[2025-07-18T10:37:12.379+0000] {processor.py:186} INFO - Started process (PID=3057) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:37:12.380+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:37:12.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:12.382+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:37:12.610+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:12.609+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:12.633+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:37:12.757+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:12.756+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:12.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:12.767+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:37:12.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.411 seconds
[2025-07-18T10:37:43.566+0000] {processor.py:186} INFO - Started process (PID=3185) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:37:43.567+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:37:43.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.569+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:37:43.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.759+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:37:43.770+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:37:43.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.871+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:37:43.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:37:43.882+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:37:43.899+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.338 seconds
[2025-07-18T10:38:14.427+0000] {processor.py:186} INFO - Started process (PID=3321) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:38:14.428+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:38:14.431+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:14.430+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:38:14.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:14.623+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:14.631+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:38:14.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:14.726+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:14.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:14.737+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:38:14.761+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.340 seconds
[2025-07-18T10:38:44.846+0000] {processor.py:186} INFO - Started process (PID=3452) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:38:44.847+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:38:44.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:44.849+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:38:45.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:45.038+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:38:45.047+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:38:45.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:45.143+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:38:45.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:38:45.153+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:38:45.172+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.332 seconds
[2025-07-18T10:39:15.753+0000] {processor.py:186} INFO - Started process (PID=3583) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:39:15.754+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:39:15.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.756+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:39:15.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:15.962+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:15.971+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:39:16.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:16.068+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:16.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:16.078+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:39:16.096+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.351 seconds
[2025-07-18T10:39:46.177+0000] {processor.py:186} INFO - Started process (PID=3709) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:39:46.178+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:39:46.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:46.180+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:39:46.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:46.391+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:39:46.400+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:39:46.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:46.496+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:39:46.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:39:46.507+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:39:46.530+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.361 seconds
[2025-07-18T10:40:17.240+0000] {processor.py:186} INFO - Started process (PID=3845) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:40:17.242+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:40:17.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:17.244+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:40:17.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:17.446+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:17.455+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:40:17.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:17.554+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:17.565+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:17.565+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:40:17.717+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.482 seconds
[2025-07-18T10:40:48.089+0000] {processor.py:186} INFO - Started process (PID=3976) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:40:48.090+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:40:48.092+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:48.092+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:40:48.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:48.288+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:40:48.299+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:40:48.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:48.398+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:40:48.519+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:40:48.519+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:40:48.535+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.452 seconds
[2025-07-18T10:41:18.649+0000] {processor.py:186} INFO - Started process (PID=4107) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:41:18.650+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:41:18.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:18.653+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:41:18.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:18.893+0000] {cost_tracking.py:150} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:18.902+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:41:18.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:18.997+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:19.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:19.124+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:41:19.144+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.501 seconds
[2025-07-18T10:41:49.318+0000] {processor.py:186} INFO - Started process (PID=4243) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:41:49.319+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:41:49.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:49.321+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:41:49.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:49.497+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:41:49.507+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:41:49.603+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:49.602+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:41:49.715+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:41:49.715+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:41:49.731+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.419 seconds
[2025-07-18T10:42:20.096+0000] {processor.py:186} INFO - Started process (PID=4377) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:42:20.097+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:42:20.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:20.099+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:42:20.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:20.304+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:42:20.314+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:42:20.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:20.548+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:42:20.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:42:20.558+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:42:20.575+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.485 seconds
[2025-07-18T10:43:01.902+0000] {processor.py:186} INFO - Started process (PID=306) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:43:01.903+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:43:01.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:01.905+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:43:02.222+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:02.222+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:02.228+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:43:02.315+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:02.314+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:02.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:02.324+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:43:02.342+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.446 seconds
[2025-07-18T10:43:34.507+0000] {processor.py:186} INFO - Started process (PID=444) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:43:34.508+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:43:34.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:34.509+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:43:34.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:34.818+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:43:34.825+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:43:34.913+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:34.913+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:43:34.921+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:43:34.921+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:43:34.939+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.438 seconds
[2025-07-18T10:44:05.583+0000] {processor.py:186} INFO - Started process (PID=580) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:44:05.584+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:44:05.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.586+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:44:05.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.763+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:05.772+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:44:05.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.858+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:05.867+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:05.867+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:44:05.883+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.306 seconds
[2025-07-18T10:44:36.862+0000] {processor.py:186} INFO - Started process (PID=716) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:44:36.864+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:44:36.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:36.866+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:44:37.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:37.063+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:44:37.073+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:44:37.160+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:37.160+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:44:37.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:44:37.169+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:44:37.186+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.329 seconds
[2025-07-18T10:45:07.946+0000] {processor.py:186} INFO - Started process (PID=850) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:45:07.947+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:45:07.950+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:07.950+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:45:08.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:08.169+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:08.178+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:45:08.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:08.285+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:08.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:08.296+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:45:08.317+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.377 seconds
[2025-07-18T10:45:38.462+0000] {processor.py:186} INFO - Started process (PID=988) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:45:38.464+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:45:38.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:38.466+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:45:38.674+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:38.673+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:45:38.681+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:45:38.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:38.783+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:45:38.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:45:38.793+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:45:38.813+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.356 seconds
[2025-07-18T10:46:09.331+0000] {processor.py:186} INFO - Started process (PID=1129) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:46:09.332+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:46:09.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:09.334+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:46:09.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:09.532+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:09.543+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:46:09.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:09.640+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:09.652+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:09.652+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:46:09.671+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.346 seconds
[2025-07-18T10:46:40.577+0000] {processor.py:186} INFO - Started process (PID=1260) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:46:40.578+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:46:40.582+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.581+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:46:40.804+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.804+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:46:40.814+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:46:40.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.914+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:46:40.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:46:40.924+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:46:40.944+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.373 seconds
[2025-07-18T10:48:07.277+0000] {processor.py:186} INFO - Started process (PID=306) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:48:07.278+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:48:07.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.281+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:48:07.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.621+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:07.627+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:48:07.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.716+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:07.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:07.724+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:48:07.739+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.468 seconds
[2025-07-18T10:48:38.453+0000] {processor.py:186} INFO - Started process (PID=442) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:48:38.454+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:48:38.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.456+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:48:38.791+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.791+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:48:38.798+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:48:38.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.898+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:48:38.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:48:38.909+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:48:38.928+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.481 seconds
[2025-07-18T10:49:10.085+0000] {processor.py:186} INFO - Started process (PID=578) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:49:10.086+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:49:10.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:10.088+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:49:10.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:10.289+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:10.301+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:49:10.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:10.397+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:10.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:10.408+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:49:10.426+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.346 seconds
[2025-07-18T10:49:40.518+0000] {processor.py:186} INFO - Started process (PID=714) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:49:40.519+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:49:40.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.521+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:49:40.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.724+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:49:40.734+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:49:40.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.851+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:49:40.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:49:40.863+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:49:40.886+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.374 seconds
[2025-07-18T10:50:11.650+0000] {processor.py:186} INFO - Started process (PID=852) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:50:11.651+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:50:11.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.653+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:50:11.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.859+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:11.869+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:50:11.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.978+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:11.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:11.990+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:50:12.014+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.373 seconds
[2025-07-18T10:50:42.537+0000] {processor.py:186} INFO - Started process (PID=993) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:50:42.538+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:50:42.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:42.540+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:50:42.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:42.732+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:50:42.742+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:50:42.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:42.835+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:50:42.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:50:42.847+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:50:42.866+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.335 seconds
[2025-07-18T10:51:13.052+0000] {processor.py:186} INFO - Started process (PID=1129) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:51:13.053+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:51:13.056+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:13.055+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:51:13.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:13.237+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:13.247+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:51:13.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:13.342+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:13.353+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:13.353+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:51:13.369+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.323 seconds
[2025-07-18T10:51:44.346+0000] {processor.py:186} INFO - Started process (PID=1261) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:51:44.347+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:51:44.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:44.350+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:51:44.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:44.542+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:51:44.552+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:51:44.646+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:44.646+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:51:44.656+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:51:44.656+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:51:44.674+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.334 seconds
[2025-07-18T10:52:14.814+0000] {processor.py:186} INFO - Started process (PID=1397) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:52:14.815+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:52:14.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:14.817+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:52:15.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:15.016+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:15.025+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:52:15.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:15.119+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:15.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:15.130+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:52:15.152+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.343 seconds
[2025-07-18T10:52:45.549+0000] {processor.py:186} INFO - Started process (PID=1533) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:52:45.550+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:52:45.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:45.552+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:52:45.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:45.751+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:52:45.761+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:52:45.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:45.869+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:52:45.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:52:45.880+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:52:45.899+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.355 seconds
[2025-07-18T10:53:16.273+0000] {processor.py:186} INFO - Started process (PID=1674) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:53:16.274+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:53:16.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:16.276+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:53:16.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:16.484+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:16.493+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:53:16.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:16.592+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:16.602+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:16.602+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:53:16.625+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.359 seconds
[2025-07-18T10:53:46.909+0000] {processor.py:186} INFO - Started process (PID=1810) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:53:46.910+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:53:46.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:46.912+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:53:47.092+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:47.092+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:53:47.101+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:53:47.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:47.189+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:53:47.199+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:53:47.198+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:53:47.216+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.313 seconds
[2025-07-18T10:54:18.163+0000] {processor.py:186} INFO - Started process (PID=1941) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:54:18.164+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:54:18.167+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:18.167+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:54:18.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:18.374+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:18.382+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:54:18.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:18.497+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:18.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:18.507+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:54:18.525+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.368 seconds
[2025-07-18T10:54:48.905+0000] {processor.py:186} INFO - Started process (PID=2075) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:54:48.906+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:54:48.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:48.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:54:49.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:49.119+0000] {cost_tracking.py:152} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:54:49.128+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:54:49.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:49.237+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:54:49.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:54:49.249+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:54:49.269+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.370 seconds
[2025-07-18T10:55:19.829+0000] {processor.py:186} INFO - Started process (PID=2211) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:55:19.830+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:55:19.832+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:19.832+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:55:20.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:20.007+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:55:20.015+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:55:20.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:20.103+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:55:20.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:55:20.114+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:55:20.134+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.312 seconds
[2025-07-18T10:57:32.018+0000] {processor.py:186} INFO - Started process (PID=306) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:57:32.020+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:57:32.022+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:32.022+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:57:32.339+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:32.339+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:57:32.346+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:57:32.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:32.501+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:57:32.510+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:57:32.509+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:57:32.526+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.513 seconds
[2025-07-18T10:58:03.445+0000] {processor.py:186} INFO - Started process (PID=442) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:58:03.446+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:58:03.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.448+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:58:03.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.762+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:03.770+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:58:03.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.858+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:03.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:03.869+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:58:03.885+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.446 seconds
[2025-07-18T10:58:35.279+0000] {processor.py:186} INFO - Started process (PID=580) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:58:35.280+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:58:35.282+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:35.282+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:58:35.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:35.462+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:58:35.473+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:58:35.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:35.561+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:58:35.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:58:35.570+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:58:35.587+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.314 seconds
[2025-07-18T10:59:05.760+0000] {processor.py:186} INFO - Started process (PID=716) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:59:05.761+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:59:05.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.763+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:59:05.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:05.947+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:05.956+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:59:06.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:06.040+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:06.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:06.050+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:59:06.068+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.313 seconds
[2025-07-18T10:59:36.284+0000] {processor.py:186} INFO - Started process (PID=852) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:59:36.285+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T10:59:36.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:36.287+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:59:36.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:36.474+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T10:59:36.484+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T10:59:36.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:36.577+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T10:59:36.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T10:59:36.588+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T10:59:36.615+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.337 seconds
[2025-07-18T11:00:06.806+0000] {processor.py:186} INFO - Started process (PID=993) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:00:06.807+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:00:06.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:06.809+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:00:07.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:07.000+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:07.010+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:00:07.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:07.107+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:07.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:07.117+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:00:07.134+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.334 seconds
[2025-07-18T11:00:37.400+0000] {processor.py:186} INFO - Started process (PID=1127) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:00:37.401+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:00:37.403+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:37.403+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:00:37.591+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:37.591+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:00:37.600+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:00:37.697+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:37.696+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:00:37.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:00:37.707+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:00:37.726+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.331 seconds
[2025-07-18T11:01:08.105+0000] {processor.py:186} INFO - Started process (PID=1263) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:01:08.105+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:01:08.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:08.107+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:01:08.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:08.308+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:08.317+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:01:08.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:08.418+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:08.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:08.429+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:01:08.449+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.351 seconds
[2025-07-18T11:01:38.876+0000] {processor.py:186} INFO - Started process (PID=1394) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:01:38.877+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:01:38.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:38.878+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:01:39.084+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:39.084+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:01:39.093+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:01:39.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:39.204+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:01:39.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:01:39.215+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:01:39.233+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.363 seconds
[2025-07-18T11:02:10.925+0000] {processor.py:186} INFO - Started process (PID=1530) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:02:10.926+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:02:10.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:10.928+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:02:11.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:11.112+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:11.120+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:02:11.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:11.212+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:11.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:11.222+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:02:11.240+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.321 seconds
[2025-07-18T11:02:41.704+0000] {processor.py:186} INFO - Started process (PID=1671) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:02:41.705+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:02:41.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.707+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:02:41.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:41.904+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:02:41.912+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:02:42.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:42.008+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:02:42.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:02:42.019+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:02:42.040+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.342 seconds
[2025-07-18T11:03:12.366+0000] {processor.py:186} INFO - Started process (PID=1807) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:03:12.368+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:03:12.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:12.370+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:03:12.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:12.580+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:12.589+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:03:12.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:12.695+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:12.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:12.705+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:03:12.725+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.366 seconds
[2025-07-18T11:03:43.413+0000] {processor.py:186} INFO - Started process (PID=1943) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:03:43.414+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:03:43.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:43.416+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:03:43.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:43.632+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:03:43.640+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:03:43.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:43.739+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:03:43.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:03:43.749+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:03:43.768+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.361 seconds
[2025-07-18T11:04:14.355+0000] {processor.py:186} INFO - Started process (PID=2074) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:04:14.356+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:04:14.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:14.358+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:04:14.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:14.567+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:14.577+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:04:14.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:14.685+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:14.697+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:14.696+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:04:14.717+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.368 seconds
[2025-07-18T11:04:44.890+0000] {processor.py:186} INFO - Started process (PID=2212) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:04:44.891+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:04:44.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:44.893+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:04:45.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:45.088+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:04:45.098+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:04:45.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:45.196+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:04:45.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:04:45.206+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:04:45.226+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.346 seconds
[2025-07-18T11:05:15.337+0000] {processor.py:186} INFO - Started process (PID=2346) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:05:15.338+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:05:15.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.342+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:05:15.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.551+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:05:15.562+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:05:15.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.668+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:05:15.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:05:15.680+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:05:15.702+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.371 seconds
[2025-07-18T11:06:54.786+0000] {processor.py:186} INFO - Started process (PID=318) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:06:54.787+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:06:54.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:54.789+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:06:55.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:55.105+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:55.110+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:06:55.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:55.208+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:06:55.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:55.217+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:06:55.233+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.453 seconds
[2025-07-18T11:07:26.236+0000] {processor.py:186} INFO - Started process (PID=459) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:07:26.238+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:07:26.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:26.241+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:07:26.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:26.630+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:26.636+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:07:26.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:26.739+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:26.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:26.749+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:07:26.773+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.546 seconds
[2025-07-18T11:07:56.932+0000] {processor.py:186} INFO - Started process (PID=600) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:07:56.933+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:07:56.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:56.935+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:07:57.123+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:57.123+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:57.132+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:07:57.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:57.227+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:07:57.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:57.238+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:07:57.257+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.330 seconds
[2025-07-18T11:08:27.795+0000] {processor.py:186} INFO - Started process (PID=741) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:08:27.796+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:08:27.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:27.799+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:08:28.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:28.032+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:28.039+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:08:28.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:28.139+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:28.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:28.149+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:08:28.166+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.376 seconds
[2025-07-18T11:08:58.858+0000] {processor.py:186} INFO - Started process (PID=882) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:08:58.859+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:08:58.862+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:58.861+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:08:59.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:59.103+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:59.121+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:08:59.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:59.232+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:08:59.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:59.245+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:08:59.264+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.413 seconds
[2025-07-18T11:09:29.727+0000] {processor.py:186} INFO - Started process (PID=1025) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:09:29.728+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:09:29.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.731+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:09:29.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:29.944+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:29.954+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:09:30.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:30.050+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:09:30.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:30.060+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:09:30.080+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.361 seconds
[2025-07-18T11:10:00.831+0000] {processor.py:186} INFO - Started process (PID=1164) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:10:00.832+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:10:00.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:00.835+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:10:01.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:01.034+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:01.042+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:10:01.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:01.139+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:01.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:01.150+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:10:01.169+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.343 seconds
[2025-07-18T11:10:31.956+0000] {processor.py:186} INFO - Started process (PID=1307) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:10:31.958+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:10:31.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:31.960+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:10:32.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:32.156+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:32.165+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:10:32.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:32.276+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:10:32.289+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:32.288+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:10:32.308+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.359 seconds
[2025-07-18T11:11:02.391+0000] {processor.py:186} INFO - Started process (PID=1453) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:11:02.392+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:11:02.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:02.394+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:11:02.576+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:02.576+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:02.586+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:11:02.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:02.677+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:02.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:02.688+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:11:02.707+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.322 seconds
[2025-07-18T11:11:32.953+0000] {processor.py:186} INFO - Started process (PID=1592) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:11:32.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:11:32.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:32.957+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:11:33.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:33.183+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:33.195+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:11:33.338+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:33.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:11:33.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:33.352+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:11:33.382+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.435 seconds
[2025-07-18T11:12:03.705+0000] {processor.py:186} INFO - Started process (PID=1733) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:12:03.706+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:12:03.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.708+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:12:03.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.890+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:03.897+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:12:03.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.987+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:03.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:03.997+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:12:04.012+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.312 seconds
[2025-07-18T11:12:34.170+0000] {processor.py:186} INFO - Started process (PID=1869) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:12:34.171+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:12:34.173+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:34.172+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:12:34.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:34.359+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:34.367+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:12:34.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:34.461+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:12:34.470+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:34.470+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:12:34.486+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.322 seconds
[2025-07-18T11:13:06.188+0000] {processor.py:186} INFO - Started process (PID=2012) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:13:06.189+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:13:06.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:06.191+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:13:06.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:06.398+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:06.408+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:13:06.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:06.506+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:06.517+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:06.517+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:13:06.536+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.354 seconds
[2025-07-18T11:13:36.755+0000] {processor.py:186} INFO - Started process (PID=2151) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:13:36.756+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:13:36.758+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.758+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:13:36.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:36.959+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:36.969+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:13:37.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:37.065+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:13:37.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:37.074+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:13:37.091+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.341 seconds
[2025-07-18T11:14:08.604+0000] {processor.py:186} INFO - Started process (PID=2294) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:14:08.605+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:14:08.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:08.608+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:14:08.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:08.835+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:08.846+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:14:08.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:08.956+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:08.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:08.972+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:14:08.998+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.401 seconds
[2025-07-18T11:14:39.506+0000] {processor.py:186} INFO - Started process (PID=2433) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:14:39.507+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:14:39.510+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:39.510+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:14:39.745+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:39.745+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:39.755+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:14:39.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:39.871+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:14:39.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:39.888+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:14:39.915+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.415 seconds
[2025-07-18T11:15:10.163+0000] {processor.py:186} INFO - Started process (PID=2574) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:15:10.164+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:15:10.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:10.166+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:15:10.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:10.390+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:10.401+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:15:10.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:10.515+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:10.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:10.526+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:15:10.549+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.393 seconds
[2025-07-18T11:15:40.852+0000] {processor.py:186} INFO - Started process (PID=2715) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:15:40.853+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:15:40.855+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:40.855+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:15:41.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:41.082+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:41.092+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:15:41.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:41.202+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:15:41.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:41.213+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:15:41.234+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.388 seconds
[2025-07-18T11:16:11.646+0000] {processor.py:186} INFO - Started process (PID=2856) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:16:11.647+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:16:11.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:11.649+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:16:11.855+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:11.855+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:11.867+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:16:11.981+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:11.980+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:11.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:11.994+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:16:12.018+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.380 seconds
[2025-07-18T11:16:42.210+0000] {processor.py:186} INFO - Started process (PID=2997) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:16:42.212+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:16:42.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:42.214+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:16:42.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:42.423+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:42.434+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:16:42.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:42.545+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:16:42.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:42.557+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:16:42.580+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.375 seconds
[2025-07-18T11:17:12.971+0000] {processor.py:186} INFO - Started process (PID=3138) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:17:12.972+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:17:12.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:12.973+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:17:13.207+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:13.207+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:13.216+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:17:13.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:13.360+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:13.375+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:13.375+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:17:13.408+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.443 seconds
[2025-07-18T11:17:43.595+0000] {processor.py:186} INFO - Started process (PID=3279) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:17:43.596+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:17:43.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.598+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:17:43.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.855+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:43.866+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:17:43.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.983+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:17:44.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:43.999+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:17:44.020+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.430 seconds
[2025-07-18T11:18:14.520+0000] {processor.py:186} INFO - Started process (PID=3420) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:18:14.521+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:18:14.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.524+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:18:14.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.781+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:14.788+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:18:14.896+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.895+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:14.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:14.908+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:18:14.930+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.418 seconds
[2025-07-18T11:18:45.903+0000] {processor.py:186} INFO - Started process (PID=3561) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:18:45.904+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:18:45.907+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:45.906+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:18:46.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:46.148+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:46.158+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:18:46.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:46.277+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:18:46.289+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:46.289+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:18:46.311+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.417 seconds
[2025-07-18T11:19:17.070+0000] {processor.py:186} INFO - Started process (PID=3709) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:19:17.071+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:19:17.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:17.073+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:19:17.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:17.279+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:17.289+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:19:17.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:17.410+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:17.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:17.424+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:19:17.443+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.380 seconds
[2025-07-18T11:19:47.688+0000] {processor.py:186} INFO - Started process (PID=3855) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:19:47.689+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:19:47.692+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:47.692+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:19:47.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:47.904+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:47.914+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:19:48.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:48.016+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:19:48.029+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:48.029+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:19:48.189+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.508 seconds
[2025-07-18T11:20:18.564+0000] {processor.py:186} INFO - Started process (PID=3996) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:20:18.565+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:20:18.568+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:18.568+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:20:18.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:18.778+0000] {cost_tracking.py:151} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:20:18.789+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:20:18.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:18.898+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:20:19.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:19.102+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:20:19.119+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.561 seconds
[2025-07-18T12:38:11.725+0000] {processor.py:186} INFO - Started process (PID=307) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:38:11.727+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T12:38:11.729+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.729+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:38:11.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:11.815+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:11.823+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:38:12.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:12.061+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:perplexity_pipeline
[2025-07-18T12:38:12.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:12.070+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:perplexity_pipeline
[2025-07-18T12:38:12.076+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:12.076+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:perplexity_pipeline
[2025-07-18T12:38:12.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:12.083+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:perplexity_pipeline
[2025-07-18T12:38:12.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:12.090+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:perplexity_pipeline
[2025-07-18T12:38:12.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:12.096+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:perplexity_pipeline
[2025-07-18T12:38:12.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:12.102+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:perplexity_pipeline
[2025-07-18T12:38:12.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:12.102+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:12.113+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T12:38:12.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:12.113+0000] {dag.py:3262} INFO - Creating ORM DAG for perplexity_pipeline
[2025-07-18T12:38:12.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:12.114+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T12:38:12.127+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.408 seconds
[2025-07-18T12:38:42.307+0000] {processor.py:186} INFO - Started process (PID=443) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:38:42.308+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T12:38:42.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:42.310+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:38:42.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:42.515+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:38:42.523+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:38:42.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:42.621+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:38:42.630+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:38:42.630+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T12:38:42.650+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.350 seconds
[2025-07-18T12:39:13.057+0000] {processor.py:186} INFO - Started process (PID=579) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:39:13.058+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T12:39:13.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:13.060+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:39:13.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:13.144+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:13.157+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:39:13.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:13.288+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:13.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:13.301+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T12:39:13.326+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.277 seconds
[2025-07-18T12:39:43.940+0000] {processor.py:186} INFO - Started process (PID=715) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:39:43.941+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T12:39:43.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:43.943+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:39:44.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:44.009+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:39:44.018+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:39:44.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:44.109+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:39:44.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:39:44.120+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T12:39:44.159+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.224 seconds
[2025-07-18T12:40:15.009+0000] {processor.py:186} INFO - Started process (PID=852) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:40:15.010+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T12:40:15.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:15.012+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:40:15.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:15.093+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:15.104+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:40:15.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:15.195+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:15.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:15.205+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T12:40:15.235+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.232 seconds
[2025-07-18T12:40:45.496+0000] {processor.py:186} INFO - Started process (PID=988) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:40:45.497+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T12:40:45.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:45.499+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:40:45.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:45.569+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:40:45.577+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:40:45.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:45.665+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:40:45.674+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:40:45.673+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T12:40:45.692+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.202 seconds
[2025-07-18T12:41:16.111+0000] {processor.py:186} INFO - Started process (PID=1124) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:41:16.112+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T12:41:16.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:16.115+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:41:16.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:16.207+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T12:41:16.218+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T12:41:16.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:16.335+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T12:41:16.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T12:41:16.345+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T12:41:16.365+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.260 seconds
