[2025-07-18T11:06:48.382+0000] {processor.py:186} INFO - Started process (PID=189) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:06:48.383+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:06:48.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:48.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:06:48.867+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:06:48.867+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:06:48.868+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:06:48.879+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.503 seconds
[2025-07-18T11:07:19.547+0000] {processor.py:186} INFO - Started process (PID=330) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:07:19.548+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:07:19.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:19.551+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:07:19.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:19.943+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:19.944+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:07:19.956+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.418 seconds
[2025-07-18T11:07:50.125+0000] {processor.py:186} INFO - Started process (PID=471) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:07:50.126+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:07:50.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:50.129+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:07:50.467+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:07:50.467+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:07:50.468+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:07:50.480+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.362 seconds
[2025-07-18T11:08:20.588+0000] {processor.py:186} INFO - Started process (PID=612) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:08:20.589+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:08:20.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:20.592+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:08:20.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:20.768+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:20.769+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:08:20.785+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.204 seconds
[2025-07-18T11:08:51.491+0000] {processor.py:186} INFO - Started process (PID=753) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:08:51.492+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:08:51.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:51.494+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:08:51.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:08:51.672+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:08:51.673+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:08:51.689+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.204 seconds
[2025-07-18T11:09:21.737+0000] {processor.py:186} INFO - Started process (PID=894) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:09:21.738+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:09:21.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:21.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:09:21.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:21.898+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:21.899+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:09:21.913+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.182 seconds
[2025-07-18T11:09:52.606+0000] {processor.py:186} INFO - Started process (PID=1035) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:09:52.607+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:09:52.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:52.609+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:09:52.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:09:52.790+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:09:52.791+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:09:52.808+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.209 seconds
[2025-07-18T11:10:23.547+0000] {processor.py:186} INFO - Started process (PID=1182) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:10:23.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:10:23.550+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:23.550+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:10:23.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:23.754+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:23.755+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:10:23.774+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.233 seconds
[2025-07-18T11:10:54.564+0000] {processor.py:186} INFO - Started process (PID=1323) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:10:54.565+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:10:54.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:54.567+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:10:54.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:10:54.743+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:10:54.744+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:10:54.759+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.201 seconds
[2025-07-18T11:11:24.836+0000] {processor.py:186} INFO - Started process (PID=1464) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:11:24.838+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:11:24.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:24.840+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:11:25.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:25.019+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:25.020+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:11:25.037+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.207 seconds
[2025-07-18T11:11:55.182+0000] {processor.py:186} INFO - Started process (PID=1605) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:11:55.183+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:11:55.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:55.185+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:11:55.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:11:55.361+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:11:55.361+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:11:55.377+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.202 seconds
[2025-07-18T11:12:25.489+0000] {processor.py:186} INFO - Started process (PID=1746) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:12:25.490+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:12:25.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:25.492+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:12:25.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:25.663+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:25.664+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:12:25.677+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.194 seconds
[2025-07-18T11:12:56.073+0000] {processor.py:186} INFO - Started process (PID=1887) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:12:56.074+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:12:56.077+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:56.076+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:12:56.261+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:12:56.261+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:12:56.262+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:12:56.279+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.212 seconds
[2025-07-18T11:13:26.988+0000] {processor.py:186} INFO - Started process (PID=2028) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:13:26.989+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:13:26.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:26.991+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:13:27.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:27.164+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:27.165+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:13:27.182+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.199 seconds
[2025-07-18T11:13:57.590+0000] {processor.py:186} INFO - Started process (PID=2169) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:13:57.591+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:13:57.593+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:57.593+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:13:57.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:13:57.796+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:13:57.797+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:13:57.815+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.231 seconds
[2025-07-18T11:14:28.225+0000] {processor.py:186} INFO - Started process (PID=2310) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:14:28.226+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:14:28.228+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:28.228+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:14:28.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:28.415+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:28.416+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:14:28.433+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.214 seconds
[2025-07-18T11:14:59.419+0000] {processor.py:186} INFO - Started process (PID=2451) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:14:59.420+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:14:59.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:59.422+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:14:59.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:14:59.631+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:14:59.632+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:14:59.650+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.237 seconds
[2025-07-18T11:15:30.051+0000] {processor.py:186} INFO - Started process (PID=2592) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:15:30.053+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:15:30.056+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:30.055+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:15:30.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:15:30.281+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:15:30.282+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:15:30.301+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.258 seconds
[2025-07-18T11:16:00.721+0000] {processor.py:186} INFO - Started process (PID=2733) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:16:00.723+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:16:00.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:00.726+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:16:00.936+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:00.935+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:00.936+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:16:00.954+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.241 seconds
[2025-07-18T11:16:31.520+0000] {processor.py:186} INFO - Started process (PID=2874) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:16:31.521+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:16:31.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:31.523+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:16:31.730+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:16:31.730+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:16:31.730+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:16:31.749+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.236 seconds
[2025-07-18T11:17:02.241+0000] {processor.py:186} INFO - Started process (PID=3015) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:17:02.243+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:17:02.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:02.246+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:17:02.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:02.537+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:02.538+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:17:02.554+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.319 seconds
[2025-07-18T11:17:32.888+0000] {processor.py:186} INFO - Started process (PID=3156) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:17:32.889+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:17:32.891+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:32.891+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:17:33.084+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:17:33.084+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:17:33.085+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:17:33.102+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.221 seconds
[2025-07-18T11:18:03.507+0000] {processor.py:186} INFO - Started process (PID=3297) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:18:03.508+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:18:03.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:03.511+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:18:03.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:03.714+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:03.715+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:18:03.733+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.234 seconds
[2025-07-18T11:18:34.442+0000] {processor.py:186} INFO - Started process (PID=3438) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:18:34.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:18:34.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:34.445+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:18:34.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:18:34.686+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:18:34.687+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:18:34.706+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.271 seconds
[2025-07-18T11:19:04.776+0000] {processor.py:186} INFO - Started process (PID=3579) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:19:04.777+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:19:04.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:04.779+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:19:04.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:04.978+0000] {cost_tracking.py:157} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:19:04.979+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:19:04.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.224 seconds
[2025-07-18T11:19:15.446+0000] {processor.py:186} INFO - Started process (PID=3672) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:19:15.447+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:19:15.450+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.450+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:19:15.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:15.459+0000] {dagbag.py:366} INFO - File /opt/airflow/dags/cost_tracking.py assumed to contain no DAGs. Skipping.
[2025-07-18T11:19:15.460+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:19:15.478+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.040 seconds
[2025-07-18T11:19:21.323+0000] {processor.py:186} INFO - Started process (PID=3719) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:19:21.324+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:19:21.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:21.326+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:19:21.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:21.333+0000] {dagbag.py:366} INFO - File /opt/airflow/dags/cost_tracking.py assumed to contain no DAGs. Skipping.
[2025-07-18T11:19:21.333+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:19:21.346+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.030 seconds
[2025-07-18T11:19:51.792+0000] {processor.py:186} INFO - Started process (PID=3860) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:19:51.794+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:19:51.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:51.796+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:19:51.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:19:51.805+0000] {dagbag.py:366} INFO - File /opt/airflow/dags/cost_tracking.py assumed to contain no DAGs. Skipping.
[2025-07-18T11:19:51.806+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:19:51.822+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.037 seconds
[2025-07-18T11:20:22.258+0000] {processor.py:186} INFO - Started process (PID=4001) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:20:22.260+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:20:22.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:22.262+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:20:22.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:22.270+0000] {dagbag.py:366} INFO - File /opt/airflow/dags/cost_tracking.py assumed to contain no DAGs. Skipping.
[2025-07-18T11:20:22.271+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:20:22.286+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.033 seconds
[2025-07-18T11:20:27.397+0000] {processor.py:186} INFO - Started process (PID=4012) to work on /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:20:27.398+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking.py for tasks to queue
[2025-07-18T11:20:27.402+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:27.401+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:20:27.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:20:27.415+0000] {dagbag.py:366} INFO - File /opt/airflow/dags/cost_tracking.py assumed to contain no DAGs. Skipping.
[2025-07-18T11:20:27.416+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/cost_tracking.py
[2025-07-18T11:20:27.435+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking.py took 0.045 seconds
